<?php

/*
 * ==========================================================
 * AMWAL-CALLBACK.PHP
 * ==========================================================
 *
 * Process AMWAL Payment Gateway callbacks. © 2017-2025 board.support. All rights reserved.
 *
 */

header('Content-Type: application/json');
require('functions.php');

$raw = file_get_contents('php://input');
$request = json_decode($raw, true);

if ($request && isset($request['type']) && isset($request['data'])) {
    $type = $request['type'];
    $data = $request['data'];
    $metadata_json = sb_isset($request, 'metadata', '{}');
    $metadata = json_decode($metadata_json, true);

    if ($type === 'complete') {
        // Verify response integrity
        if (amwal_verify_response($data)) {
            $cloud_user_id = sb_isset($metadata, 'sb_user_id');
            $amount = floatval(sb_isset($data, 'amount', 0));
            $currency_id = sb_isset($data, 'currencyId');
            $transaction_id = sb_isset($data, 'transactionId');
            $customer_id = sb_isset($data, 'customerId');
            $response_code = sb_isset($data, 'responseCode');

            if ($response_code === '00' && $cloud_user_id && $amount > 0) {
                // Store customer ID for future recurring payments
                if ($customer_id) {
                    db_query('UPDATE users SET customer_id = "' . db_escape($customer_id) . '" WHERE id = ' . $cloud_user_id);
                }

                // Handle membership subscription
                if (isset($metadata['membership_id'])) {
                    $membership = membership_get($metadata['membership_id']);
                    if ($membership) {
                        membership_update($membership['id'], $membership['period'], $cloud_user_id, $transaction_id, sb_isset($metadata, 'referral'));
                        cloud_add_to_payment_history($cloud_user_id, $amount, 'membership', $transaction_id);

                        sb_cloud_debug('AMWAL Payment Success - Membership: ' . $membership['id'] . ', User: ' . $cloud_user_id . ', Amount: ' . $amount);
                        echo json_encode(['status' => 'success', 'message' => 'Membership updated']);
                    } else {
                        sb_cloud_debug('AMWAL Payment Error - Membership not found: ' . sb_isset($metadata, 'membership_id'));
                        echo json_encode(['status' => 'error', 'message' => 'Membership not found']);
                    }
                }
                // Handle credits purchase
                else if (isset($metadata['credits'])) {
                    membership_set_purchased_credits($amount, 'OMR', $cloud_user_id, $transaction_id);
                    sb_cloud_debug('AMWAL Payment Success - Credits: ' . $amount . ', User: ' . $cloud_user_id);
                    echo json_encode(['status' => 'success', 'message' => 'Credits added']);
                }
                // Handle white label purchase
                else if (isset($metadata['white_label'])) {
                    if ($amount == super_get_white_label()) {
                        membership_save_white_label($cloud_user_id);
                        cloud_add_to_payment_history($cloud_user_id, $amount, 'white-label', $transaction_id);
                        sb_cloud_debug('AMWAL Payment Success - White Label: User: ' . $cloud_user_id);
                        echo json_encode(['status' => 'success', 'message' => 'White label activated']);
                    } else {
                        sb_cloud_debug('AMWAL Payment Error - White label amount mismatch');
                        echo json_encode(['status' => 'error', 'message' => 'Amount mismatch']);
                    }
                }
            } else {
                sb_cloud_debug('AMWAL Payment Error - Invalid response code or data: ' . $response_code);
                echo json_encode(['status' => 'error', 'message' => 'Payment not successful']);
            }
        } else {
            sb_cloud_debug('AMWAL Payment Error - Response verification failed');
            echo json_encode(['status' => 'error', 'message' => 'Response verification failed']);
        }
    } else {
        sb_cloud_debug('AMWAL Callback - Unknown type: ' . $type);
        echo json_encode(['status' => 'error', 'message' => 'Unknown callback type']);
    }
} else {
    sb_cloud_debug('AMWAL Callback - Invalid request data');
    echo json_encode(['status' => 'error', 'message' => 'Invalid request']);
}

?>