/*

===================================================================
CLOUD MAIN CSS FILE
===================================================================

*/
/*
     
GLOBAL
==========================================================

*/
@keyframes pulse {
  0% {
    transform: scale(1); }
  50% {
    transform: scale(1.1); }
  100% {
    transform: scale(1); } }
@keyframes sb-fade-in {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
body {
  font-family: "Support Board Font", "Helvetica Neue", "Apple Color Emoji", Helvetica, Arial, sans-serif;
  min-height: 0; }

body.on-load * {
  transition: none !important; }

.disclaimer {
  text-align: center;
  max-width: 600px;
  display: block;
  margin: 0 auto 30px auto;
  font-size: 13px;
  line-height: 25px;
  color: #707070; }
  .disclaimer a {
    color: #028be5;
    text-decoration: none; }

.sb-admin {
  left: 0;
  padding: 0;
  width: 100%;
  background: #fff; }

hr {
  border: none;
  opacity: 0;
  height: 30px;
  margin: 0; }

hr.hr-border {
  border-bottom: 1px solid #d4d4d4;
  background: #FFF;
  opacity: 1;
  margin-bottom: 15px; }

.sb-lightbox {
  height: calc(100% - 100px); }

.sb-content .banner {
  position: relative;
  overflow: hidden;
  margin-bottom: 30px;
  border-radius: 4px;
  padding: 30px;
  background-color: #f5f7fa; }
  .sb-content .banner img {
    width: 170px;
    position: absolute;
    left: 0; }
  .sb-content .banner h2 {
    margin: 0 0 5px 0; }
    .sb-content .banner h2:empty {
      display: none; }
  .sb-content .banner p {
    margin: 5px 0 0 0 !important; }
  .sb-content .banner > i {
    position: absolute;
    right: 10px;
    top: 10px; }
    .sb-content .banner > i:before {
      font-size: 13px; }
  .sb-content .banner input {
    background: #fff; }
  .sb-content .banner a:not(.sb-btn) {
    cursor: pointer;
    font-weight: 500;
    text-decoration: underline; }
    .sb-content .banner a:not(.sb-btn):hover {
      text-decoration: none; }
  .sb-content .banner > div {
    padding-top: 30px; }
    .sb-content .banner > div:empty {
      display: none; }
  .sb-content .banner.banner-img {
    padding: 0 0 0 210px;
    height: 140px;
    background: none;
    border: none; }
    .sb-content .banner.banner-img h2 {
      font-size: 25px;
      margin: 15px 0 25px 0; }
    .sb-content .banner.banner-img p {
      font-size: 16px;
      line-height: 30px; }
    .sb-content .banner.banner-img > i {
      right: 0;
      top: 0; }
  .sb-content .banner.banner-error {
    background: rgba(202, 52, 52, 0.1); }
    .sb-content .banner.banner-error h2, .sb-content .banner.banner-error p, .sb-content .banner.banner-error i, .sb-content .banner.banner-error a {
      color: #ca3434; }
    .sb-content .banner.banner-error .sb-icon-close:hover {
      border-color: #ca3434;
      color: #ca3434;
      background: rgba(202, 52, 52, 0.1); }
  .sb-content .banner.banner-success {
    background: #e6f1ec; }
    .sb-content .banner.banner-success h2, .sb-content .banner.banner-success p, .sb-content .banner.banner-success i, .sb-content .banner.banner-success a {
      color: #1a9260; }
    .sb-content .banner.banner-success .sb-icon-close:hover {
      border-color: #1a9260;
      color: #1a9260;
      background: #e6f1ec; }

/*
     
REGISTRATION AND LOGIN
==========================================================

*/
.sb-admin-box .sb-top-bar img {
  margin-bottom: 20px; }
.sb-admin-box .sb-bottom {
  justify-content: flex-start; }
.sb-admin-box .sb-bottom div + div {
  margin-left: 15px; }
.sb-admin-box .sb-bottom div + .sb-btn-login-box, .sb-admin-box .sb-bottom div + .btn-registration-box, .sb-admin-box .btn-cancel-reset-password {
  margin-left: 5px;
  color: #028be5;
  cursor: pointer; }
.sb-admin-box .sb-top-bar img {
  max-width: 335px; }
.sb-admin-box .btn-forgot-password {
  margin: 5px 0 0 0 !important;
  text-align: right;
  font-size: 13px;
  cursor: pointer; }
.sb-admin-box .btn-forgot-password:hover {
  color: #ca3434;
  transition: all 0.4s; }

.sb-registration-box .loading-screen {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-radius: 6px;
  animation: sb-fade-animation 1s; }
  .sb-registration-box .loading-screen p {
    margin: 0;
    font-size: 15px;
    line-height: 25px;
    color: #566069; }
  .sb-registration-box .loading-screen i {
    width: 30px;
    height: 30px;
    margin-bottom: 15px; }

.sb-errors-area {
  color: #ca3434;
  margin: 30px 0 0 0;
  font-size: 15px;
  line-height: 25px; }
  .sb-errors-area:empty {
    display: none; }

.sb-registration-box:not(.active), .sb-login-box:not(.active), .sb-reset-password-box:not(.active), .sb-reset-password-box-2:not(.active), .loading-screen:not(.active) {
  display: none !important; }

/*
     
ACCOUNT
==========================================================

*/
.sb-account-box .sb-top-bar, .sb-super-box .sb-admin .sb-top-bar {
  margin: 0;
  padding: 0 15px;
  align-items: center;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #d4d4d4; }
  .sb-account-box .sb-top-bar h2, .sb-super-box .sb-admin .sb-top-bar h2 {
    display: flex;
    align-items: center;
    margin-right: 30px; }
    .sb-account-box .sb-top-bar h2 img, .sb-super-box .sb-admin .sb-top-bar h2 img {
      height: 35px;
      margin: 0 15px 0 0; }

.sb-account-box > .sb-tab {
  height: calc(100% - 70px); }

.sb-tab .sb-content {
  padding: 20px 15px 30px 20px; }
  .sb-tab .sb-content > div {
    max-width: 1000px; }
    .sb-tab .sb-content > div.sb-loading:before {
      left: 10px;
      top: 10px; }
  .sb-tab .sb-content textarea[readonly], .sb-tab .sb-content input[readonly] {
    background: #f5f7fa; }
  .sb-tab .sb-content h2 {
    font-weight: 600;
    font-size: 17px;
    margin: 0;
    color: #566069; }
    .sb-tab .sb-content h2 + p {
      margin-bottom: 30px; }
  .sb-tab .sb-content p {
    font-size: 14px;
    line-height: 23px;
    letter-spacing: 0.3px;
    margin: 5px 0 0 0;
    color: #788692; }
    .sb-tab .sb-content p a {
      color: #788692; }
  .sb-tab .sb-content .addons-title {
    border-bottom: 1px solid #dddddd;
    padding-bottom: 20px;
    margin: 60px 0 20px 0; }
  .sb-tab .sb-content #tab-installation .addons-title {
    border-bottom: none;
    padding-bottom: 0;
    border-top: 1px solid #dddddd;
    padding-top: 20px;
    margin: 30px 0 10px 0; }
    .sb-tab .sb-content #tab-installation .addons-title.first-title {
      border-top: none;
      padding-top: 0;
      margin-top: 0; }
  .sb-tab .sb-content .addons-title.first-title {
    margin-top: 0; }
  .sb-tab .sb-content #chart-usage {
    margin-top: 30px; }
  .sb-tab .sb-content .sb-table td a {
    line-height: 30px;
    font-weight: 500;
    display: block; }
  .sb-tab .sb-content .sb-table td:first-child {
    border-top: 1px solid #d4d4d4; }
  .sb-tab .sb-content #add-credits {
    margin-top: 20px; }
    .sb-tab .sb-content #add-credits select {
      background: #fff;
      min-height: 52px; }
    .sb-tab .sb-content #add-credits.sb-loading select {
      color: #fff; }
  .sb-tab .sb-content #credits-recharge {
    margin-top: 20px; }
    .sb-tab .sb-content #credits-recharge input {
      width: 52px;
      height: 52px; }
      .sb-tab .sb-content #credits-recharge input:before {
        line-height: 52px;
        font-size: 20px; }
  .sb-tab .sb-content .maso-box-credits .box-black {
    min-width: 260px; }
  .sb-tab .sb-content .box-membership .box-black {
    width: 50%; }

.box-split, .box-maso {
  display: flex;
  justify-content: space-between; }

.box-maso {
  justify-content: flex-start;
  margin: 0 -10px; }
  .box-maso > div {
    margin: 0 10px; }

.sb-tab .sb-content .box-black {
  background: #3b454d;
  padding: 20px 30px;
  border-radius: 4px; }
  .sb-tab .sb-content .box-black h2 {
    font-size: 17px;
    line-height: 20px;
    letter-spacing: .3px;
    color: #fff;
    margin: 0 0 15px 0;
    font-weight: 400; }
  .sb-tab .sb-content .box-black div {
    font-size: 25px;
    font-weight: 500;
    color: #fff; }
    .sb-tab .sb-content .box-black div span + span {
      font-size: 12px;
      letter-spacing: .3px;
      margin-left: 2px; }
  .sb-tab .sb-content .box-black > div {
    display: flex;
    align-items: baseline; }
    .sb-tab .sb-content .box-black > div > div + div {
      margin-left: 15px; }

.sb-btn-text {
  font-size: 15px; }

.sb-input .sb-btn {
  background-color: #fff;
  color: #566069;
  border: 1px solid #ccd2d5;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12);
  padding: 0 10px;
  white-space: nowrap;
  border-radius: 4px;
  height: 40px;
  line-height: 40px;
  margin-left: 15px; }
  .sb-input .sb-btn:hover {
    color: #fff;
    border-color: #028be5;
    background-color: #028be5; }
  .sb-input .sb-btn.sb-loading {
    width: 15px;
    background: #FFF !important;
    border-color: #ccd2d5;
    cursor: default; }
    .sb-input .sb-btn.sb-loading:before {
      color: #26435c; }

.sb-type-input-button input {
  min-width: 0;
  flex-shrink: 10; }

.animation-button {
  animation: pulse 0.5s ease-in-out infinite both; }

#tab-profile > .sb-flex {
  justify-content: space-between; }
  #tab-profile > .sb-flex #delete-account:hover {
    color: #ca3434; }

.sb-direct-link input {
  cursor: pointer; }
  .sb-direct-link input:hover {
    text-decoration: underline; }

.text-earnings {
  font-weight: 500;
  font-size: 17px;
  color: #566069;
  margin-top: 30px; }

/*
     
MEMBERSHIP
==========================================================

*/
.plans-box {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 20px;
  color: #3b454d; }
  .plans-box > div {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    transition: all .3s;
    cursor: pointer;
    position: relative;
    display: none; }
    .plans-box > div[data-active-membership]:not([data-expired]), .plans-box > div[data-active-membership]:not([data-expired]) p, .plans-box > div[data-active-membership]:not([data-expired]) h3, .plans-box > div[data-active-membership]:not([data-expired]) h4 {
      border-color: #1a9260;
      color: #1a9260;
      cursor: default; }
    .plans-box > div[data-expired], .plans-box > div[data-expired] p, .plans-box > div[data-expired] h3, .plans-box > div[data-expired] h4 {
      border-color: #ca3434 !important;
      color: #ca3434 !important; }
    .plans-box > div[data-active-membership], .plans-box > div[data-expired] {
      box-shadow: none !important; }
    .plans-box > div.sb-visible {
      display: block; }
    .plans-box > div:hover .active-membership-info {
      display: block;
      animation: sb-fade-in .3s; }
    .plans-box > div:hover, .plans-box > div.sb-active {
      border-color: #228be5;
      box-shadow: 0 0 5px rgba(39, 156, 255, 0.2); }
      .plans-box > div:hover h3, .plans-box > div:hover h4, .plans-box > div:hover p, .plans-box > div.sb-active h3, .plans-box > div.sb-active h4, .plans-box > div.sb-active p {
        color: #228be5; }
  .plans-box h3, .plans-box h4, .plans-box p {
    transition: all .3s; }
  .plans-box h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 500; }
  .plans-box h4 {
    margin: 0 0 15px 0;
    font-size: 22px;
    font-weight: 600;
    line-height: 35px; }
  .plans-box p {
    margin: 0;
    font-size: 15px;
    color: #6c7984;
    font-weight: 400; }

.active-membership-info {
  display: none;
  position: absolute;
  background: #1a9260;
  color: #fff;
  left: -1px;
  right: -1px;
  border-radius: 4px;
  top: -35px;
  line-height: 40px;
  padding: 0 20px;
  font-weight: 500;
  font-size: 14px;
  letter-spacing: .3px; }

[data-expired] .active-membership-info {
  background: #ca3434; }

.plans-box-menu {
  margin-bottom: 30px; }

#tab-invoices .sb-table td {
  display: flex;
  align-items: center; }
  #tab-invoices .sb-table td i {
    font-size: 20px;
    margin-right: 15px; }
#tab-invoices .sb-table tr:not(:last-child) td {
  border-bottom: none; }

.sb-invoice-row {
  display: flex;
  align-items: center; }
  .sb-invoice-row span {
    padding-right: 15px; }
    .sb-invoice-row span:first-child, .sb-invoice-row span + span + span {
      min-width: 150px; }

.sb-plan-active {
  cursor: default !important;
  color: #24272a;
  border-color: #1a9260 !important;
  box-shadow: none !important;
  position: relative; }
  .sb-plan-active * {
    color: #1a9260 !important; }
  .sb-plan-active:before {
    content: "\77";
    font-family: "Support Board Icons" !important;
    position: absolute;
    z-index: 2;
    right: 15px;
    color: #1a9260;
    font-size: 20px;
    line-height: 15px; }

/*
     
SUPER ADMIN
==========================================================

*/
#tab-customers {
  max-width: none; }

#tab-settings .sb-setting-content h2 {
  font-size: 16px; }

#tab-emails .sb-setting + .sb-setting, #tab-settings .sb-setting + .sb-setting {
  margin-top: 30px; }

#add-membership, #save-membership-plans {
  overflow: hidden; }

.table-customers {
  table-layout: auto; }
  .table-customers td:first-child {
    max-width: 60px; }
  .table-customers tr:last-child td {
    border-bottom: none; }
  .table-customers td {
    overflow: hidden;
    text-overflow: ellipsis; }

td[data-id="name"], th[data-field="name"] {
  max-width: 200px; }

.sb-profile-edit-box {
  height: calc(100% - 50px);
  max-width: 1000px; }
  .sb-profile-edit-box .sb-sales-box, .sb-profile-edit-box .sb-volume-box > div {
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis; }
  .sb-profile-edit-box .sb-readonly-box {
    margin-top: 15px; }
  .sb-profile-edit-box input[readonly] {
    background: none;
    color: #6c6f72; }
  .sb-profile-edit-box .sb-top-bar .sb-profile {
    padding-left: 0; }
  .sb-profile-edit-box .sb-input > span {
    width: 200px;
    min-width: 200px; }
  .sb-profile-edit-box .sb-delete-box {
    padding: 10px;
    border: 1px solid #ca3434;
    border-radius: 3px;
    margin-top: 30px; }
    .sb-profile-edit-box .sb-delete-box > div {
      display: flex;
      align-items: center; }
    .sb-profile-edit-box .sb-delete-box input {
      border: 1px solid #ca3434;
      border-radius: 3px;
      padding: 5px 10px; }
    .sb-profile-edit-box .sb-delete-box .sb-btn-text {
      margin-left: 30px;
      color: #ca3434; }
    .sb-profile-edit-box .sb-delete-box > p {
      font-size: 12px;
      line-height: 15px;
      margin-top: 15px;
      color: #ca3434; }

.sb-super-box > div > .sb-tab {
  height: calc(100% - 80px); }
.sb-super-box .input input + textarea {
  margin-top: 15px; }

[data-type="repeater"] .sb-btn {
  overflow: hidden; }

#membership-plans > div {
  margin-bottom: 40px;
  position: relative; }
  #membership-plans > div > div {
    display: flex;
    justify-content: space-between; }
  #membership-plans > div > i {
    position: absolute;
    right: -25px;
    top: 16px;
    font-size: 10px;
    cursor: pointer;
    opacity: 0;
    color: #bb0b0b; }
  #membership-plans > div:hover i {
    opacity: 1; }
  #membership-plans > div h5 {
    margin: 0 15px 0 0;
    font-size: 14px;
    white-space: nowrap; }
  #membership-plans > div input {
    width: 100%; }
    #membership-plans > div input[type=number] {
      width: 30%;
      min-width: 100px; }
    #membership-plans > div input + h5 {
      margin-left: 15px; }

.super-white-label {
  margin-top: 60px; }
  .super-white-label .sb-input {
    justify-content: flex-start;
    margin-bottom: 30px; }
    .super-white-label .sb-input h5 {
      margin-right: 30px;
      font-size: 14px; }
  .super-white-label input {
    display: inline-block;
    width: auto; }

.table-affiliates td {
  overflow: hidden;
  text-overflow: ellipsis; }

/*
     
RTL
==========================================================

*/
.sb-rtl .sb-admin {
  padding-left: 0;
  padding-right: 15px; }
.sb-rtl .sb-account-box .sb-top-bar h2 img, .sb-rtl .sb-super-box .sb-admin .sb-top-bar h2 img {
  margin: 0 0 0 15px; }
.sb-rtl .sb-account-box .sb-top-bar h2, .sb-rtl .sb-super-box .sb-admin .sb-top-bar h2 {
  margin-right: 0;
  margin-left: 30px; }
.sb-rtl .sb-content .banner > i {
  right: auto;
  left: 10px; }
.sb-rtl .sb-input .sb-btn {
  margin-left: 0;
  margin-right: 15px; }
.sb-rtl #membership-plans > div input + h5, .sb-rtl .sb-admin-box .sb-bottom div + div, .sb-rtl .sb-tab .sb-content .box-black > div > div + div {
  margin-left: 0;
  margin-right: 15px; }

.sb-nav > .sb-btn-text {
  position: absolute;
  left: 15px;
  bottom: 15px;
  font-size: 15px;
  font-weight: 600; }

/*
     
RESPONSIVE
==========================================================

*/
@media (max-width: 464px) {
  .sb-admin {
    width: auto;
    position: static; }

  .sb-account-box .sb-top-bar {
    padding: 0 15px 0 15px; }
  .sb-account-box > .sb-tab {
    display: block; }
    .sb-account-box > .sb-tab > .sb-content {
      overflow: visible;
      width: auto;
      padding: 20px 15px 30px 15px; }
    .sb-account-box > .sb-tab .sb-docs {
      display: none; }

  .sb-nav ul, .sb-menu-wide > ul {
    position: static !important;
    border: none !important;
    box-shadow: none !important; }
    .sb-nav ul li, .sb-menu-wide > ul li {
      padding: 10px 0 10px 0 !important; }

  .sb-content .banner {
    padding: 15px 40px 15px 15px; }

  .sb-input .sb-btn {
    margin: 15px 0 0 0; }

  .box-split, .box-maso {
    display: block; }

  .box-maso > div {
    margin: 10px; }

  .sb-tab .sb-content .box-membership .box-black {
    width: auto; }
  .sb-tab .sb-content .box-black div {
    line-height: 30px; }

  .plans-box {
    grid-template-columns: 1fr; }

  .sb-account-box .sb-top-bar h2, .sb-super-box .sb-admin .sb-top-bar h2 {
    width: 40px;
    overflow: hidden;
    text-indent: -9999px; }

  .sb-admin-box .sb-top-bar img {
    max-width: 100%; } }

/*# sourceMappingURL=skin.min.css.map */
