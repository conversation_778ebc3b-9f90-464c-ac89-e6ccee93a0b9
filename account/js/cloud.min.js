"use strict";!function(e){function t(t,i={},a=!1){e.extend(i,{function:t}),e.ajax({method:"POST",url:"ajax.php",data:i}).done(e=>{a&&a(!1!==e&&JSON.parse(e))})}function i(e,t,i){let a=new Date;a.setTime(a.getTime()+504e4*i),document.cookie=e+"="+t+";expires="+(0==i?"Thu, 01 Jan 1970 00:00:01 GMT":a.toUTCString())+";path=/;SameSite=None;Secure;"}function a(e,t){i("sb-cloud",e,3650),i("sb-login",t,3650)}function n(t){return!!e(t).hasClass("sb-loading")||(e(t).addClass("sb-loading"),!1)}function s(e,t,i="",a=!1,n=!1,s=!1){let o=function(e){e=e.trim(),e=e.toLowerCase();let t="åàáãäâèéëêìíïîòóöôùúüûñç·/_,:;";for(var i=0,a=t.length;i<a;i++)e=e.replace(new RegExp(t.charAt(i),"g"),"aaaaaaeeeeiiiioooouuuunc------".charAt(i));return e.replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,"").replace(/ /g,"")}(e);b.find(`#banner-${o}`).remove(),b.find(".sb-tab > .sb-content > .sb-active").prepend(`<div id="banner-${o}" class="banner${a?" banner-img":""}${n?" banner-error":""}${s?" banner-success":""}">${a?`<img src="${a}" />`:""}<h2>${c(e)}</h2><p>${c(t)}</p><div>${i}</div><i class="sb-btn-icon sb-icon sb-icon-close"></i></div>`)}function o(e){s("",e,"",!1,!1,!0),h()}function r(e){s("",e,"",!1,!0),h()}function l(e){switch(e){case"suspended":let t="quota_agents"in membership&&membership.count_agents>membership.quota_agents;(membership.count>membership.quota||membership.expired||t)&&s(SETTINGS.text_suspended_title?SETTINGS.text_suspended_title:"Your account has been suspended",(SETTINGS.text_suspended?SETTINGS.text_suspended:c("Your website visitors can still use the chat but you are not able to view the messages and reply to your visitors because you can not enter the administration area. Please renew your subscription below or upgrade to a higher plan to reactivate your account again."))+(t?" "+c("You can also delete newly created agents or admins and reactivate your account by clicking {R}.").replace("{R}",'<a id="delete-agents-quota">'+c("here")+"</a>"):""),"",!1,!0);break;case"verify":let i=f.find(".btn-verify-email").length,a=f.find(".btn-verify-phone").length,n=i&&a?"email and phone number":i?"email":"phone number";!i&&!a||S.includes("welcome")||s(`Verify your ${n}`,`Please verify your ${n} from the profile area.`,"",!1,!0)}}function d(e){return!0===e||1==e||"true"===e}function c(e){return SB_TRANSLATIONS&&e in SB_TRANSLATIONS?SB_TRANSLATIONS[e]:e}function u(e){return(e=e.replace(/_/g," ").replace(/-/g," ")).charAt(0).toUpperCase()+e.slice(1)}function p(){let e="";for(var t=5;t>0;--t)e+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[Math.floor(62*Math.random())];return e}function m(t,i){return!1===i||!0===i?(e(t).setClass("active",i),t):e(t).hasClass("active")}function h(){g.length?g.find(".sb-content.sb-scroll-area")[0].scrollTop=0:f.find("> .sb-tab > .sb-content")[0].scrollTop=0}let b,f,v,g,y,_,w,S=document.location.href,x=S,$=e(window).width()<465,k="razorpay"==PAYMENT_PROVIDER,L="stripe"==PAYMENT_PROVIDER,E="rapyd"==PAYMENT_PROVIDER,A="messages-agents"==MEMBERSHIP_TYPE,R={password_length:c("The password must be at least 8 characters long."),password_match:c("The passwords do not match."),email:c("The email address is not valid.")};e(document).ready(function(){if(b=e("body"),f=b.find(".sb-account-box"),v=!f.length&&b.find(".sb-registration-box"),g=b.find(".sb-super-box"),_=b.find(".sb-profile-edit-box"),y=b.find(".sb-loading-global"),b.removeClass("on-load"),b.on("click",".sb-nav li",function(){e(this).siblings().sbActive(!1),e(this).sbActive(!0),e(this).closest(".sb-tab").find("> .sb-content > div").sbActive(!1).eq(e(this).index()).sbActive(!0)}),b.on("click",".sb-lightbox .sb-close",function(){e(this).closest(".sb-lightbox").sbActive(!1),b.find(".sb-lightbox-overlay").sbActive(!1)}),b.on("click",".sb-lightbox .sb-info",function(){e(this).sbActive(!1)}),b.on("click",".banner > i",function(){e(this).parent().remove()}),S.includes("?")&&(x=S.substring(0,S.indexOf("?"))),S.includes("reload=true"))return f.startLoading(),void setTimeout(()=>{document.location=S.replace("&reload=true","").replace("?reload=true","")},2e3);if($&&(b.on("click",".sb-nav,.sb-menu-wide",function(){e(this).setClass("sb-active",!e(this).hasClass("sb-active"))}),b.on("click",".sb-nav li,.sb-menu-wide li",function(){e(this).parents().eq(1).find(" > div").html(e(this).html())})),f.length){let i,d,p=f.find("#chart-usage"),m=["installation","membership","invoices","profile"],b=f.find("#tab-profile .sb-input:not(#password)").map(function(){return e(this).attr("id")}).get(),v=f.find(".plans-box-menu");if(S.includes("#credits")&&setTimeout(()=>{f.find("#credits")[0].scrollIntoView()},500),t("account-user-details",{},e=>{w=e,f.find("#embed-code").val(`\x3c!-- ${BRAND_NAME} --\x3e\n<script id="chat-init" src="${CLOUD_URL}/account/js/init.js?id=${e.chat_id}"><\/script>`),f.stopLoading();for(t=0;t<b.length;t++)b[t]in e&&f.find(`#${b[t]} input`).val(e[b[t]]);for(var t=0;t<2;t++){let i=t?"email":"phone";1!=e[i+"_confirmed"]&&(t||TWILIO_SMS)||f.find("#"+i).removeClass("sb-type-input-button").find(".sb-btn").remove()}l("verify")}),v.length){let t=v.find("li").eq(0).sbActive(!0);f.find("#plans > div").removeClass("sb-visible"),f.find(`#plans > [data-menu="${t.attr("data-type")}"]`).addClass("sb-visible"),e(v).on("click","li",function(){v.find("li").sbActive(!1),f.find("#plans > div").removeClass("sb-visible"),f.find(`#plans > [data-menu="${e(this).attr("data-type")}"]`).addClass("sb-visible"),e(this).sbActive(!0)}),f.find("#membership-appsumo").length&&v.find("ul").append('<li><a href="https://appsumo.com/account/products/" target="_blank" style="color:#028be5;text-decoration:none">AppSumo</a></li>')}else f.find("#plans > div").addClass("sb-visible");if(f.on("click"," > .sb-tab > .sb-nav li",function(){let t=e(this).attr("id").replace("nav-","");"membership"==t&&(p.isLoading()&&e.getScript(CLOUD_URL+"/script/vendor/chart.min.js",()=>{p.stopLoading(),i=new Chart(p,{type:"bar",data:{labels:[c("January"),c("February"),c("March"),c("April"),c("May"),c("June"),c("July"),c("August"),c("September"),c("October"),c("November"),c("December")],datasets:[{data:messages_volume,backgroundColor:"#009BFC"}]},options:{legend:{display:!1}}})}),setTimeout(()=>{l("suspended")},300)),"profile"==t&&setTimeout(()=>{l("verify")},1e3),"logout"==t&&(SBF.logout(!1),setTimeout(()=>{document.location=location.href.substring(0,location.href.indexOf("/account"))+"?login"},300)),window.history.replaceState(null,null,"?tab="+t+(location.href.includes("debug")?"&debug":""))}),f.on("click",".btn-verify-email,.btn-verify-phone",function(){if(!e(this).parent().find("input").val()||n(this))return;let i={},a=e(this).hasClass("btn-verify-email");i[a?"email":"phone"]=e(this).parent().find("input").val(),t("verify",i,t=>{d=t,s("We sent you a secret code",`We sent you a secret code, please enter it below to verify your ${a?"email address":"phone number"}`,`<div data-type="text" class="sb-input sb-type-input-button"><input type="text"><a id="btn-verify-code" class="sb-btn">${c("Complete verification")}</a></div>`),e(this).stopLoading()})}),f.on("click",".banner #btn-verify-code",function(){let i=e(this).parent().find("input").val();i&&!n(this)&&t("verify",{code_pairs:[d,i]},t=>{if(t){let i="email"==t[0];a(t[1][0],t[1][1]);e(f).find(i?"#email":"#phone").removeClass("sb-type-input-button").find(".sb-btn").remove(),f.find(".banner").remove(),o(`Thank you! Your ${i?"email address":"phone number"} has been verified.`)}else r("Error. Something went wrong.");e(this).stopLoading()})}),f.on("click","#save-profile",function(){if(n(this))return;let i={},s=!1;f.find("#tab-profile .sb-input input").each((t,a)=>{let n=e(a).parent().attr("id"),o=e.trim(e(a).val());o||(r("All fields are required."),s=!0),"password"==n&&o.length<8&&(r(R.password_length),s=!0),"email"!=n||o.includes("@")&&o.includes(".")||(r(R.email),s=!0),i[n]=o}),s?e(this).stopLoading():t("account-save",{details:i},t=>{Array.isArray(t)?(a(t[0],t[1]),o("Your profile information has been updated successfully.")):r(t),e(this).stopLoading()})}),f.on("click","#nav-invoices",function(){let e=f.find("#tab-invoices");e.isLoading()&&t("get-payments",{},t=>{let i="";for(var a=0;a<t.length;a++)i+=`<tr><td><i class="sb-icon-file"></i>${function(e){let t=JSON.parse(e.value);return`<div class="sb-invoice-row" data-id="${e.id}"><span>INV-${t[5]}-${CLOUD_USER_ID}</span><span>${CLOUD_CURRENCY} ${t[0]}</span><span>${u(t[1])}</span><span>${new Date(1e3*t[5]).toLocaleString()}</span></div>`}(t[a])}</td></tr>`;i?e.find("tbody").html(i):e.append(`<p>${c(`There are no ${L?"invoices":"payments"} yet.`)}</p>`),e.stopLoading()})}),f.on("click",".sb-invoice-row",function(){t("get-invoice",{payment_id:e(this).attr("data-id")},e=>{window.open(CLOUD_URL+"/script/uploads/invoices/"+e),setTimeout(()=>{t("delete-invoice",{file_name:e})},1e3)})}),f.on("click","#plans > div",function(i){if(i.preventDefault(),e(this).attr("data-active-membership")&&!e(this).attr("data-expired")||n(this))return;if("manual"==PAYMENT_PROVIDER)return e(this).stopLoading(),document.location=PAYMENT_MANUAL_LINK;t("shopify"==external_integration?"shopify-subscription":{stripe:"stripe-create-session",rapyd:"rapyd-checkout",verifone:"verifone-checkout",razorpay:"razorpay-create-subscription",yoomoney:"yoomoney-create-subscription"}[PAYMENT_PROVIDER],{price_id:e(this).attr("data-id"),cloud_user_id:CLOUD_USER_ID},e=>{document.location=e.url})}),f.on("click","#purchase-white-label",function(i){if(i.preventDefault(),!e(this).hasClass("sb-plan-active")&&!n(this))return"manual"==PAYMENT_PROVIDER?(e(this).stopLoading(),document.location=PAYMENT_MANUAL_LINK):void t("purchase-white-label",{external_integration:external_integration},e=>{document.location=e.url})}),f.on("change","#add-credits select",function(i){let a=e(this).val();if(i.preventDefault(),a&&!n(e(this).parent()))return"manual"==PAYMENT_PROVIDER?(e(this).parent().stopLoading(),document.location=PAYMENT_MANUAL_LINK):void t("purchase-credits",{amount:a,external_integration:external_integration},e=>{e.error?console.error(e.error):document.location=e.url})}),f.on("click",".sb-custom-addon",function(i){n(this)||t("purchase-addon",{index:e(this).attr("data-index")},e=>{document.location=e.url})}),f.on("click","#credits-recharge input",function(i){t("set-auto-recharge-credits",{enabled:e(this).is(":checked")},e=>{o("Settings saved.")})}),f.on("click","#cancel-subscription",function(i){if(i.preventDefault(),confirm(c("Are you sure?"))){if(n(this))return;t((external_integration||PAYMENT_PROVIDER)+"-cancel-subscription",{},t=>{"no-subscriptions"==t?s("Subscription already cancelled","You do not have any active subscription.","",!1,!1,!0):t&&"canceled"==t.status?(s("Subscription cancelled","The subscription has ben cancelled sucessfully.","",!1,!1,!0),e(this).remove()):s("Error",JSON.stringify(t),"",!1,!0),e(this).stopLoading()})}}),S.includes("welcome")&&SETTINGS.text_welcome_title&&(s(SETTINGS.text_welcome_title,SETTINGS.text_welcome,"",SETTINGS.text_welcome_image),window.history.replaceState({},document.title,x),f.find(".sb-btn-dashboard").addClass("animation-button").attr("href","../?welcome").attr("target","_blank")),S.includes("tab="))for(var h=0;h<m.length;h++)if(S.includes("tab="+m[h])){let e=f.find(" > .sb-tab > .sb-nav");e.find("li").eq(h).click(),e.sbActive(!1);break}f.on("click","#delete-account",function(e){if(e.preventDefault(),confirm(c("Are you sure? Your account, along with all its users and conversations, will be deleted permanently."))){if(n(this))return;t("account-delete",{},()=>{SBF.cookie("sb-login","","",!1),SBF.cookie("sb-cloud","","",!1),SBF.storage("open-conversation",""),SBF.storage("login",""),setTimeout(()=>{location.reload()},500)})}}),f.on("click","#delete-agents-quota",function(){confirm(c("Are you sure?"))&&t("account-delete-agents-quota",{},()=>{location.href=CLOUD_URL})}),f.on("click","#save-payment-information",function(){t("save-referral-payment-information",{method:f.find("#payment_method").val(),details:f.find("#payment_information").val()},()=>{o("Settings saved.")})}),f.on("click","#nav-referral",function(){let e=f.find("#payment_method");e.attr("data-loaded")||t("get-referral-payment-information",{},t=>{t=t?t.split("|"):["",""],e.attr("data-loaded","true"),e.val(t[0]),f.find("#payment_information").val(t[1]),f.find("#payment_information_label").html(c("bank"==t[0]?"Bank details":"PayPal email"))})}),f.on("change","#payment_method",function(){f.find("#payment_information_label").html(c("bank"==e(this).val()?"Bank details":"PayPal email"))}),l("suspended")}if(v.length){let s=b.find(".sb-login-box"),o=b.find(".sb-reset-password-box");if(SBF.getURL("login_email")&&setTimeout(()=>{s.find("#email input").val(SBF.getURL("login_email")),s.find("#password input").val(SBF.getURL("login_password")),m(s,!0),m(v,!1),s.find(".btn-login").click()},300),e(v).on("click",".btn-register",function(s){if(n(this))return;let o={},r=!1,l=v.find(".sb-errors-area");return l.html(""),v.find("[id].sb-input").each(function(){let t=e(this).find("input");e.trim(t.val())?t.removeClass("sb-error"):(t.addClass("sb-error"),r=!0),o[e(this).attr("id")]=e.trim(t.val())}),r?l.html(c("All fields are required.")):o.password.length<8?(l.html(R.password_length),r=!0):o.password!=o.password_2?(l.html(R.password_match),r=!0):o.email.includes("@")&&o.email.includes(".")?(a("",""),S.includes("ref=")&&i("sb-referral",SBF.getURL("ref"),180),t("registration",{details:o},i=>{"duplicate-email"==i?l.html(c("This email is already in use. Please use another email.")):(a(i[0],i[1]),t("account-welcome"),setTimeout(()=>{document.location=SBF.getURL("redirect")?SBF.getURL("redirect"):CLOUD_URL+"/account?welcome"},300)),e(this).stopLoading()})):(l.html(R.email),r=!0),r&&e(this).stopLoading(),s.preventDefault(),!1}),e(s).on("click",".btn-login",function(i){let o=s.find("#email input").val(),r=s.find("#password input").val(),l=s.find(".sb-errors-area");if(o&&r&&!n(this))return l.html(""),t("login",{email:o,password:r},t=>{!1===t?l.html(c("Invalid email or password.")):"ip-ban"===t?l.html(c("Too many login attempts. Please retry again in a few hours.")):(a(t[0],t[1]),document.location=SBF.getURL("redirect")?SBF.getURL("redirect"):CLOUD_URL),e(this).stopLoading()}),i.preventDefault(),!1}),e(s).on("click",".btn-registration-box",function(){m(s,!1),m(v,!0)}),e(v).on("click",".sb-btn-login-box",function(){m(v,!1),m(s,!0)}),e(o).on("click",".btn-reset-password",function(){let i=e.trim(o.find("#reset-password-email").val());i&&i.includes("@")&&i.includes(".")&&(t("account-reset-password",{email:i}),o.html(`<div class="sb-top-bar"><div class="sb-title">${c("Check your email")}</div><div class="sb-text">${c("If an account linked to the email provided exists you will receive an email with a link to reset your password.")}</div></div>`))}),e(o).on("click",".btn-cancel-reset-password",function(){m(s,!0),m(o,!1)}),e(s).on("click",".btn-forgot-password",function(){m(v,!1),m(s,!1),m(o,!0)}),S.includes("reset=")){let i=b.find(".sb-reset-password-box-2"),a=i.find(".sb-info");e(i).on("click",".btn-reset-password-2",function(){let o=i.find("#reset-password-1").val();a.html("").sbActive(!1),o&&(o==i.find("#reset-password-2").val()?o.length<8?a.html(R.password_length).sbActive(!0):n(this)||t("account-reset-password",{email:SBF.getURL("email"),token:SBF.getURL("reset"),password:o},t=>{m(s,!0),m(i,!1),e(this).stopLoading()}):a.html(R.password_match).sbActive(!0))})}e(window).keydown(function(t){13==t.which&&e(".btn-login").click()})}g.length&&(g.find(".table-customers").length&&t("super-get-customers",{},e=>{let t="";for(var i=0;i<e.length;i++){let a=e[i];t+=`<tr data-customer-id="${a.id}"><td data-id="id">${a.id}</td><td data-id="name">${a.first_name} ${a.last_name}</td><td data-id="email">${a.email}</td><td data-id="phone">${a.phone}</td><td data-id="membership">${function(e){for(var t=0;t<MEMBERSHIPS.length;t++)if(MEMBERSHIPS[t].id==e)return MEMBERSHIPS[t];return MEMBERSHIPS[0]}(a.membership).name}</td><td data-id="token">${a.token}</td><td data-id="creation_time">${a.creation_time}</td></tr>`}g.find(".table-customers tbody").html(t),g.find("#tab-customers").stopLoading()}),e(g).on("click",".btn-login",function(a){let s=g.find("#email input").val(),o=g.find("#password input").val(),r=g.find(".sb-errors-area");if(s&&o&&!n(this))return t("super-login",{email:s,password:o},t=>{!1===t?r.html("Invalid email or password."):(i("sb-super",t,3650),document.location=x+"?login=success"),e(this).stopLoading()}),a.preventDefault(),!1}),e(g).on("click",".table-customers td",function(i){return y.sbActive(!0),t("super-get-customer",{customer_id:e(this).parent().attr("data-customer-id")},e=>{let t=["first_name","last_name","email","phone","password","credits"],i=["id","lifetime_value","token","creation_time","customer_id","database","count_users","count_agents","membership_expiration"],a="";for(n=0;n<t.length;n++){let i=t[n];a+=`<div data-type="text" class="sb-input"><span>${u(i)}</span><input id="${i}" type="text" value="${e[i]}" ${"phone"!=i?"required":""} /></div>`}for(n=0;n<e.extra_fields.length;n++){let t=e.extra_fields[n];["payment","active_membership_cache","notifications_credits_count","marketing_email_30","marketing_email_7","email_limit"].includes(t.slug)||(a+=`<div data-type="${"white-label"==t.slug?"select":"text"}" class="sb-input"><span>${u(t.slug)}</span>`,"white-label"==t.slug?a+=`<select id="white_label" data-extra="true"><option>${t.value}</option><option value="renew">Manual renewal</option><option value="disable">Disable</option></select></div>`:a+=`<input id="${t.slug}" type="text" value="${t.value}" data-extra="true" /></div>`)}a.includes("white_label")||(a+='<div data-type="select" class="sb-input"><span>White label</span><select id="white_label" data-extra="true"><option></option><option value="activate">Activate</option></select></div>'),a+='<div data-type="text" class="sb-input"><span>Membership</span><select id="membership" required>';for(n=0;n<MEMBERSHIPS.length;n++)a+=`<option value="${MEMBERSHIPS[n].id}"${MEMBERSHIPS[n].id==e.membership?" selected":""}>${MEMBERSHIPS[n].name}${MEMBERSHIPS[n].period?" | "+MEMBERSHIPS[n].period:""}</option>`;a+='<option value="manual_membership_renewal">Manual membership renewal</option></select></div>',_.find(".sb-edit-box").html(a),a="";for(n=0;n<i.length;n++)a+=`<div data-type="readonly" class="sb-input"><span>${u(i[n])}</span><input id="${i[n]}" type="text" value="${e[i[n]]}" readonly /></div>`;_.find(".sb-readonly-box").html(a),a="";for(n=0;n<e.invoices.length;n++)a+=function(e){return L||k?`${new Date(1e3*e.created).toISOString().slice(0,10)} | ${e.currency.toUpperCase()} ${e.amount_paid/(["BIF","CLP","DJF","GNF","JPY","KMF","KRW","MGA","PYG","RWF","UGX","VND","VUV","XAF","XOF","XPF"].includes(e.currency.toUpperCase())?1:100)} | ${e.number}<br>`:E?`${e.currency_code} ${e.amount} | ${new Date(1e3*e.paid_at).toISOString().slice(0,10)} | ${e.payment_method_type.replace(/_/g," ").toUpperCase()}<br>`:`${e.Currency.toUpperCase()} ${e.NetPrice} | ${e.OrderDate} | ${e.RefNo}<br>`}(e.invoices[n]);_.find(".sb-sales-box").html(a||"<div>No data available</div>"),a="";for(var n=0;n<e.monthly_volume.length;n++)a+=`<div>${e.monthly_volume[n].date} | ${e.monthly_volume[n].count} messages</div>`;_.find(".sb-volume-box").html(a||"<div>No data available</div>"),_.find(".sb-name").html(e.first_name+" "+e.last_name),_.find(".sb-delete-box input").val(""),_.attr("data-customer-id",e.id),_.lightbox()}),i.preventDefault(),!1}),e(_).on("click",".sb-save",function(i){if(n(this))return;let a={},s={},r=!1,l="free"==_.find("#membership").val();if(_.find("input:not([readonly]), select").each((t,i)=>{let n=e.trim(e(i).val()),o=e(i).attr("id");if(o){if(!(l||"membership_expiration"!=o||n&&8==n.length))return _.lightboxError("The membership expiration must be in the following format: dd-mm-yy (ex. 25-10-22)."),void(r=!0);if(!n&&e(i).attr("required"))return e(this).stopLoading(),e(_).find(".sb-info").html("All fields are required.").sbActive(!0),void(r=!0);e(i).attr("data-extra")?s[o]=n:a[o]=n}}),r)return void e(this).stopLoading();let d=e(this).closest("[data-customer-id]").attr("data-customer-id");return t("super-save-customer",{customer_id:d,details:a,extra_details:s},t=>{if(e(this).stopLoading(),"duplicate-phone-or-email"==t)return void e(_).find(".sb-info").html("Duplicated email or phone.").sbActive(!0);let i=g.find(`.table-customers [data-customer-id="${d}"]`),n=["name","email","phone","membership"];a.name=a.first_name+" "+a.last_name;for(s=0;s<MEMBERSHIPS.length;s++)if(a.membership==MEMBERSHIPS[s].id){a.membership=MEMBERSHIPS[s].name;break}for(var s=0;s<n.length;s++)i.find(`[data-id="${n[s]}"]`).html(a[n[s]]);o("Settings saved. Reload to apply the changes."),b.find(".sb-lightbox,.sb-lightbox-overlay").sbActive(!1)}),i.preventDefault(),!1}),e(_).on("click",".sb-delete-box .sb-btn-text",function(){if("DELETE"==e(this).parent().find("input").val().toUpperCase()){let i=e(this).closest("[data-customer-id]").attr("data-customer-id");t("super-delete-customer",{customer_id:i}),g.find(`.table-customers [data-customer-id="${i}"]`).remove(),b.find(".sb-lightbox,.sb-lightbox-overlay").sbActive(!1)}}),e(g).on("click","#save-emails, #save-settings",function(i){if(n(this))return;let a={},s="save-emails"==e(this).attr("id");return g.find(s?"#tab-emails":"#tab-settings").find(" .sb-setting textarea,.sb-setting input,.sb-setting select").each((t,i)=>{i=e(i),a[i.attr("id")]=i.is(":checkbox")?i.is(":checked"):e.trim(i.val())}),t(s?"super-save-emails":"super-save-settings",{settings:a},t=>{d(t)?o("Settings saved successfully."):r("Error:"+t),e(this).stopLoading()}),i.preventDefault(),!1}),e(b).on("click","#nav-emails, #nav-settings, #nav-membership-plans",function(){let i=e(this).attr("id"),a="nav-emails"==i,n="nav-settings"==i,s=e(b).find("#"+i.replace("nav","tab"));s.isLoading()&&t(a?"super-get-emails":n?"super-get-settings":"super-membership-plans",{},t=>{if(n||a)for(var i in t){let e=s.find("#"+i);e.is(":checkbox")?e.prop("checked","false"!=t[i]):e.val(t[i])}else s.append(t),L||s.find("[data-period]").each(function(){e(this).find("select").val(e(this).attr("data-period"))});s.stopLoading()})}),e(g).on("click","#save-membership-plans",function(){if(n(this))return;let i=e(this),a=[],s=!1;g.find("#membership-plans > div").each(function(){let t={id:e(this).attr("data-id"),price:L||k?e(this).attr("data-price"):e(this).find(".price").val(),currency:L||k?e(this).attr("data-currency"):CURRENCY,period:L||k?e(this).attr("data-period"):e(this).find(".period").val(),name:e(this).find(".name").val().trim(),quota:e(this).find(".quota").val().trim()};t.currency||(t.currency="usd"),A&&(t.quota_agents=e(this).find(".quota-agents").val().trim()),t.quota&&t.name&&e.isNumeric(t.quota)&&("free"==t.id||t.price&&t.period)?a.push(t):(r("All fields are required. Quota must be an integer."),i.stopLoading(),s=!0)}),!s&&window.confirm("Are you sure to update the membership plans? The changes will be live instantaneously.")?t("super-save-membership-plans",{plans:a},e=>{i.stopLoading(),d(e)?o("Membership plans saved successfully."):r("Error:"+e)}):i.stopLoading()}),e(g).on("click","#save-white-label",function(){n(this)||t("super-save-white-label",{price:g.find(".super-white-label input").val()},t=>{e(this).stopLoading(),d(t)?o("White label price saved successfully."):r("Error:"+t)})}),e(g).on("click","#logout",function(){i("sb-super","",0),document.location=x+"?logout=true"}),e(g).on("click","#membership-plans > div > i",function(){e(this).parent().remove()}),e(g).on("click","#add-membership",function(){e(g).find("#membership-plans").append(`<div data-id="${p()}" data-price="" data-period="" data-currency=""><div class="sb-input"><h5>Name</h5><input class="name" type="text" value="" placeholder="Insert plan name..."><h5>Quota</h5><input type="number" class="quota" placeholder="0" value="">${A?'<h5>Quota agents</h5><input type="number" class="quota-agents" placeholder="0" value="">':""}<h5>Price</h5><input type="number" class="price" placeholder="0" value=""><h5>Period</h5><select class="period"><option value="month">Monthly</option><option value="year">Yearly</option></select></div><i class="sb-icon-close"></i></div>`)}),e(g).on("click","#nav-affiliates",function(){let i=e(b).find("#tab-affiliates");i.isLoading()&&t("super-get-affiliates",{},e=>{let t="";for(var a=0;a<e.length;a++){let i=e[a];t+=`<tr><td>${i.id}</td><td>${i.first_name} ${i.last_name}</td ><td>${i.email}</td><td>${CURRENCY.toUpperCase()} <span>${i.value}</span></td><td><div class="sb-btn sb-btn-payment-details" data-id="${i.id}">Details</div></td><td><div class="sb-btn" data-id="${i.id}">Reset to zero</div></td></tr>`}i.find("tbody").html(t),i.stopLoading()})}),e(g).on("click",".table-affiliates .sb-btn",function(){let i={affiliate_id:e(this).attr("data-id")};e(this).hasClass("sb-btn-payment-details")?(y.sbActive(!0),t("super-get-affiliate-details",i,t=>{let i=b.find(".sb-generic-box");i.find(".sb-top-bar > div:first-child").html("Payment details of "+e(this).closest("tr").find("td").eq(1).html()),i.find(".sb-main").html(t[0]?"<b>Payment method</b><br>"+(t[0]?t[0].toUpperCase():"")+"</b><br><br><b>Payment details</b><br>"+t[1].replace("\n","<br>"):"The user has not provided the payment details yet."),i.lightbox()})):confirm("Are you sure?")&&t("super-reset-affiliate",i,()=>{e(this).closest("tr").remove()})}),e(g).on("click","#sb-get-emails",function(){let t="";g.find(".table-customers [data-customer-id]").each(function(){"Free"!=e(this).find('[data-id="membership"]').html().trim()&&(t+=e(this).find('[data-id="email"]').html().trim()+", ")}),e("#sb-response-area").html("<br>"+t.substring(0,t.length-2))}),e(g).on("click","#sb-btn-update-saas",function(){n(this)||t("super-update-saas",{},t=>{e(this).stopLoading(),d(t)?o("Update completed successfully."):r(t.includes("success-no-apps")?'The update was successfully completed, but it does not include the latest app updates. Unfortunately, your access to app updates has expired, as updates are only valid for one year. You can re-enable updates for another year by purchasing them <a href="https://shop.board.support/pay.php?checkout_id=custom-saas-apps-'+p()+"&price=199&external_reference="+t.substring(16)+'" target="_blank">here</a>.':"Error: "+t)})}))}),e.fn.lightbox=function(){e(this).css({"margin-top":e(this).outerHeight()/-2+"px","margin-left":e(this).outerWidth()/-2+"px"}),y.sbActive(!1),e(this).sbActive(!0),b.find(".sb-lightbox-overlay").sbActive(!0)},e.fn.lightboxError=function(t){let i=e(this).find(" > .sb-info");i.html(t).sbActive(!0),setTimeout(()=>{i.html("").sbActive(!1)},1e4)},e.fn.setClass=function(t,i=!0){return i?e(this).addClass(t):e(this).removeClass(t),this},e.fn.stopLoading=function(){return e(this).removeClass("sb-loading"),this},e.fn.startLoading=function(){return e(this).addClass("sb-loading"),this},e.fn.isLoading=function(){return e(this).hasClass("sb-loading")},e.fn.sbActive=function(t=-1){return-1===t?e(this).hasClass("sb-active"):(e(this).setClass("sb-active",t),this)}}(jQuery);