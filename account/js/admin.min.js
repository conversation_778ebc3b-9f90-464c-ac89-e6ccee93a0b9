!function(e){function t(t,i=!0){let n=a.find({google:"#google-client-id, #google-client-secret, #google-refresh-token","open-ai":"#open-ai-key","whatsapp-cloud":"#whatsapp-twilio-btn, #whatsapp-cloud-key",messenger:"#messenger-key, #messenger-path-btn"}[t]),s=a.find({"whatsapp-cloud":"#whatsapp-cloud-sync-btn, #whatsapp-cloud-reconnect-btn"}[t]);n.sbActive(i),s.sbActive(!i),i||n.each(function(){e(this).find("input").val("")})}function i(e){return SB_TRANSLATIONS&&e in SB_TRANSLATIONS?SB_TRANSLATIONS[e]:e}function n(t=!0){let i=t?{config_id:SB_CLOUD_WHATSAPP.configuration_id,response_type:"code",override_default_response_type:!0}:{config_id:SB_CLOUD_MESSENGER.configuration_id,response_type:"token"};FB.logout(),FB.login(function(i){if((i=!!i.authResponse&&i.authResponse)&&(t&&i.code||!t&&i.accessToken)){let n=a.find(t?"#whatsapp-cloud-sync-btn a":"#messenger-sync-btn a");if(SBAdmin.loading(n))return;!function(t,i={},n=!1){e.extend(i,{function:t}),e.ajax({method:"POST",url:"account/ajax.php",data:i}).done(e=>{n&&n(!1!==e&&JSON.parse(e))})}(t?"whatsapp-sync":"messenger-sync",{access_token:i.accessToken,code:i.code},i=>{if(n.sbLoading(!1),i&&(t&&i.access_token||!t&&Array.isArray(i)&&i.length)){let n=a.find(t?"#whatsapp-cloud-numbers":"#messenger-pages"),o=n.find(".repeater-item"),d=o.length,c=0;1!=d||o.eq(0).find("input").eq(0).val()||(d=0);let l=(t?i.phone_numbers.length:i.length)+d,p=n.find(t?"[data-id=whatsapp-cloud-numbers-phone-id]":"[data-id=messenger-page-id]").map(function(){return e(this).val()}).get();for(var s=d;s<l;s++){if(!p.includes(t?i.phone_numbers[c]:i[c].page_id)){s>=o.length&&(n.find(".sb-repeater-add").click(),o=n.find(".repeater-item"));let e=o.last();t?(e.find("[data-id=whatsapp-cloud-numbers-phone-id]").val(i.phone_numbers[c]),e.find("[data-id=whatsapp-cloud-numbers-token]").val(i.access_token),e.find("[data-id=whatsapp-cloud-numbers-account-id]").val(i.waba_id)):(e.find("[data-id=messenger-page-name]").val(i[c].name),e.find("[data-id=messenger-page-id]").val(i[c].page_id),e.find("[data-id=messenger-page-token]").val(i[c].access_token),e.find("[data-id=messenger-instagram-id]").val(i[c].instagram))}c++}SBAdmin.settings.save(),SBAdmin.infoBottom("Synchronization completed.")}else console.error(i)})}},i)}var a,s,o=!1,d={shopify_products_box:!1,shopify_products_box_ul:!1,removeAdminID:function(e){let t=e.indexOf(SB_ADMIN_SETTINGS.cloud.id);return-1!=t&&e.splice(t,1),e},creditsAlert:function(t,n){let s=e(t).closest("[id]").attr("id");return!!(SB_ADMIN_SETTINGS.credits<=0&&((s.includes("google")||s.includes("dialogflow"))&&"open-ai-spelling-correction-dialogflow"!=s&&"auto"==a.find("#google-sync-mode select").val()||s.includes("open-ai")&&"auto"==a.find("#open-ai-sync-mode select").val()))&&(SBAdmin.genericPanel("credits-panel","Credits required","<p>"+i("To use the {R} feature in automatic sync mode, credits are required. If you don't want to buy credits, switch to manual sync mode and use your own API key.").replace("{R}","<b>"+e(t).prev().html()+"</b>")+"</p>",[["Buy credits","plus"]]),SBAdmin.settings.input.reset(t),n.preventDefault(),!0)},creditsAlertQuota:function(){let e=a.find(".sb-docs").attr("href");SBAdmin.infoBottom(i("You have used all of your credits. Add more credits {R}.").replace("{R}",'<a href="account?tab=membership#credits">'+i("here")+"</a>")+(e?'<a href="'+e+'#cloud-credits" target="_blank" class="sb-icon-link"><i class="sb-icon-help"></i></a>':""),"error")},shopify:{conversationPanel:function(){let t="",n=SBF.activeUser().getExtra("shopify_id");this.panel||(this.panel=a.find(".sb-panel-shopify")),(n||SB_ADMIN_SETTINGS.shopify_shop&&SBF.activeUser().getExtra("current_url")&&SBF.activeUser().getExtra("current_url").value.includes(SB_ADMIN_SETTINGS.shopify_shop))&&!SBAdmin.loading(this.panel)?SBF.ajax({function:"shopify-get-conversation-details",shopify_id:!!n&&n.value},n=>{if(t=`<i class="sb-icon-refresh"></i><h3>Shopify</h3><div><div class="sb-split"><div><div class="sb-title">${i("Number of orders")}</div><span>${n.orders_count} ${i("orders")}</span></div><div><div class="sb-title">${i("Total spend")}</div><span>${n.total}</span></div></div><div class="sb-title">${i("Cart")}</div><div class="sb-list-items sb-list-links sb-shopify-cart">`,n.cart.items)for(a=0;a<n.cart.items.length;a++){let e=n.cart.items[a];t+=`<a href="${e.url}" target="_blank" data-id="${e.id}"><span>${e.quantity} x</span><span>${e.title}</span><span>${e.price}</span></a>`}if(t+=(n.cart.items&&n.cart.items.length?"":"<p>"+i("The cart is currently empty.")+"</p>")+"</div>",n.orders.length){t+=`<div class="sb-title">${i("Orders")}</div><div class="sb-list-items sb-shopify-orders sb-accordion">`;for(var a=0;a<n.orders.length;a++){let e=n.orders[a],o=e.id,d=e.items;t+=`<div data-id="${o}"><span><span>#${o}</span><span>${e.price}</span><span>${SBF.beautifyTime(e.date,!0)}</span><a href="${e.url}" target="_blank" class="sb-icon-next"></a></span><div>`;for(s=0;s<d.length;s++)t+=`<a data-product-id="${d[s].id}"><span>${d[s].quantity} x</span> <span>${d[s].name}</span></a>`;for(var s=0;s<2;s++){let n=0==s?"shipping":"billing";e[n+"_address"]&&(t+=`<div class="sb-title">${i((0==s?"Shipping":"Billing")+" address")}</div><div class="sb-multiline">${e[n+"_address"].replace(/\n/g,"<br>")}</div>`)}t+=`<span data-status="${e.status}">${e.status}</span></div></div>`}t+="</div>"}e(this.panel).html(t).sbLoading(!1),SBAdmin.collapse(this.panel,160)}):e(this.panel).html(t)}}};window.SBCloud=d,e(document).ready(function(){a=e(".sb-admin"),s=SB_URL.substring(0,SB_URL.substring(0,SB_URL.length-2).lastIndexOf("/")),"true"!=DISABLE_APPS||SB_CLOUD_MEMBERSHIP&&"0"!=SB_CLOUD_MEMBERSHIP&&"free"!=SB_CLOUD_MEMBERSHIP||(a.find("#tab-messenger,#tab-whatsapp,#tab-twitter,#tab-telegram,#tab-wechat,#tab-viber,#tab-line").hide(),a.find('[data-app="messenger"],[data-app="whatsapp"],[data-app="twitter"],[data-app="telegram"],[data-app="wechat"],[data-app="viber"],[data-app="line"],[data-app="zalo"]').addClass("sb-disabled")),SB_ADMIN_SETTINGS.shopify_shop&&(a.find(".sb-btn-saved-replies").after(`<div class="sb-btn-shopify" data-sb-tooltip="${i("Add Shopify product")}"></div>`),a.find(".sb-editor").append(`<div class="sb-popup sb-shopify-products"><div class="sb-header"><div class="sb-select"><p data-value="">${i("All")}</p><ul class="sb-scroll-area"></ul></div><div class="sb-search-btn"><i class="sb-icon sb-icon-search"></i><input type="text" placeholder="${i("Search ...")}" /></div></div><div class="sb-shopify-products-list sb-list-thumbs sb-scroll-area"><ul class="sb-loading"></ul></div><i class="sb-icon-close sb-popup-close"></i></div>`),d.shopify_products_box=a.find(".sb-shopify-products"),d.shopify_products_box_ul=d.shopify_products_box.find(" > div > ul")),e(document).on("SBSettingsLoaded",function(e,i){o=!0;let n=[["google","client-id"],["open-ai","key"],["whatsapp-cloud","key"]];for(var s=0;s<n.length;s++){let e=n[s][0];i[e]&&(i[e][0][e+"-sync-mode"]&&"manual"==i[e][0][e+"-sync-mode"][0]||i[e][0][e+"-"+n[s][1]][0])&&(t(e),a.find("#"+e+"-sync-mode select").val("manual"))}for(var d in SB_AUTO_SYNC)if(!SB_AUTO_SYNC[d]){let e=a.find("#"+d+"-sync-mode");e.find("select").val("manual"),e.addClass("sb-hide"),t(d,!0)}}),e(a).on("change","#google-sync-mode select, #open-ai-sync-mode select, #whatsapp-cloud-sync-mode select",function(){t(e(this).parent().attr("id").replace("-sync-mode",""),"manual"==e(this).val()),SBAdmin.infoBottom("Save changes to apply new sync mode.","info")}),e(a).on("click","#open-ai-active input, #open-ai-spelling-correction input, #open-ai-spelling-correction-dialogflow input, #open-ai-rewrite input, #open-ai-speech-recognition input, #sb-train-chatbot, #dialogflow-sync-btn .sb-btn, #dialogflow-active input, #google-multilingual input, #google-multilingual-translation input, #google-translation input, #google-language-detection input",function(e){if(d.creditsAlert(this,e))return!1}),e(a).on("change","#open-ai-mode select",function(){"assistant"==e(this).val()&&(a.find("#open-ai-sync-mode select").val("manual"),a.find("#open-ai-key").sbActive(!0))}),e(a).on("change","#open-ai-sync-mode select",function(){if("auto"==e(this).val()){let e=a.find("#open-ai-mode select");"assistant"==e.val()&&e.val(""),a.find("#open-ai-assistant-id").sbActive(!1)}}),SB_ADMIN_SETTINGS.credits_required&&d.creditsAlertQuota();let c=!1;e(a).on("click","#whatsapp-cloud-sync-btn .sb-btn, #whatsapp-cloud-reconnect-btn .sb-btn, #messenger-sync-btn a",function(t){let i=e(this).parent().attr("id"),a="messenger-sync-btn"!=i,s="whatsapp-cloud-reconnect-btn"==i&&{scope:"whatsapp_business_messaging, whatsapp_business_management, business_management"};return c?s?FB.login(()=>{},s):n(a):(window.fbAsyncInit=function(){FB.init(a?{appId:SB_CLOUD_WHATSAPP.app_id,autoLogAppEvents:!0,xfbml:!0,version:"v18.0"}:{appId:SB_CLOUD_MESSENGER.app_id,cookie:!0,xfbml:!0,version:"v18.0"})},e.getScript("https://connect.facebook.net/en_US/sdk.js",()=>{c=!0,s?FB.login(()=>{},s):n(a)})),t.preventDefault(),!1});let l=document.location.href;if((l.includes("board.support")||l.includes("support-board"))&&l.includes("welcome")){a.find("#sb-settings").click();let t=[["Need help?","Not sure what to do? Contact us for help. Log in with your existing email and password. We reply in a few hours.",[],"","Contact us","https://board.support/docs/support"],["Notifications","Activate Push and Email notifications to receive alerts for incoming messages. On iPhone, the mobile app is required.",["t-qzDPG88Xg","enb291Aai5Q"],"#notifications","Activate"],["Chatbot","Activate the OpenAI chatbot and Human Takeover to transfer the chat to an agent when needed.",["0p2YWQtsglg"],"#optimal-configuration-ai","Activate"],["Try out the chat","Try out the chat and send a test message to test notifications and the chatbot functionalities.",["mxjRevd_8bw"],"#widget-hidden","Try now","https://chat.cloud.board.support/"+SB_ADMIN_SETTINGS.cloud.chat_id],["Mobile app","The admin area is a PWA that can be installed on iPhones, Android, and mobile devices.",["IhoAlXFywFY"],"#pwa","Read more","https://board.support/docs#pwa?cloud"]],i="<div>",n=["push-notifications-active","open-ai-active"];for(var p=0;p<t.length;p++){let e=`id="onboarding-${t[p][3].replace("#","")}"`,n="";for(var r=0;r<t[p][2].length;r++)n+=`<a href="https://www.youtube.com/watch?v=${t[p][2][r]}" target="_blank"><img src="account/media/play-video.svg"></a>`;i+=`<div class="sb-setting"><div><h2>${t[p][0]} ${n}<a href="https://board.support/docs/${t[p][3]}" target="_blank"><i class="sb-icon-help"></i></a></h2><p>${t[p][1]}</p></div><div>${t[p][5]?`<a ${e} href="${t[p][5]}" target="_blank" class="sb-btn">${t[p][4]}</a>`:`<div ${e} class="sb-btn">${t[p][4]}</div>`}</div></div>`}setTimeout(()=>{SBAdmin.genericPanel("onboarding",`Welcome ${a.find("> .sb-header > .sb-admin-nav-right .sb-account .sb-name").html()} 👋`,"<p>Support Board is a powerful tool with many options. Let's start by activating the basic functionalities below. Don't hesitate to reach out to us if you have any questions.</p>"+i+"</div>",[],"",!0);for(var t=0;t<n.length;t++)a.find("#"+n[t]+" input").prop("checked")&&e(".sb-onboarding-box [id].sb-btn").eq(t).sbActive(!0);a.find(".sb-admin-list .sb-scroll-area > ul li").length&&e("#onboarding-widget-hidden").sbActive(!0)},1e3)}e(a).on("click","#onboarding-notifications",function(){if(e(this).sbActive()||SBAdmin.loading(this))return;let t=["notify-agent-email","notify-user-email","push-notifications-active"];for(var i=0;i<t.length;i++)a.find("#"+t[i]+" input").prop("checked",!0);"undefined"!=typeof OneSignal?OneSignal.Slidedown.promptPush({force:!0}):SBF.serviceWorker.initPushNotifications(),e(document).on("SBPushNotificationSubscription",(t,i)=>{e(this).sbLoading(!1),i.optedIn&&e(this).sbActive(!0)}),SBAdmin.settings.save()}),e(a).on("click","#onboarding-optimal-configuration-ai",function(){a.find("#open-ai-active input").prop("checked",!0),a.find("#dialogflow-human-takeover-active input").prop("checked",!0),a.find("#dialogflow-human-takeover-message textarea").val("I'm a chatbot. Do you want to get in touch with one of our agents?"),a.find("#dialogflow-human-takeover-message-confirmation textarea").val("Alright! We will get in touch soon!"),a.find("#dialogflow-human-takeover-confirm input").val("Yes"),a.find("#dialogflow-human-takeover-cancel input").val("Cancel"),SBAdmin.settings.save(),e(this).sbActive(!0)}),e(a).on("click","#onboarding-widget-hidden",function(){e(this).sbActive()||SBAdmin.loading(this)||e(document).on("SBAdminNewConversation",()=>{e(this).sbLoading(!1),e(this).sbActive(!0)})}),e(a).on("click","#onboarding-pwa",function(){e(this).sbActive(!0)}),e(a).on("click","[data-product-id]:not([href])",function(){SBF.ajax({function:"shopify-get-product-link",product_id:e(this).attr("data-product-id")},t=>{e(this).attr("href",t).attr("target","_blank"),window.open(t,"_blank")})}),e(a).on("click",".sb-panel-shopify > i",function(){d.shopify.conversationPanel()}),e(a).on("click",".sb-btn-shopify",function(){(d.shopify_products_box_ul.sbLoading()||SB_ADMIN_SETTINGS.languages&&SBF.activeUser()&&SB_ADMIN_SETTINGS.languages.includes(activeUser().language)&&SBF.activeUser().language!=SBAdmin.apps.itemsPanel.panel_language)&&SBAdmin.apps.itemsPanel.populate("shopify"),d.shopify_products_box.find(".sb-search-btn").sbActive(!0).find("input").get(0).focus(),d.shopify_products_box.sbTogglePopup(this)}),e(d.shopify_products_box).find(".sb-shopify-products-list").on("scroll",function(){(function(t,i=!1,n=0){if(i)return e(t).scrollTop()+e(t).innerHeight()>=e(t)[0].scrollHeight-1;e(t).scrollTop(e(t)[0].scrollHeight-n)})(this,!0)&&SBAdmin.apps.itemsPanel.pagination(this,"shopify")}),e(d.shopify_products_box).on("click",".sb-select li",function(){SBAdmin.apps.itemsPanel.filter(this,"shopify")}),e(d.shopify_products_box).on("input",".sb-search-btn input",function(){SBAdmin.apps.itemsPanel.search(this,"shopify")}),e(d.shopify_products_box).on("click",".sb-search-btn i",function(){SBF.searchClear(this,()=>{SBAdmin.apps.itemsPanel.search(e(this).next(),"shopify")})}),e(d.shopify_products_box).on("click",".sb-shopify-products-list li",function(){SBChat.insertText(`{shopify product_id="${e(this).data("id")}"}`),SBF.deactivateAll(),a.removeClass("sb-popup-active")}),e(a).on("click",'.sb-admin-nav-right [data-value="account"], #sb-buy-credits',function(){document.location=s+"/account?tab=membership"+("sb-buy-credits"==e(this).attr("id")?"#credits":"")}),e(a).on("click",".sb-btn-app-disable",function(){SBAdmin.loading(this)||SBF.ajax({function:"app-disable",app_name:e(this).closest("[data-app]").attr("data-app")},e=>{location.reload()})})})}(jQuery);