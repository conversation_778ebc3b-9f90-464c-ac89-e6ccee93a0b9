<?php

/*
 * ==========================================================
 * AMWAL-CHECKOUT.PHP
 * ==========================================================
 *
 * AMWAL Payment Gateway Checkout Page. © 2017-2025 board.support. All rights reserved.
 *
 */

session_start();
require('functions.php');

// Get payment configuration from session
$payment_config = sb_isset($_SESSION, 'amwal_payment_config');
$merchant_reference = sb_isset($_GET, 'ref');

if (!$payment_config || !$merchant_reference) {
    header('Location: ' . CLOUD_URL . '/account?tab=membership&error=invalid_payment');
    exit;
}

// Verify merchant reference matches
if ($payment_config['MerchantReference'] !== $merchant_reference) {
    header('Location: ' . CLOUD_URL . '/account?tab=membership&error=invalid_reference');
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AMWAL Payment - Support Board</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .payment-container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .logo {
            margin-bottom: 30px;
        }
        .amount {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin: 20px 0;
        }
        .currency {
            color: #7f8c8d;
            font-size: 18px;
        }
        .pay-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            margin: 30px 0;
            transition: background 0.3s;
        }
        .pay-button:hover {
            background: #2980b9;
        }
        .pay-button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .loading {
            display: none;
            margin: 20px 0;
        }
        .error {
            color: #e74c3c;
            margin: 20px 0;
            padding: 15px;
            background: #fdf2f2;
            border-radius: 6px;
            border: 1px solid #fecaca;
        }
        .reference {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="logo">
            <h2>Support Board Payment</h2>
        </div>
        
        <div class="amount">
            <?php echo number_format(floatval($payment_config['AmountTrxn']), 3); ?>
            <span class="currency">OMR</span>
        </div>
        
        <div class="reference">
            Reference: <?php echo htmlspecialchars($payment_config['MerchantReference']); ?>
        </div>
        
        <button id="payButton" class="pay-button">Pay Now</button>
        
        <div id="loading" class="loading">
            Processing payment...
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <!-- Load AMWAL SmartBox JavaScript -->
    <script src="<?php echo AMWAL_SMARTBOX_JS_URL; ?>"></script>
    
    <script>
        // Configure AMWAL SmartBox
        SmartBox.Checkout.configure = {
            MID: <?php echo $payment_config['MID']; ?>,
            TID: <?php echo $payment_config['TID']; ?>,
            CurrencyId: <?php echo $payment_config['CurrencyId']; ?>,
            AmountTrxn: <?php echo $payment_config['AmountTrxn']; ?>,
            MerchantReference: "<?php echo $payment_config['MerchantReference']; ?>",
            LanguageId: "<?php echo $payment_config['LanguageId']; ?>",
            PaymentViewType: <?php echo $payment_config['PaymentViewType']; ?>,
            TrxDateTime: "<?php echo $payment_config['TrxDateTime']; ?>",
            SessionToken: "<?php echo $payment_config['SessionToken']; ?>",
            SecureHash: "<?php echo $payment_config['SecureHash']; ?>",
            
            completeCallback: function (data) {
                console.log("Payment completed:", data);
                
                // Store customer ID if provided for future recurring payments
                if (data.customerId) {
                    // Send customer ID to backend for storage
                    fetch('<?php echo CLOUD_URL; ?>/account/amwal-callback.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            type: 'complete',
                            data: data,
                            metadata: <?php echo json_encode($payment_config['metadata']); ?>
                        })
                    }).then(() => {
                        // Redirect to success page
                        window.location.href = '<?php echo CLOUD_URL; ?>/account?tab=membership&payment=success';
                    });
                } else {
                    window.location.href = '<?php echo CLOUD_URL; ?>/account?tab=membership&payment=success';
                }
            },
            
            errorCallback: function (data) {
                console.log("Payment error:", data);
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').textContent = 'Payment failed: ' + (data.message || 'Unknown error');
                document.getElementById('payButton').disabled = false;
                document.getElementById('loading').style.display = 'none';
            },
            
            cancelCallback: function (data) {
                console.log("Payment cancelled:", data);
                window.location.href = '<?php echo CLOUD_URL; ?>/account?tab=membership&payment=cancelled';
            }
        };

        // Handle pay button click
        document.getElementById("payButton").onclick = function() {
            document.getElementById('payButton').disabled = true;
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            
            try {
                SmartBox.Checkout.showSmartBox();
            } catch (error) {
                console.error('SmartBox error:', error);
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').textContent = 'Failed to initialize payment: ' + error.message;
                document.getElementById('payButton').disabled = false;
                document.getElementById('loading').style.display = 'none';
            }
        };
    </script>
</body>
</html>

<?php
// Clear payment config from session after use
unset($_SESSION['amwal_payment_config']);
?>
