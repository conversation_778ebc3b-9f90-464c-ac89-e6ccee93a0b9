<?php
// Test Support Board database connection
require_once('script/config.php');
require_once('script/include/functions.php');

echo "Testing Support Board database connection...\n";
echo "SB_DB_HOST: " . (defined('SB_DB_HOST') ? SB_DB_HOST : 'NOT DEFINED') . "\n";
echo "SB_DB_NAME: " . (defined('SB_DB_NAME') ? SB_DB_NAME : 'NOT DEFINED') . "\n";
echo "SB_DB_USER: " . (defined('SB_DB_USER') ? SB_DB_USER : 'NOT DEFINED') . "\n";

// Test Support Board connection
$connection_check = sb_db_check_connection();
if ($connection_check === true) {
    echo "✅ Support Board database connection successful!\n";
    
    // Test a query
    $users = sb_db_get("SELECT COUNT(*) as count FROM users");
    if ($users && isset($users['count'])) {
        echo "✅ Support Board query successful! Users count: " . $users['count'] . "\n";
    } else {
        echo "❌ Support Board query failed\n";
    }
} else {
    echo "❌ Support Board database connection failed: " . $connection_check . "\n";
}

// Test Cloud connection
echo "\nTesting Cloud database connection...\n";
echo "CLOUD_DB_HOST: " . (defined('CLOUD_DB_HOST') ? CLOUD_DB_HOST : 'NOT DEFINED') . "\n";
echo "CLOUD_DB_NAME: " . (defined('CLOUD_DB_NAME') ? CLOUD_DB_NAME : 'NOT DEFINED') . "\n";
echo "CLOUD_DB_USER: " . (defined('CLOUD_DB_USER') ? CLOUD_DB_USER : 'NOT DEFINED') . "\n";

require_once('account/functions.php');
if (db_connect()) {
    echo "✅ Cloud database connection successful!\n";
    
    $users = db_get("SELECT COUNT(*) as count FROM users");
    if ($users && isset($users['count'])) {
        echo "✅ Cloud query successful! Users count: " . $users['count'] . "\n";
    } else {
        echo "❌ Cloud query failed\n";
    }
} else {
    echo "❌ Cloud database connection failed\n";
}
?>
