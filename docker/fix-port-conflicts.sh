#!/bin/bash

# Support Board SaaS - Port Conflict Fix Script
# This script helps identify and resolve port conflicts

echo "🔍 Checking for port conflicts..."

# Function to check what's using a port
check_port_usage() {
    local port=$1
    local service_name=$2
    
    echo "Checking port $port ($service_name)..."
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "❌ Port $port is in use by:"
        lsof -Pi :$port -sTCP:LISTEN
        echo ""
        return 0
    else
        echo "✅ Port $port is available"
        return 1
    fi
}

# Check common ports
echo "=== Port Conflict Check ==="
check_port_usage 8080 "Web Server"
check_port_usage 3306 "MySQL"
check_port_usage 3307 "MySQL Alternative"
check_port_usage 8081 "phpMyAdmin"

echo ""
echo "=== Solutions ==="

# Check for MySQL
if lsof -Pi :3306 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "🔧 MySQL is running on port 3306. Options:"
    echo "   1. Stop local MySQL: sudo brew services stop mysql"
    echo "   2. Or use alternative port (already configured): 3307"
    echo ""
fi

# Check for Apache/Nginx
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "🔧 Web server is running on port 8080. Options:"
    echo "   1. Stop local web server"
    echo "   2. Or use alternative setup script: ./docker/setup-alternative-ports.sh"
    echo ""
fi

# Check for phpMyAdmin
if lsof -Pi :8081 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "🔧 Service is running on port 8081. Options:"
    echo "   1. Stop the conflicting service"
    echo "   2. Or use alternative setup script: ./docker/setup-alternative-ports.sh"
    echo ""
fi

echo "=== Recommended Actions ==="
echo "1. 🛑 Stop conflicting services:"
echo "   sudo brew services stop mysql"
echo "   sudo brew services stop apache2"
echo "   sudo brew services stop nginx"
echo ""
echo "2. 🚀 Use alternative ports setup:"
echo "   ./docker/setup-alternative-ports.sh"
echo ""
echo "3. 🔄 Or try the regular setup after stopping services:"
echo "   ./docker/setup.sh"
