-- Support Board SaaS Database Initialization Script
-- This script sets up the database with proper permissions and initial data

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS supportboard_cloud CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE supportboard_cloud;

-- Grant permissions to supportboard user
GRANT ALL PRIVILEGES ON supportboard_cloud.* TO 'supportboard'@'%';
FLUSH PRIVILEGES;

-- Insert default admin user (email: admin@localhost, password: password)
INSERT IGNORE INTO users (
    id, 
    first_name, 
    last_name, 
    email, 
    phone, 
    password, 
    membership, 
    membership_expiration, 
    token, 
    last_activity, 
    creation_time, 
    email_confirmed, 
    phone_confirmed,
    customer_id,
    white_label,
    referral,
    referral_count,
    referral_amount,
    purchased_credits,
    purchased_credits_currency,
    purchased_credits_amount,
    purchased_credits_date,
    purchased_credits_transaction_id,
    payment_history
) VALUES (
    1,
    'Admin',
    'User',
    'admin@localhost',
    '',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: "password"
    'premium',
    '2025-12-31',
    'admin_token_123',
    NOW(),
    NOW(),
    1,
    0,
    '',
    0,
    '',
    0,
    0,
    0,
    '',
    0,
    '',
    '',
    ''
);

-- Insert default settings
INSERT IGNORE INTO settings (name, value) VALUES 
('app_name', 'Support Board Local'),
('app_url', 'http://localhost:8080'),
('timezone', 'UTC'),
('language', 'en'),
('currency', 'USD'),
('payment_provider', 'amwal'),
('debug_mode', '1'),
('registration_enabled', '1'),
('email_verification', '0'),
('maintenance_mode', '0');

-- Create default messenger configuration if table exists
INSERT IGNORE INTO messenger (token, page_id, page_token) VALUES 
('local_token', 'local_page_id', 'local_page_token');

-- Show success message
SELECT 'Database initialized successfully!' as message;
