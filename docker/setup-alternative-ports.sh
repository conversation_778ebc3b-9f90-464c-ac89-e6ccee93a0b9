#!/bin/bash

# Support Board SaaS - Alternative Ports Setup Script
# This script uses alternative ports to avoid conflicts

echo "🚀 Setting up Support Board SaaS with Alternative Ports..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Find available ports
WEB_PORT=8080
DB_PORT=3307
PHPMYADMIN_PORT=8081

echo "🔍 Checking for available ports..."

# Check web port
while check_port $WEB_PORT; do
    echo "⚠️  Port $WEB_PORT is in use, trying $((WEB_PORT + 1))..."
    WEB_PORT=$((WEB_PORT + 1))
done

# Check database port
while check_port $DB_PORT; do
    echo "⚠️  Port $DB_PORT is in use, trying $((DB_PORT + 1))..."
    DB_PORT=$((DB_PORT + 1))
done

# Check phpMyAdmin port
while check_port $PHPMYADMIN_PORT; do
    echo "⚠️  Port $PHPMYADMIN_PORT is in use, trying $((PHPMYADMIN_PORT + 1))..."
    PHPMYADMIN_PORT=$((PHPMYADMIN_PORT + 1))
done

echo "✅ Using ports: Web=$WEB_PORT, DB=$DB_PORT, phpMyAdmin=$PHPMYADMIN_PORT"

# Create dynamic docker-compose file
cat > docker-compose.override.yml << EOF
version: '3.8'

services:
  web:
    ports:
      - "$WEB_PORT:80"
  
  db:
    ports:
      - "$DB_PORT:3306"
  
  phpmyadmin:
    ports:
      - "$PHPMYADMIN_PORT:80"
EOF

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    
    # Update ports in .env file
    sed -i.bak "s/WEB_PORT=8080/WEB_PORT=$WEB_PORT/" .env
    sed -i.bak "s/DB_PORT=3307/DB_PORT=$DB_PORT/" .env
    sed -i.bak "s/PHPMYADMIN_PORT=8081/PHPMYADMIN_PORT=$PHPMYADMIN_PORT/" .env
    rm .env.bak
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p script/uploads
mkdir -p docker/logs
chmod -R 777 script/uploads

# Backup original config if it exists
if [ -f script/config.php ] && [ ! -f script/config.php.backup ]; then
    echo "💾 Backing up original config.php..."
    cp script/config.php script/config.php.backup
fi

# Use local config for development
echo "⚙️ Setting up local configuration..."
cp script/config.local.php script/config.php

# Update local config with correct URL
sed -i.bak "s|http://localhost:8080|http://localhost:$WEB_PORT|g" script/config.php
rm script/config.php.bak

# Stop any existing containers
echo "🛑 Stopping any existing containers..."
docker-compose down --remove-orphans

# Build and start containers
echo "🐳 Building and starting Docker containers..."
docker-compose up -d --build

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 15

# Check if containers are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Containers are running successfully!"
    echo ""
    echo "🌐 Access URLs:"
    echo "   • Application: http://localhost:$WEB_PORT"
    echo "   • phpMyAdmin: http://localhost:$PHPMYADMIN_PORT"
    echo ""
    echo "🔐 Default Login:"
    echo "   • Email: admin@localhost"
    echo "   • Password: password"
    echo ""
    echo "💾 Database Info:"
    echo "   • Host: localhost:$DB_PORT"
    echo "   • Database: supportboard_cloud"
    echo "   • Username: supportboard"
    echo "   • Password: supportboard123"
    echo ""
    echo "🎉 Setup complete! You can now access the application."
else
    echo "❌ Some containers failed to start. Check the logs:"
    docker-compose logs
fi
