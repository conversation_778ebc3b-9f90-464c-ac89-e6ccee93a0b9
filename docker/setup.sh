#!/bin/bash

# Support Board SaaS - Local Development Setup Script
# This script helps set up the local development environment

echo "🚀 Setting up Support Board SaaS Local Development Environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. You can modify it if needed."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p script/uploads
mkdir -p docker/logs
chmod -R 777 script/uploads

# Backup original config if it exists
if [ -f script/config.php ] && [ ! -f script/config.php.backup ]; then
    echo "💾 Backing up original config.php..."
    cp script/config.php script/config.php.backup
fi

# Use local config for development
echo "⚙️ Setting up local configuration..."
cp script/config.local.php script/config.php

# Build and start containers
echo "🐳 Building and starting Docker containers..."
docker-compose down --remove-orphans
docker-compose build --no-cache
docker-compose up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Check if containers are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Containers are running successfully!"
    echo ""
    echo "🌐 Access URLs:"
    echo "   • Application: http://localhost:8080"
    echo "   • phpMyAdmin: http://localhost:8081"
    echo ""
    echo "🔐 Default Login:"
    echo "   • Email: admin@localhost"
    echo "   • Password: password"
    echo ""
    echo "💾 Database Info:"
    echo "   • Host: localhost:3306"
    echo "   • Database: supportboard_cloud"
    echo "   • Username: supportboard"
    echo "   • Password: supportboard123"
    echo ""
    echo "🎉 Setup complete! You can now access the application."
else
    echo "❌ Some containers failed to start. Check the logs:"
    docker-compose logs
fi
