# Support Board SaaS - Local Development Setup

This guide will help you set up the Support Board SaaS project locally on your Mac using Docker.

## 🔧 Prerequisites

Before starting, make sure you have the following installed on your Mac:

1. **Docker Desktop** - [Download here](https://www.docker.com/products/docker-desktop/)
2. **Git** (usually pre-installed on Mac)

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

1. **Open Terminal** and navigate to the project directory:
   ```bash
   cd "/Users/<USER>/Documents/supportBorad SaaS/SaaS-Chat Board"
   ```

2. **Run the setup script**:
   ```bash
   ./docker/setup.sh
   ```

3. **Wait for the setup to complete** (this may take 5-10 minutes for the first run)

4. **Access the application**:
   - **Application**: http://localhost:8080
   - **phpMyAdmin**: http://localhost:8081

### Option 2: Manual Setup

1. **Create environment file**:
   ```bash
   cp .env.example .env
   ```

2. **Build and start containers**:
   ```bash
   docker-compose up -d --build
   ```

3. **Wait for containers to start** (about 2-3 minutes)

## 🔐 Default Login Credentials

- **Email**: `admin@localhost`
- **Password**: `password`

## 🌐 Access URLs

| Service | URL | Description |
|---------|-----|-------------|
| **Support Board** | http://localhost:8080 | Main application |
| **phpMyAdmin** | http://localhost:8081 | Database management |
| **Account Panel** | http://localhost:8080/account | User account management |

## 💾 Database Information

| Setting | Value |
|---------|-------|
| **Host** | localhost:3307 |
| **Database** | supportboard_cloud |
| **Username** | supportboard |
| **Password** | supportboard123 |
| **Root Password** | root123 |

## 🧪 Testing AMWAL Payment Gateway

The project is configured with AMWAL Payment Gateway for Oman market testing:

- **Test Mode**: Enabled
- **MID**: 190621
- **TID**: 513651
- **Currency**: OMR (Oman Rial)

To test payments:
1. Go to http://localhost:8080/account
2. Login with admin credentials
3. Navigate to Membership section
4. Try purchasing a membership plan

## 📁 Project Structure

```
SaaS-Chat Board/
├── docker/                 # Docker configuration
│   ├── php/                # PHP configuration
│   ├── mysql/              # MySQL configuration
│   └── setup.sh           # Automated setup script
├── script/                 # Main application
├── account/                # Account management
├── docker-compose.yml      # Docker services definition
├── Dockerfile             # PHP-Apache container
└── README-LOCAL-SETUP.md  # This file
```

## 🛠️ Common Commands

### Start the application
```bash
docker-compose up -d
```

### Stop the application
```bash
docker-compose down
```

### View logs
```bash
docker-compose logs -f
```

### Restart services
```bash
docker-compose restart
```

### Rebuild containers
```bash
docker-compose down
docker-compose up -d --build
```

### Access container shell
```bash
# Web container
docker exec -it supportboard_web bash

# Database container
docker exec -it supportboard_db mysql -u root -p
```

## 🔧 Troubleshooting

### Port Already in Use
If you get port conflicts, you can change the ports in `docker-compose.yml`:
```yaml
ports:
  - "8080:80"  # Change 8080 to another port like 8090
```

### Database Connection Issues
1. Wait for the database to fully initialize (2-3 minutes)
2. Check if containers are running: `docker-compose ps`
3. Check logs: `docker-compose logs db`

### Permission Issues
```bash
# Fix file permissions
sudo chown -R $(whoami) .
chmod -R 755 .
chmod -R 777 script/uploads
```

### Reset Everything
```bash
# Stop and remove all containers and volumes
docker-compose down -v
docker system prune -a

# Start fresh
./docker/setup.sh
```

## 📝 Development Notes

- The application runs in **debug mode** by default
- File changes are automatically reflected (volume mounted)
- Database data persists between container restarts
- Original `config.php` is backed up as `config.php.backup`

## 🎯 Next Steps

1. **Explore the application** at http://localhost:8080
2. **Test AMWAL payment integration** in the account section
3. **Check database** via phpMyAdmin at http://localhost:8081
4. **Review logs** for any issues: `docker-compose logs`

## 🆘 Getting Help

If you encounter any issues:

1. Check the container status: `docker-compose ps`
2. View logs: `docker-compose logs`
3. Ensure Docker Desktop is running
4. Try the reset procedure above

---

**Happy coding! 🚀**
