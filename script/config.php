<?php

/*
 * ==========================================================
 * INITIAL CONFIGURATION FILE
 * ==========================================================
 *
 * Cloud configuration file for Support Board
 *
 */

define('SUPER_EMAIL', '<EMAIL>');
define('SUPER_PASSWORD', '$2y$10$Ktl63f77N0O9.ccGdZiN.OgmDneM8ZkPUBsye8UUBAn7XGUyRI36C');

define('CLOUD_PUSHER_ID', '*******');
define('CLOUD_PUSHER_KEY', '6f5c342ea927f8a19a65');
define('CLOUD_PUSHER_SECRET', 'a1dc8291d789baed4f37');
define('CLOUD_PUSHER_CLUSTER', 'ap2');

//define('CLOUD_TWILIO_SID', '');
//define('CLOUD_TWILIO_TOKEN', '');
//define('CLOUD_TWILIO_SENDER', '');

define('CLOUD_SMTP_HOST', 'smtp.chatboard.om');
define('CLOUD_SMTP_USERNAME', '<EMAIL>');
define('CLOUD_SMTP_PASSWORD', 'RVvjw82M4UdJ');
define('CLOUD_SMTP_PORT', 587);
define('CLOUD_SMTP_SENDER', '<EMAIL>');
define('CLOUD_SMTP_SENDER_NAME', 'Chat Board');

// Docker Database Configuration
define('CLOUD_DB_NAME', 'supportboard_cloud');
define('CLOUD_DB_USER', 'supportboard');
define('CLOUD_DB_PASSWORD', 'supportboard123');
define('CLOUD_DB_HOST', 'db');
define('CLOUD_URL', 'http://localhost:8080');

define('SB_CLOUD', true);
define('SB_CLOUD_KEY', 'Ck-PEBIQ68lRo2k');
define('SB_CLOUD_PATH', '/var/www/html/');
define('SB_CLOUD_BRAND_LOGO', 'https://my.chatboard.om/custom/chatBoard-slug.png');
define('SB_CLOUD_BRAND_LOGO_LINK', 'https://my.chatboard.om');
define('SB_CLOUD_BRAND_ICON', 'https://my.chatboard.om/custom/chatBoard-logo.png');
define('SB_CLOUD_BRAND_ICON_PNG', 'https://my.chatboard.om/custom/chatBoard-logo.png');
define('SB_CLOUD_BRAND_NAME', 'Chat Board');
define('SB_CLOUD_MANIFEST_URL', 'https://my.chatboard.om/manifest.json');
define('SB_CLOUD_MEMBERSHIP_TYPE', 'messages-agents'); //messages, users, agents

define('PAYMENT_PROVIDER', 'manual'); //rapyd, verifone, yoomoney, razorpay, manual

//define('STRIPE_SECRET_KEY', 'XXX');
//define('STRIPE_PRODUCT_ID', 'XXX');
//define('STRIPE_CURRENCY', 'usd');
define('ONESIGNAL_APP_ID', '************************************');
define('ONESIGNAL_API_KEY', 'os_v2_app_wwjfq4pdbfeula3vczkvfqts6w52uppumeduvuvcdt2xy3glmf5fi7zubzbsy74rupowrdcmnrkdv4t733jzlpma4x3k7nsy6xnytaq');

define('ENVATO_PURCHASE_CODE' ,'2a7d2dc6-2dce-4616-9b56-056b75c6ff40');

define('OPEN_EXCHANGE_RATE_APP_ID', '8b08d8ac1f51432d8796fcec6875c5f5');

//define('GOOGLE_CLIENT_ID', '');
//define('GOOGLE_CLIENT_SECRET', '');

//define('WHATSAPP_APP_ID', '');
//define('WHATSAPP_APP_SECRET', '');
//define('WHATSAPP_CONFIGURATION_ID', '');
//define('WHATSAPP_APP_TOKEN', '');
//define('WHATSAPP_VERIFY_TOKEN', '');

//define('MESSENGER_APP_ID', '');
//define('MESSENGER_APP_SECRET', '');
//define('MESSENGER_CONFIGURATION_ID', '');
//define('MESSENGER_VERIFY_TOKEN', '');
//define('MESSENGER_APP_TOKEN', '');

define('OPEN_AI_KEY', '***********************************************************************************************************************************************************************');

//define('RAZORPAY_KEY_ID', '');
//define('RAZORPAY_KEY_SECRET', '');
//define('RAZORPAY_CURRENCY', '');

//define('RAPYD_ACCESS_KEY', '');
//define('RAPYD_SECRET_KEY', '');
//define('RAPYD_TEST_MODE', );
//define('RAPYD_CURRENCY', '');
//define('RAPYD_COUNTRY', '');

//define('VERIFONE_SECRET_WORD', '');
//define('VERIFONE_SECRET_KEY', '');
//define('VERIFONE_MERCHANT_ID', '');
//define('VERIFONE_CURRENCY', '');

//define('YOOMONEY_SHOP_ID', '');
//define('YOOMONEY_KEY_SECRET', '');
//define('YOOMONEY_CURRENCY', '');

// AMWAL Payment Gateway Configuration
define('AMWAL_MID', '190621');
define('AMWAL_TID', '513651');
define('AMWAL_SECURE_HASH_KEY', 'AA988491A63BEFD5368D41A5BA26D745BC73E24B0F67FC38F8A1FDAA589B4E72');
define('AMWAL_CURRENCY_ID', '512'); // OMR
define('AMWAL_TEST_MODE', true);
define('AMWAL_SMARTBOX_JS_URL', 'https://test.amwalpg.com:7443/js/SmartBox.js?v=1.1'); // UAT
define('AMWAL_SESSION_TOKEN_URL', 'https://test.amwalpg.com:14443/Customer/GetSmartboxDirectCallSessionToken');

define('PAYMENT_MANUAL_LINK', 'https://www.omancloud.com');
define('PAYMENT_MANUAL_CURRENCY', 'omr');

//define('SHOPIFY_CLIENT_ID', '');
//define('SHOPIFY_CLIENT_SECRET', '');
//define('SHOPIFY_APP_ID', '');
//define('SHOPIFY_PLANS', [['100 messages', ''], ['5000 messages', 'SB_PLAN_ID'], ['50000 messages', 'SB_PLAN_ID'], ['100000 messages', 'SB_PLAN_ID']]);

//define('SB_CLOUD_AWS_S3', ['amazon-s3-access-key' => '', 'amazon-s3-secret-access-key' => '', 'amazon-s3-bucket-name' => '', 'amazon-s3-backup-bucket-name' => '', 'amazon-s3-region' => '']);
//define('SB_CLOUD_DOCS', '');
//define('STRIPE_PRODUCT_ID_WHITE_LABEL', '');
//define('CLOUD_IP', '');
//define('SB_CLOUD_DEFAULT_LANGUAGE_CODE', 'zh');
//define('SB_CLOUD_DEFAULT_RTL', true);
//define('DIRECT_CHAT_URL', '');
//define('WEBSITE_URL', '');
//define('ARTICLES_URL', '');
//define('SUPER_BRANDING', true);
//define('SB_CLOUD_EMAIL_BODY_AGENTS', '<link href="https://fonts.googleapis.com/css?family=Roboto:400,500,900" rel="stylesheet" type="text/css"><div style="font-family:\'Roboto\',sans-serif;text-align:left;max-width:560px;margin:auto;"><div style="display:none>{message}</div><a href="https://board.support"><img style="width:200px;" src="https://board.support/media/logo.png" alt="logo"></a></div><div style="background:#FFF;padding:30px;border-radius:6px;border:1px solid rgb(218, 222, 223);margin:30px auto;max-width:500px"><p style="font-family:\'Roboto\',sans-serif;text-align:left;letter-spacing:.3px;font-size:15px;line-height:28px;color:#486d85;margin:0;">{message}{attachments}<table style="margin-top:30px;"><tr><td><img style="width:35px;border-radius:50%" src="{sender_profile_image}"></td><td><b style="font-size:13px;color:rgb(128,128,128);padding-left:5px;">{sender_name}</b></td></tr></table><a href="{conversation_link}" style="font-family:\'Roboto\',sans-serif;background-color: #009bfc;color: #FFF;font-size: 14px;line-height: 36px;letter-spacing: 0.3px;font-weight: 500;border-radius: 6px;text-decoration: none;height: 35px;display: inline-block;padding: 0 25px;margin-top: 30px;">Click here to reply</a></p></div><div style="color:#444444;font-size:12px;line-height:20px;padding:0;text-align:left;"><p style="font-family:\'Roboto\',sans-serif;font-size:12px; line-height:20px;color:#a0abb2;max-width:560px;margin: 30px auto">This email was sent to you by Support Board. By using our services, you agree to our <a href="https://board.support/privacy" target="_blank" style="color:#a0abb2;text-decoration:none;">Privacy Policy</a>.<br />&copy; Schiocco LTD. All rights reserved.</p></div></div>');
//define('SB_CLOUD_EMAIL_BODY_USERS', '<link href="https://fonts.googleapis.com/css?family=Roboto:400,500,900" rel="stylesheet" type="text/css"><div style="font-family:\'Roboto\',sans-serif;text-align:left;max-width:560px;margin:auto;"><div style="display:none>{message}</div><a href="https://board.support"><img style="width:200px;" src="https://board.support/media/logo.png" alt="logo"></a></div><div style="background:#FFF;padding:30px;border-radius:6px;border:1px solid rgb(218, 222, 223);margin:30px auto;max-width:500px"><p style="font-family:\'Roboto\',sans-serif;text-align:left;letter-spacing:.3px;font-size:15px;line-height:28px;color:#486d85;margin:0;">{message}{attachments}<table style="margin-top:30px;"><tr><td><img style="width:35px;border-radius:50%" src="{sender_profile_image}"></td><td><b style="font-size:13px;color:rgb(128,128,128);padding-left:5px;">{sender_name}</b> from Support Board</td></tr></table><a href="{conversation_link}" style="font-family:\'Roboto\',sans-serif;background-color: #009bfc;color: #FFF;font-size: 14px;line-height: 36px;letter-spacing: 0.3px;font-weight: 500;border-radius: 6px;text-decoration: none;height: 35px;display: inline-block;padding: 0 25px;margin-top: 30px;">Click here to reply</a></p></div><div style="color:#444444;font-size:12px;line-height:20px;padding:0;text-align:left;"><p style="font-family:\'Roboto\',sans-serif;font-size:12px; line-height:20px;color:#a0abb2;max-width:560px;margin: 30px auto">This email was sent to you by Support Board. By using our services, you agree to our <a href="https://board.support/privacy" target="_blank" style="color:#a0abb2;text-decoration:none;">Privacy Policy</a>.<br />&copy; Schiocco LTD. All rights reserved.</p></div></div>');
//define('CLOUD_ADDONS', [['title' => 'AAAA', 'description' => 'AAA', 'price' => 5.00], ['title' => 'BBBB', 'description' => 'AAA', 'price' => 9.15]]);

?>