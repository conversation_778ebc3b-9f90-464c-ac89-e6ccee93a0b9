"use strict";!function(e){function t(e,t=!1){return dt.infoBottom(e,t)}function i(e,t="info",i=!1,s="",a="",n=!1,o=!1,r=!1){return dt.infoPanel(e,t,i,s,a,n,o,r)}function s(e){return dt.activeUser(e)}function a(e){return dt.loading(e)}function n(e=!0,t=!0){return dt.loadingGlobal(e,t)}function o(e){return SB_TRANSLATIONS&&e in SB_TRANSLATIONS?SB_TRANSLATIONS[e]:e}function r(){typeof caches!==Ke&&caches.delete("sb-pwa-cache")}function l(e,t){dt.collapse(e,t)}function c(t,i){let s=e(t).parent().find("i"),a=e(t).val();SBF.search(a,()=>{s.sbLoading(!0),i(a,s)})}function d(t,i=!1,s=0){if(i)return e(t).scrollTop()+e(t).innerHeight()>=e(t)[0].scrollHeight-1;e(t).scrollTop(e(t)[0].scrollHeight-s)}function u(t=!1){if(!it){if(!1===tt)return tt=!0,void e.getScript(SB_URL+"/vendor/editorjs.js",()=>{u(t)});p(),it=!0,tt=new EditorJS({data:h(t)?{time:Date.now(),blocks:[t?{id:"sb",type:"raw",data:{html:t}}:{id:"sb",type:"paragraph",data:{text:""}}]}:t,i18n:{messages:{ui:{blockTunes:{toggler:{"Click to tune":o("Click to tune")}},inlineToolbar:{converter:{"Convert to":o("Convert to")}},toolbar:{toolbox:{Add:o("Add")}}},toolNames:{Text:o("Text"),Heading:o("Heading"),List:o("List"),Image:o("Image"),Code:o("Code"),"Raw HTML":o("Raw HTML"),Bold:o("Bold"),Italic:o("Italic"),Link:o("Link")},tools:{list:{Ordered:o("Ordered"),Unordered:o("Unordered")}},blockTunes:{delete:{Delete:o("Delete")},moveUp:{"Move up":o("Move up")},moveDown:{"Move down":o("Move down")}}},direction:k.hasClass("sb-rtl")?"rtl":"ltr"},tools:{list:{class:List,inlineToolbar:!0},image:{class:ImageTool,config:{uploader:{uploadByFile(t){let i=new FormData;return i.append("file",t),new Promise(t=>{e.ajax({url:SB_URL+"/include/upload.php",cache:!1,contentType:!1,processData:!1,data:i,type:"POST",success:function(e){"success"==(e=JSON.parse(e))[0]?t({success:1,file:{url:e[1]}}):console.log(e)}})})}}}},header:Header,code:CodeTool,raw:RawTool},onReady:()=>{it=!1,qe=!0,Pe=!1},onChange:()=>{qe?qe=!1:Pe=!0},minHeight:50})}}function p(){typeof tt.destroy!==Ke&&(tt.destroy(),tt=!1)}function h(e){return"string"==typeof e}function f(e){et||window.history.pushState("","",e)}function g(){return SB_ADMIN_SETTINGS.cloud?"&cloud="+SB_ADMIN_SETTINGS.cloud.token:""}function b(e){return e.replace("https://","").replace("http://","").replace("www.","").replace(/\/$/,"")}function v(e){SBF.search(e,()=>{let t="",i=!(e.length>1),s=Fe.find(".sb-replies-list > ul"),a=`<li class="sb-no-results">${o("No results found.")}</li>`;for(var n=0;n<Ne.length;n++){let s=Ne[n]["reply-name"];(i||s.toLowerCase().includes(e)||s.replaceAll("-"," ").toLowerCase().includes(e)||Ne[n]["reply-text"].toLowerCase().includes(e))&&(t+=`<li><div>${s}</div><div>${Ne[n]["reply-text"]}</div></li>`)}if(s.html(t),i||!SB_ADMIN_SETTINGS.dialogflow&&!SB_ADMIN_SETTINGS.chatbot_features)t||s.html(a);else{let i=s.closest(".sb-popup").find(".sb-icon-search");i.sbLoading(!0),SB_ADMIN_SETTINGS.dialogflow?st.dialogflow.getIntents(n=>{let o=st.dialogflow.searchIntents(e,!0),r="";for(var l=0;l<o.length;l++){let e=o[l].messages[0].text;e&&e.text&&(r+=`<li><div>${o[l].displayName}</div><div>${o[l].messages[0].text.text[0]}</div></li>`)}s.html(r?t+r:t||a),i.sbLoading(!1)}):SBF.ajax({function:"open-ai-message",model:SB_ADMIN_SETTINGS.open_ai_model,message:e},e=>{s.html(!e[1]||e[5]&&e[5].unknow_answer?t||a:`${t}<li><div></div><div>${e[1]}</div></li>`),i.sbLoading(!1)})}})}function m(e){let i=k.find("#sb-whatsapp-send-template-box");n(),SBF.ajax({function:"whatsapp-get-templates"},s=>{let a='<option value=""></option>',o=s[0],r="twilio"==o;if((s=s[1]).error)return t(s.error.message,"error");if(!Array.isArray(s))return t(s,"error");for(var l=0;l<s.length;l++)"official"!=o||"APPROVED"!=s[l].status||SB_ACTIVE_AGENT.department&&s[l].department.length&&!s[l].department.includes(SB_ACTIVE_AGENT.department)||(a+=`<option value="${s[l].name}" data-languages="${s[l].languages}" data-phone-id="${s[l].phone_number_id}">${s[l].name} (${s[l].label})</option>`),r&&(a+=`<option value="${s[l].sid}">${s[l].friendly_name}</option>`);i.attr("data-provider",o),i.find("#sb-whatsapp-send-template-list").html(a),SBForm.clear(i),i.find(".sb-direct-message-users").val(e.length?e.join(","):"all"),i.find(".sb-bottom > div").html(""),i.find(".sb-loading").sbLoading(!1),n(!1),i.sbShowLightbox()})}function _(e,t,s){window.open(e),i(`${o("For security reasons, delete the file after downloading it. Close this window to automatically delete it. File location:")}<pre>${e}</pre>`,"info",!1,t,s)}function S(){xe=!1,Be=!1,setTimeout(()=>{ke&&(ke[1].css("transform",""),ke[1].removeClass("sb-touchmove"))},100)}function w(e){return C.find(`[data-conversation-id="${e}"]`)}function y(){SBF.ajax({function:"get-conversations"},e=>{e.length||A.find(".sb-board").addClass("sb-no-conversation"),lt.populateList(e),We&&A.find(".sb-admin-list").sbActive(!0),SBF.getURL("conversation")?(A.sbActive()||x.find(".sb-admin-nav #sb-conversations").click(),lt.openConversation(SBF.getURL("conversation"))):We||SBF.getURL("user")||SBF.getURL("setting")||SBF.getURL("report")||SBF.getURL("area")&&"conversations"!=SBF.getURL("area")||setTimeout(()=>{lt.clickFirst()},100),lt.startRealTime(),lt.datetime_last_conversation=SB_ADMIN_SETTINGS.now_db,n(!1)}),SBF.serviceWorker.init(),SB_ADMIN_SETTINGS.push_notifications&&SBF.serviceWorker.initPushNotifications(),setInterval(function(){SBF.ajax({function:"get-active-user",db:!0},e=>{e||SBF.reset()})},36e5)}var k,x,B,A,T,I,$,C,F,N,E,L,D,M,R,j,G,U,P,q,O,V,H,W,z,J,Y,X,Q,Z,K,ee,te,ie,se,ae,ne,oe,re,le,ce,de,ue,pe,he,fe,ge,be,ve,me,_e,Se,we,ye,ke,xe,Be,Ae,Te,Ie,$e,Ce=[],Fe=!1,Ne=!1,Ee=!1,Le=!1,De=1,Me=1,Re={},je=1,Ge=1,Ue=!0,Pe=!1,qe=!0,Oe=!1,Ve=[],He=[],We=e(window).width()<465,ze={last:0,header:!0,always_hidden:!1},Je="localhost"===location.hostname||"127.0.0.1"===location.hostname,Ye=new Date,Xe=!1,Qe=!0,Ze=!1,Ke="undefined",et=!1,tt=!1,it=!1;e.fn.miniTip=function(t){var i=e.extend({title:"",content:!1,delay:300,anchor:"n",event:"hover",fadeIn:200,fadeOut:200,aHide:!0,maxW:"250px",offset:5,stemOff:0,doHide:!1},t);k&&0==k.find("#miniTip").length&&k.append('<div id="miniTip" class="sb-tooltip"><div></div></div>');var s=k.find("#miniTip"),a=s.find("div");return i.doHide?(s.stop(!0,!0).fadeOut(i.fadeOut),!1):this.each(function(){var t=e(this),n=i.content?i.content:t.attr("title");if(""!=n&&void 0!==n){window.delay=!1;var o=!1,r=!0;i.content||t.removeAttr("title"),"hover"==i.event?(t.hover(function(){s.removeAttr("click"),r=!0,l.call(this)},function(){r=!1,c()}),i.aHide||s.hover(function(){o=!0},function(){o=!1,setTimeout(function(){!r&&!s.attr("click")&&c()},20)})):"click"==i.event&&(i.aHide=!0,t.click(function(){return s.attr("click","t"),s.data("last_target")!==t?l.call(this):"none"==s.css("display")?l.call(this):c(),s.data("last_target",t),e("html").unbind("click").click(function(t){"block"==s.css("display")&&!e(t.target).closest("#miniTip").length&&(e("html").unbind("click"),c())}),!1}));var l=function(){i.show&&i.show.call(this,i),i.content&&""!=i.content&&(n=i.content),a.html(n),i.render&&i.render(s),s.hide().width("").width(s.width()).css("max-width",i.maxW);var o=t.is("area");if(o){var r,l=[],c=[],d=t.attr("coords").split(",");function u(e,t){return e-t}for(r=0;r<d.length;r++)l.push(d[r++]),c.push(d[r]);var p=t.parent().attr("name"),h=e("img[usemap=\\#"+p+"]").offset(),f=parseInt(h.left,10)+parseInt((parseInt(l.sort(u)[0],10)+parseInt(l.sort(u)[l.length-1],10))/2,10),g=parseInt(h.top,10)+parseInt((parseInt(c.sort(u)[0],10)+parseInt(c.sort(u)[c.length-1],10))/2,10)}else g=parseInt(t.offset().top,10),f=parseInt(t.offset().left,10);var b=o?0:parseInt(t.outerWidth(),10),v=o?0:parseInt(t.outerHeight(),10),m=s.outerWidth(),_=s.outerHeight(),S=Math.round(f+Math.round((b-m)/2)),w=Math.round(g+v+i.offset+8),y=Math.round(m-16)/2-parseInt(s.css("borderLeftWidth"),10),k=0,x=f+b+m+i.offset+8>parseInt(e(window).width(),10),B=m+i.offset+8>f,A=_+i.offset+8>g-e(window).scrollTop(),T=g+v+_+i.offset+8>parseInt(e(window).height()+e(window).scrollTop(),10),I=i.anchor;B||"e"==i.anchor&&!x?"w"!=i.anchor&&"e"!=i.anchor||(I="e",k=Math.round(_/2-8-parseInt(s.css("borderRightWidth"),10)),y=-8-parseInt(s.css("borderRightWidth"),10),S=f+b+i.offset+8,w=Math.round(g+v/2-_/2)):(x||"w"==i.anchor&&!B)&&("w"!=i.anchor&&"e"!=i.anchor||(I="w",k=Math.round(_/2-8-parseInt(s.css("borderLeftWidth"),10)),y=m-parseInt(s.css("borderLeftWidth"),10),S=f-m-i.offset-8,w=Math.round(g+v/2-_/2))),T||"n"==i.anchor&&!A?"n"!=i.anchor&&"s"!=i.anchor||(I="n",k=_-parseInt(s.css("borderTopWidth"),10),w=g-(_+i.offset+8)):(A||"s"==i.anchor&&!T)&&("n"!=i.anchor&&"s"!=i.anchor||(I="s",k=-8-parseInt(s.css("borderBottomWidth"),10),w=g+v+i.offset+8)),"n"==i.anchor||"s"==i.anchor?m/2>f?(S=S<0?y+S:y,y=0):f+m/2>parseInt(e(window).width(),10)&&(S-=y,y*=2):A?(w+=k,k=0):T&&(w-=k,k*=2),delay&&clearTimeout(delay),delay=setTimeout(function(){s.css({"margin-left":S+"px","margin-top":w+"px"}).stop(!0,!0).fadeIn(i.fadeIn)},i.delay),s.attr("class","sb-tooltip "+I)},c=function(){(!i.aHide&&!o||i.aHide)&&(delay&&clearTimeout(delay),delay=setTimeout(function(){d()},i.delay))},d=function(){!i.aHide&&!o||i.aHide?(s.stop(!0,!0).fadeOut(i.fadeOut),i.hide&&i.hide.call(this)):setTimeout(function(){c()},200)}}})},e.fn.sbLanguageSwitcher=function(t=[],i="",s=!1){let a=`<div class="sb-language-switcher" data-source="${i}">`,n=[],r=e(this).hasClass("sb-language-switcher-cnt")?e(this):e(this).find(".sb-language-switcher-cnt");for(var l=0;l<t.length;l++){let e=h(t[l])?t[l]:t[l][0],i=!(h(t[l])||!t[l][1])&&t[l][1];n.includes(e)||(a+=`<span ${s==e?'class="sb-active" ':""}data-language="${e}"${i?' data-id="'+i+'"':""}><i class="sb-icon-close"></i><img src="${SB_URL}/media/flags/${e.toLowerCase()}.png" /></span>`,n.push(e))}return r.find(".sb-language-switcher").remove(),r.append(a+`<i data-sb-tooltip="${o("Add translation")}" class="sb-icon-plus"></i></div>`),r.sbInitTooltips(),this},e.fn.sbShowLightbox=function(t=!1,i=""){return k.find(".sb-lightbox").sbActive(!1),we.sbActive(!0),e(this).sbActive(!0),t?e(this).addClass("sb-popup-lightbox").attr("data-action",i):e(this).css({"margin-top":e(this).outerHeight()/-2+"px","margin-left":e(this).outerWidth()/-2+"px"}),e("body").addClass("sb-lightbox-active"),setTimeout(()=>{dt.open_popup=this},500),this.preventDefault,this},e.fn.sbHideLightbox=function(){return e(this).find(".sb-lightbox,.sb-popup-lightbox").sbActive(!1).removeClass("sb-popup-lightbox").removeAttr("data-action"),we.sbActive(!1),e("body").removeClass("sb-lightbox-active"),dt.open_popup=!1,this},e.fn.sbInitTooltips=function(){return e(this).find("[data-sb-tooltip]").each(function(){e(this).miniTip({content:e(this).attr("data-sb-tooltip"),anchor:"s",delay:500})}),this};var st={dialogflow:{intents:!1,qea:[],token:SBF.storage("dialogflow-token"),dialogflow_languages:[],original_response:!1,smart_reply_busy:!1,smartReply:function(e=!1){let t=SBChat.conversation.id;this.smart_reply_busy!=t&&(this.smart_reply_busy=t,SBF.ajax({function:"dialogflow-smart-reply",message:e,token:this.token,conversation_id:t,dialogflow_languages:this.dialogflow_languages},e=>{if(this.smart_reply_busy=!1,SBChat.conversation.id&&t===SBChat.conversation.id){let t=e.suggestions,s="",a=T[0].scrollTop===T[0].scrollHeight-T[0].offsetHeight,n=!(!SBChat.conversation||!SBChat.conversation.getLastMessage())&&SBChat.conversation.getLastMessage().message;e.token&&(this.token=e.token,SBF.storage("dialogflow-token",e.token));for(var i=0;i<t.length;i++)t[i]!=n&&(s+=`<span>${SBF.escape(t[i])}</span>`);R.html(s),a&&SBChat.scrollBottom()}e.dialogflow_languages&&(this.dialogflow_languages=e.dialogflow_languages)}))},showCreateIntentBox:function(e){let t="",i=SBChat.conversation.getMessage(e),s=i.message;if(SBF.isAgent(i.get("user_type")))t=SBChat.conversation.getLastUserMessage(i.get("index")),t=t&&t.payload("sb-human-takeover")?SBChat.conversation.getLastUserMessage(t.get("index")).message:t.message;else{t=s;let e=SBChat.conversation.getNextMessage(i.id,"agent");e&&(s=e.message)}M.hasClass("sb-dialogflow-disabled")?(SBF.ajax({function:"open-ai-get-qea-training"},e=>{let t='<option value="">'+o("New Q&A")+"</option>";for(var i=0;i<e.length;i++)this.qea=e,t+=`<option value="${i}">${e[i][0][0]}</option>`;M.find("#sb-qea-select").html(t)}),st.openAI.generateQuestions(t)):this.getIntents(e=>{let i='<option value="">'+o("New Intent")+"</option>";for(var s=0;s<e.length;s++)i+=`<option value="${e[s].name}">${e[s].displayName}</option>`;M.find("#sb-intents-select").html(i),st.openAI.generateQuestions(t)}),M.attr("data-message-id",i.id),M.find(".sb-type-text:not(.sb-first)").remove(),M.find(".sb-type-text input").val(t),M.find("#sb-intents-select,#sb-qea-select").val(""),M.find(".sb-search-btn").sbActive(!1).find("input").val(""),this.searchIntents(""),this.original_response=s,M.find("textarea").val(s),M.sbShowLightbox()},submitIntent:function(i){if(a(i))return;let s=[],n=M.find("textarea").val(),o=M.find("#sb-intents-select,#sb-qea-select").val(),r=M.find("#sb-train-chatbots").val(),l="open-ai"==r||M.hasClass("sb-dialogflow-disabled");if(M.find(".sb-type-text input").each(function(){e(this).val()&&s.push(e(this).val())}),!n&&!o||0==s.length)SBForm.showErrorMessage(M,"Please insert the bot response and at least one user expression."),e(i).sbLoading(!1);else{let a;l&&(o?(this.qea[o][0]=this.qea[o][0].concat(s),a=this.qea[o]):a=[[s,n]]),SBF.ajax({function:l?"open-ai-qea-training":o?"dialogflow-update-intent":"dialogflow-create-intent",questions_answers:a,expressions:s,response:n,agent_language:M.find(".sb-dialogflow-languages select").val(),conversation_id:SBChat.conversation.id,intent_name:o,update_index:o,services:r,language:M.find(".sb-dialogflow-languages select").val()},s=>{e(i).sbLoading(!1),!0===s?(k.sbHideLightbox(),t("Training completed")):SBForm.showErrorMessage(M,s.error&&s.error.message?s.error&&s.error.message:"Error")})}},getIntents:function(e){!1===this.intents?SBF.ajax({function:"dialogflow-get-intents"},t=>{this.intents=Array.isArray(t)?t:[],e(this.intents)}):e(this.intents)},searchIntents:function(e,t=!1){if(M.hasClass("sb-dialogflow-disabled")){let t=!(e.length>1),s=t?`<option value="">${o("New Q&A")}</option>`:"";e=e.toLowerCase();for(i=0;i<this.qea.length;i++)(t||this.qea[i][0].join("").toLowerCase().includes(e)||this.qea[i][1].toLowerCase().includes(e))&&(s+=`<option value="${i}">${this.qea[i][0][0]}</option>`);M.find("#sb-qea-select").html(s).change(),t&&M.find("textarea").val(this.original_response)}else{let n=!(e.length>1),r=n?`<option value="">${o("New Intent")}</option>`:"",l=this.intents,c=[];e=e.toLowerCase();for(var i=0;i<l.length;i++){let o=n||l[i].displayName.toLowerCase().includes(e);if(!o&&l[i].trainingPhrases){let t=l[i].trainingPhrases;for(var s=0;s<t.length;s++){for(var a=0;a<t[s].parts.length;a++)if(t[s].parts[a].text.toLowerCase().includes(e)){o=!0;break}if(o)break}}o&&(t?c.push(l[i]):r+=`<option value="${l[i].name}">${l[i].displayName}</option>`)}if(t)return c;M.find("#sb-intents-select").html(r).change(),e||M.find("textarea").val(this.original_response)}},previewIntentDialogflow:function(e){let t="",s=this.getIntent(e);if(s){let e=s.trainingPhrases?s.trainingPhrases:[],o=e.length;if(o>1){for(var a=0;a<o;a++)for(var n=0;n<e[a].parts.length&&(t+=`<span>${e[a].parts[n].text}</span>`,15!=n);n++);i(t,"info",!1,"intent-preview-box","",o>10)}}},previewIntent:function(e){if(e){let s="",a=this.qea[e];if(a[0].length>1){let e=a[0].length>15?15:a[0].length;for(var t=0;t<e;t++)s+=`<span>${a[0][t]}</span>`;i(s,"info",!1,"qea-preview-box","",e>10)}}},getIntent:function(e){for(var t=0;t<this.intents.length;t++)if(this.intents[t].name==e)return this.intents[t];return!1},translate:function(e,t,i,s,a){e.length&&SBF.ajax({function:"google-translate",strings:e,language_code:t,token:this.token,message_ids:s,conversation_id:a},e=>{if(this.token=e[1],!Array.isArray(e[0]))return SBF.error(JSON.stringify(e[0]),"SBApps.dialogflow.translate"),!1;i(e[0])})}},openAI:{urls_history:[],progress:1,rewriteButton:function(e){j.length&&j.sbActive(e.length>2&&e.indexOf(" "))},rewrite:function(e,t){SBF.ajax({function:"open-ai-message",model:SB_ADMIN_SETTINGS.open_ai_model,message:(SB_ADMIN_SETTINGS.open_ai_prompt_rewrite?SB_ADMIN_SETTINGS.open_ai_prompt_rewrite:"Make the following sentence more friendly and professional")+` and use ${SB_LANGUAGE_CODES[SB_ADMIN_SETTINGS.active_agent_language]} language: """${e.replace('"',"'")}"""`,extra:"rewrite"},i=>{i[0]||console.error("OpenAI: "+JSON.stringify(i[1])),lt.previous_editor_text=e,t(i)})},troubleshoot:function(){let e=SB_ADMIN_SETTINGS.open_ai_chatbot_status;return!0!==e&&t("inactive"==e?"Enable the chatbot in Settings > Artificial Intelligence > OpenAI > Chatbot.":"key"==e?"Enter the OpenAI API key in Settings > Artificial Intelligence > OpenAI > API key.":"The training data is ignored. Change the chatbot mode in Settings > Artificial Intelligence > OpenAI > Chatbot mode.","error"),e},getCode:{set_data:function(e){let t="",i=this.select_user_details();e&&e.length||(e=[["",""]]);for(var s=0;s<e.length;s++)t+=`<div class="repeater-item"><div>${i.replace(`"${e[s][0]}"`,`"${e[s][0]}" selected`)}<div class="sb-setting"><input type="url" placeholder="${o("Enter the value")}" value="${e[s][1]}"></div></div><i class="sb-icon-close"></i></div>`;return this.repeater_("Data",t)},actions:function(e){let t=[["tags","Assign tags"],["department","Assign a department"],["agent","Assign an agent"],["redirect","Go to URL"],["open_article","Show an article"],["transcript","Download transcript"],["transcript_email","Email transcript"],["send_email","Send email to user"],["send_email_agents","Send email to agents"],["archive_conversation","Archive the conversation"],["human_takeover","Human takeover"]],i="",s="";e&&e.length||(e=[["tags",""]]);for(a=0;a<t.length;a++)i+=`<option value="${t[a][0]}">${o(t[a][1])}</option>`;for(var a=0;a<e.length;a++)s+=`<div class="repeater-item"><div><div class="sb-setting"><select>${i.replace(`"${e[a][0]}"`,`"${e[a][0]}" selected`)}</select></div>${this.action(e[a][0],e[a][1])}</div><i class="sb-icon-close"></i></div>`;return this.repeater_("Actions",s)},action:function(e,t){let i={department:"number",agent:"number",redirect:"url"};return["send_email_agents","send_email","open_article","redirect","agent","department","tags"].includes(e)?`<div class="sb-setting"><input type="${i[e]?i[e]:"text"}" value="${t}" placeholder="${o({tags:"Enter tag names, separated by commas",department:"Enter the department ID",agent:"Enter the agent ID",redirect:"Enter the URL",open_article:"Enter the article ID",send_email:"Enter a message",send_email_agents:"Enter a message"}[e])}" value="${t}"></div>`:""},select_user_details:function(){let e='<div class="sb-setting"><select>',t=[["full_name","Name"],["email","Email"],["password","Password"]].concat(rt.getExtraDetailsList());for(var i=0;i<t.length;i++)e+=`<option value="${t[i][0]}">${o(t[i][1])}</option>`;return e+"</select></div>"},repeater_:function(e,t){return`<div class="sb-title">${o(e)}</div><div data-type="repeater" class="sb-setting sb-type-repeater"><div class="input"><div class="sb-repeater sb-repeater-block-${SBF.stringToSlug(e)}">${t}</div><div class="sb-btn sb-btn-white sb-repeater-add sb-icon"><i class="sb-icon-plus"></i>${o("Add new item")}</div></div></div>`}},generateQuestions:function(e){if(SB_ADMIN_SETTINGS.open_ai_user_expressions&&e){let t=M.find('[data-value="add"]');t.sbLoading(!0),M.find(".sb-open-ai-intent").remove(),SBF.ajax({function:"open-ai-user-expressions",message:e},e=>{let i="";for(var s=0;s<e.length;s++)e[s]&&(i+=`<div class="sb-setting sb-type-text sb-open-ai-intent"><input type="text" value="${e[s].replace(/"/g,"")}"></div>`);i&&M.find("> div > .sb-type-text").last().after(i),t.sbLoading(!1)})}},flows:{flows:[],set:function(e){if("string"==typeof e)(e=e.trim().replaceAll('"',"").replaceAll("_","-"))&&(e={name:e,steps:[[[{type:"start",start:"message",message:"",conditions:[],disabled:!1}]],[[]]]});else for(var t=0;t<this.flows.length;t++)if(this.flows[t].name==e.name)return this.flows[t]=e,this.show(e.name),!0;this.flows.push(e),re.find(".sb-active").sbActive(!1),re.append(this.navCode(e.name,!0)),this.show(e.name)},get:function(e=!1){e||(e=this.getActiveName());for(var t=0;t<this.flows.length;t++)if(this.flows[t].name==e)return this.flows[t].steps||(this.flows[t].steps=[]),this.flows[t];return!1},show:function(t=!1){t||(t=this.getActiveName());let i,s=this.get(t),a="";if(s){let t=[],d=[];for(var n=0;n<s.steps.length;n++){let e=s.steps[n],u=0;t=d,d=[],a+="<div><div>";for(var r=0;r<e.length;r++){let s=e[r];a+='<div class="sb-flow-block-cnt">',t[r]&&(a+=`<div class="sb-flow-block-cnt-name">${t[r]}</div>`);for(var l=0;l<s.length;l++){"start"!=s[l].type||Array.isArray(s[l].message)||(s[l].message=[{message:s[l].message}]);let e=s[l].message?Array.isArray(s[l].message)?s[l].message.length?s[l].message[0].message:"":s[l].message:"";switch(e&&(e=`<div>${e.length>45?e.substring(0,45)+"...":e}</div>`),a+=`<div class="sb-flow-block" data-type="${s[l].type}"><div>${o(SBF.slugToString(s[l].type))}</div>${e}`,s[l].type){case"get_user_details":d.push(!1);break;case"condition":case"button_list":let t="condition"==s[l].type,n=t?[o("True"),o("False")]:s[l].options;if(t){a+="<div>",i=s[l].conditions;for(c=0;c<i.length;c++)a+=`<div>${SBF.slugToString(i[c][0]+" "+i[c][1])}${i[c][2]?": "+i[c][2]:""}</div>`;a+="</div>"}a+='<div class="sb-flow-connectors">';for(c=0;c<n.length;c++)d.push("ABCDEFGHIJKLMNOPQRSTUVWXYZ"[u]+(c+1)),a+=`<div>${n[c]}<span>${d[d.length-1]}</span></div>`;a+="</div>",u++;break;case"video":a+=`<div>${s[l].url}</div>`;break;case"action":case"set_data":a+="<div>",i=s[l]["set_data"==s[l].type?"data":"actions"];for(var c=0;c<i.length;c++)a+=`<div>${SBF.slugToString(i[c][0])}${i[c][1]?": "+i[c][1]:""}</div>`;a+="</div>";break;case"rest_api":a+=`<div>${s[l].url}</div>`}a+="</div>"}s.length<4&&(a+='<div class="sb-flow-add-block sb-icon-plus"></div>'),a+="</div>"}a+='</div><div class="sb-flow-add-step sb-icon-plus"></div></div>'}le.html(a),e(k).find(".sb-flow-scroll").sbActive(250*s.steps.length>le.outerWidth())}},delete:function(e){for(var t=0;t<this.flows.length;t++)if(this.flows[t].name==e){let i=re.find(`[data-value="${e}"]`);return this.flows[t].steps.forEach(e=>{e.forEach(e=>{e.forEach(e=>{e.attachments&&e.attachments.forEach(e=>{SBF.ajax({function:"delete-file",path:e})})})})}),this.flows.splice(t,1),i.sbActive()&&(i.prev().length?i.prev().click():i.next().length?i.next().click():le.html("")),i.remove(),!0}return!1},save:function(e=!1){SBF.ajax({function:"open-ai-flows-save",flows:JSON.stringify(this.flows)},t=>{e(t)})},navCode:function(e,t=!1){return`<li${t?' class="sb-active"':""} data-value="${e}">${e}<i class="sb-icon-delete"></i></li>`},getActive:function(){return re.find(".sb-active")},getActiveName:function(){return this.getActive().attr("data-value")},getActiveIndex:function(){return this.getActive().index()},steps:{get:function(e=!1,t=!1){return st.openAI.flows.get(e).steps[t||this.getActiveIndex()]},getActiveIndex:function(){return st.openAI.flows.blocks.getActive().parent().parent().parent().index()}},blocks:{set:function(e,t=!1,i=!1,s=!1,a=!1){let n=this.getIndexes(t,i,s,a),o=st.openAI.flows.get(t);if(o){let i=!1;if(o.steps.length>n.step){let t=o.steps[n.step];if(t.length>n.cnt){let s=t[n.cnt];s.length>n.block&&(s[n.block]=e,i=!0)}i||(o.steps[n.step][n.cnt].push(e),i=!0)}i||o.steps.push([[e]]);let s=o.steps[n.step],a=o.steps[st.openAI.flows.steps.getActiveIndex()+1],c=0;for(l=0;l<s.length;l++)for(var r=0;r<s[l].length;r++)switch(s[l][r].type){case"get_user_details":c+=1;break;case"button_list":c+=s[l][r].options.length;break;case"condition":c+=2}if(a)if(a.length>c)a.splice(-1*(c-1),c);else{let e=a.length;for(e;e<c;e++)a.splice(n.cnt,0,[])}else{a=[];for(var l=0;l<c;l++)a.push([]);a.length&&o.steps.push(a)}return st.openAI.flows.show(t),!0}return!1},get:function(e=!1,t=!1,i=!1,s=!1){let a=st.openAI.flows.get(e);if(a){let n=this.getIndexes(e,t,i,s);return a.steps[n.step][n.cnt][n.block]}return!1},add:function(e,t=!1,i=!1,s=!1,a=!1){let n=this.getIndexes(t,i,s,a);this.set(Object.assign({button_list:{message:"",options:[]},message:{message:""},video:{message:"",url:""},get_user_details:{message:"",details:[]},set_data:{data:[]},action:{actions:[]},rest_api:{url:"",method:"",headers:[],body:"",save_response:[]},condition:{conditions:[]}}[e],{type:e}),n.name,n.step,n.cnt,n.block),st.openAI.flows.show(t),setTimeout(()=>{le.find("> div").eq(n.step).find(".sb-flow-block-cnt").eq(n.cnt).find(".sb-flow-block").eq(n.block).click()},100)},delete:function(e=!1,t=!1,i=!1,s=!1){let a=this.getIndexes(e,t,i,s);for(var n=0;n<st.openAI.flows.flows.length;n++){let e=st.openAI.flows.flows[n];if(a.name==e.name){if(["get_user_details","button_list","condition"].includes(e.steps[a.step][a.cnt][a.block].type)){let t=this.delete_(n,a.step,a.cnt),i=Object.assign({},e);for(o=0;o<t.length;o++)e.steps[t[o][0]][t[o][1]]=!1;e.steps[a.step][a.cnt].splice(a.block,1),i.steps=[];for(var o=0;o<e.steps.length;o++){let t=[];for(var r=0;r<e.steps[o].length;r++)e.steps[o][r]&&t.push(e.steps[o][r]);t.length&&i.steps.push(t)}st.openAI.flows.flows[n]=i}else e.steps[a.step][a.cnt].splice(a.block,1);return st.openAI.flows.show(a.name),!0}}return!1},delete_:function(e,t,i,s=[]){let a=st.openAI.flows.blocks.getNextCntIndexes(e,t,i);for(var n=0;n<a.length;n++)s.push([t+1,a[n]]),this.delete_(e,t+1,a[n],s);return s},getActive:function(){return le.find(".sb-flow-add-block.sb-active, .sb-flow-block.sb-active")},getActiveIndex:function(){let e=this.getActiveCnt().find(".sb-flow-block"),t=e.index(this.getActive());return-1===t?e.length:t},getActiveCnt:function(){return this.getActive().parent()},getActiveCntIndex:function(){return this.getActive().parent().index()},getNextCnt:function(e,t,i,s=0){let a=this.getNextCntIndexes(e,t,i,s);return a.length>s&&st.openAI.flows.flows[e].steps[t+1][a[s]]},getNextCntIndexes:function(e,t,i){let s=st.openAI.flows.flows[e],a=[];if(s&&s.steps[t+1]){let e=0;for(var n=0;n<=i;n++){let l=s.steps[t][n];for(var o=0;o<l.length;o++)if("button_list"==l[o].type)for(r=0;r<l[o].options.length;r++)n==i&&a.push(e),e++;else if("get_user_details"==l[o].type)n==i&&a.push(e),e++;else if("condition"==l[o].type)for(var r=0;r<2;r++)n==i&&a.push(e),e++}}return a},getPreviousCntIndex:function(e,t,i){let s=st.openAI.flows.flows[e].steps[t-1];if(s){let e=0;for(var a=0;a<s.length;a++){let t=s[a];for(var n=0;n<t.length;n++)e+="button_list"==t[n].type?t[n].options.length:"get_user_details"==t[n].type?1:"condition"==t[n].type?2:0;if(e>i)return a}return e}},getIndexes:function(e,t,i,s){return{name:e||st.openAI.flows.getActiveName(),step:t||st.openAI.flows.steps.getActiveIndex(),cnt:i||this.getActiveCntIndex(),block:s||this.getActiveIndex()}},activateLinkedCnts:function(t){let i=e(t).parent(),s=st.openAI.flows.getActiveIndex(),a=i.parent().parent().index(),n=le.find("> div"),o=n.eq(a-1).find(".sb-flow-block-cnt").eq(st.openAI.flows.blocks.getPreviousCntIndex(s,a,i.index()));if(!Oe){let e=n.eq(a+1).find(".sb-flow-block-cnt"),t=st.openAI.flows.blocks.getNextCntIndexes(s,a,i.index());for(var r=0;r<t.length;r++)e.eq(t[r]).sbActive(!0)}le.find(".sb-flow-connectors > div").sbActive(!1),o.sbActive(!0).find(".sb-flow-block-cnt-name").sbActive(!0),i.find(".sb-flow-block-cnt-name").length&&o.find(".sb-flow-connectors > div").sbActive(!1).eq(parseInt(i.find(".sb-flow-block-cnt-name").html().substring(1))-1).sbActive(!0)}}},train:{urls:[],errors:[],base_url:!1,start_urls:[],sitemap_processed_urls:[],active_source:!1,history:[],training_button:!1,extract_url:[],skip_files:[],files:function(e,t=0){let i=de.prop("files");if(t>=i.length)return e(!0);let s=i[t].name;this.isFile(s)&&!this.skip_files.includes(s)?(k.find("#sb-embeddings-box p").html(o("We are processing the source")+"<pre>"+s+"</pre><span>"+o("Only {R} sources left to complete.").replace("{R}",i.length-t)+"</span>"),de.sbUploadFiles(i=>{"success"==(i=JSON.parse(i))[0]&&SBF.ajax({function:"open-ai-file-training",url:i[1]},i=>{this.isError(i)||this.files(e,t+1)})},t)):this.files(e,t+1)},website:function(e,t=0){if(t>=this.urls.length)return e(!0);let i=this.urls[t];i&&i.includes("http")?(k.find("#sb-embeddings-box p").html(o("We are processing the source")+"<pre>"+i+"</pre><span>"+o("Only {R} sources left to complete.").replace("{R}",this.urls.length-t)+"</span>"),i.includes(".xml")?SBF.ajax({function:"get-sitemap-urls",sitemap_url:i},i=>{Array.isArray(i)?this.urls=this.urls.concat(i):this.errors.push(i),this.website(e,t+1)}):!this.sitemap_processed_urls.includes(i)&&this.extract_url[t]?(this.sitemap_processed_urls.push(i),this.sitemap(i,i=>{this.urls=this.urls.concat(i),this.website(e,t+1)})):SBF.ajax({function:"open-ai-url-training",url:i},i=>{this.isError(i)||(i[0]||i[1].includes("http-error-404")||i[1].includes("http-error-302")||(i[1].includes("http-error")&&0===t?this.errors.push(i[2]):!0!==i[0][0]&&this.errors.push(i)),this.website(e,t+1))})):(i&&this.errors.push(o("Use a valid URL starting with http. The URL {R} is not valid.").replace("{R}",i)),this.website(e,t+1))},sitemap:function(e,t,i=[]){k.find("#sb-embeddings-box p").html(o("We are generating the sitemap")+"<pre>"+e+"</pre>"),SBF.ajax({function:"generate-sitemap",url:e},e=>{t(e)})},qea:function(e){k.find("#sb-embeddings-box p").html(o("We are processing the Q&A."));let t=at.repeater.get(ae.find(".sb-repeater").eq(0).find("> .repeater-item")).map(e=>[e["open-ai-faq-questions"].map(e=>e.question),e["open-ai-faq-answer"],e["open-ai-faq-function-calling-url"],e["open-ai-faq-function-calling-method"],e["open-ai-faq-function-calling-headers"],!!e["open-ai-faq-function-calling-properties"].length&&e["open-ai-faq-function-calling-properties"].map(e=>[e.name,e.description,e.allowed]),e["open-ai-faq-set-data"].map(e=>[e.id,e.value])]);SBF.ajax({function:"open-ai-qea-training",questions_answers:t,reset:!0},t=>{e(t)})},articles:function(e){k.find("#sb-embeddings-box p").html(o("We are processing the articles.")),SBF.ajax({function:"open-ai-articles-training"},t=>{e(t)})},isFile:function(e){return e.includes(".pdf")||e.includes(".txt")},isError:function(e){let t="chars-limit-exceeded"==e[1],s=t||e[1]&&e[1][0]&&e[1][0].error;return s&&(te.find("#sb-train-chatbot").sbLoading(!1),i(t?o("The chatbot cannot be trained with these sources because the limit of your plan is {R} characters. Upgrade your plan to increase the number of characters.").replace("{R}",e[2]):e[1][0].error.message)),s}},playground:{messages:[],last_response:!1,addMessage:function(e,t="user",i=[]){oe.append(`<div data-type="${t}"><div>${o("user"==t?"User":"Assistant")}<div><i class="sb-icon-close sb-btn-icon sb-btn-red"></i></div></div><div>${new SBMessage({id:1,message:SBF.escape(e),creation_time:"0000-00-00 00:00:00",status_code:0,user_type:"agent",attachments:JSON.stringify(i)}).getCode()}</div></div>`),oe[0].scrollTop=oe[0].scrollHeight,this.messages.push([t,e])}},init:function(){SBF.ajax({function:"open-ai-get-training-files"},e=>{let t=["",""];for(var i=0;i<e.length;i++)if(!["sb-conversations","sb-articles","sb-database","sb-flows"].includes(e[i])){let s=this.train.isFile(e[i]);t[s?1:0]+=`<tr data-url="${e[i]}"><td><input type="checkbox" /></td><td>${s?SBF.beautifyAttachmentName(e[i].split("/").pop()):b(e[i])}</td><td></td><td><i class="sb-icon-delete"></i></td></tr>`}ie.html(t[1]).sbLoading(!1),se.html(t[0]).sbLoading(!1),te.find("#sb-chatbot-delete-website").setClass("sb-hide",!t[0])}),SBF.ajax({function:"open-ai-get-qea-training"},t=>{for(var i=0;i<t.length;i++)t[i][0]&&!Array.isArray(t[i][0])&&(t[i][0]=[t[i][0]]);let s=t.map(e=>({"open-ai-faq-questions":e[0]?e[0].map(e=>({question:e})):[""],"open-ai-faq-answer":e[1],"open-ai-faq-function-calling-url":e[2],"open-ai-faq-function-calling-method":e[3],"open-ai-faq-function-calling-headers":e[4],"open-ai-faq-function-calling-properties":e[5]&&e[5].length?e[5].map(e=>({name:e[0],description:e[1],allowed:e[2]})):[["","",""]],"open-ai-faq-set-data":e[6]&&e[6].length?e[6].map(e=>({id:e[0],value:e[1]})):[["",""]]}));s.length&&(ae.find("> div > .sb-repeater").html(at.repeater.set(s,ae.find("> div > .sb-repeater > .repeater-item:last-child"))),ae.find(".sb-enlarger").each(function(){let t=e(this).find("select"),i=["transcript","transcript_email","human_takeover","archive_conversation"];(e(this).find("input").val()||i.includes(t.val()))&&(e(this).sbActive(!0),e(this).hasClass("sb-enlarger-function-calling")&&e(this).closest(".repeater-item").find(".sb-qea-repeater-answer").addClass("sb-hide")),t.each(function(){i.includes(e(this).val())&&e(this).parent().next().find("input").addClass("sb-hide")})}))})}},messenger:{check:function(e){return["fb","ig"].includes(e.get("source"))},send:function(e,t,i="",s=[],a,n=!1,o=!1){SBF.ajax({function:"messenger-send-message",psid:e,facebook_page_id:t,message:i,message_id:n,attachments:s,metadata:a},e=>{o&&o(e),st.unsupportedRichMessages(i,"Messenger")})}},whatsapp:{check:function(e){return"wa"==e.get("source")},send:function(e,t="",s=[],a=!1,n=!1){SBF.ajax({function:"whatsapp-send-message",to:e,message:t,attachments:s,phone_id:a},e=>{e.error&&i(e.error.message,"info",!1,"error-wa"),n&&n(e),st.unsupportedRichMessages(t,"WhatsApp")})},activeUserPhone:function(e=s()){return!!e.getExtra("phone")&&e.getExtra("phone").value.replace("+","")}},telegram:{check:function(e){return"tg"==e.get("source")},send:function(e,t="",i=[],s=!1,a=!1){SBF.ajax({function:"telegram-send-message",chat_id:e,message:t,attachments:i,conversation_id:s},e=>{a&&a(e),st.unsupportedRichMessages(t,"Telegram")})}},viber:{check:function(e){return"vb"==e.get("source")},send:function(e,t="",i=[],s=!1){SBF.ajax({function:"viber-send-message",viber_id:e,message:t,attachments:i},e=>{s&&s(e),st.unsupportedRichMessages(t,"Viber")})}},zalo:{check:function(e){return"za"==e.get("source")},send:function(e,t="",i=[],s=!1){SBF.ajax({function:"zalo-send-message",zalo_id:e,message:t,attachments:i},e=>{s&&s(e),st.unsupportedRichMessages(t,"Zalo")})}},twitter:{check:function(e){return"tw"==e.get("source")},send:function(e,t="",i=[],s=!1){SBF.ajax({function:"twitter-send-message",twitter_id:e,message:t,attachments:i},e=>{s&&s(e),st.unsupportedRichMessages(t,"Twitter")})}},line:{check:function(e){return"ln"==e.get("source")},send:function(e,t="",i=[],s=!1,a=!1){SBF.ajax({function:"line-send-message",line_id:e,message:t,attachments:i,conversation_id:s},e=>{a&&a(e),st.unsupportedRichMessages(t,"LINE")})}},wechat:{token:!1,check:function(e){return"wc"==e.get("source")},send:function(e,t="",i=[],s=!1){SBF.ajax({function:"wechat-send-message",open_id:e,message:t,attachments:i,token:this.token},e=>{Array.isArray(e)&&(this.token=e[1],e=e[0]),s&&s(e),st.unsupportedRichMessages(t,"WeChat")})}},aecommerce:{conversationPanel:function(){let t="",i=s().getExtra("aecommerce-id");this.panel||(this.panel=A.find(".sb-panel-aecommerce")),i&&!a(this.panel)&&SBF.ajax({function:"aecommerce-get-conversation-details",aecommerce_id:i.value},i=>{t=`<h3>${SB_ADMIN_SETTINGS.aecommerce_panel_title}</h3><div><div class="sb-split"><div><div class="sb-title">${o("Number of orders")}</div><span>${i.orders_count} ${o("orders")}</span></div><div><div class="sb-title">${o("Total spend")}</div><span>${i.total} ${i.currency_symbol}</span></div></div><div class="sb-title">${o("Cart")}</div><div class="sb-list-items sb-list-links sb-aecommerce-cart">`;for(s=0;s<i.cart.length;s++){let e=i.cart[s];t+=`<a href="${e.url}" target="_blank" data-id="${e.id}"><span>#${e.id}</span> <span>${e.name}</span> <span>x ${e.quantity}</span></a>`}if(t+=(i.cart.length?"":"<p>"+o("The cart is currently empty.")+"</p>")+"</div>",i.orders.length){t+=`<div class="sb-title">${o("Orders")}</div><div class="sb-list-items sb-list-links sb-aecommerce-orders">`;for(var s=0;s<i.orders.length;s++){let e=i.orders[s],a=e.id;t+=`<a data-id="${a}" href="${e.url}" target="_blank"><span>#${e.id}</span> <span>${SBF.beautifyTime(e.time,!0)}</span> <span>${e.price} ${i.currency_symbol}</span></a>`}t+="</div>"}e(this.panel).html(t).sbLoading(!1),l(this.panel,160)}),e(this.panel).html(t)}},martfury:{conversationPanel:function(){let t=s().getExtra("martfury-id");this.panel||(this.panel=A.find(".sb-panel-martfury")),t&&!a(this.panel)&&SBF.ajax({function:"martfury-get-conversation-details",martfury_id:t.value},t=>{e(this.panel).html(t).sbLoading(!1),l(this.panel,160)}),e(this.panel).html("")}},whmcs:{conversationPanel:function(){let t="",i=s().getExtra("whmcs-id");this.panel||(this.panel=A.find(".sb-panel-whmcs")),i&&!a(this.panel)&&SBF.ajax({function:"whmcs-get-conversation-details",whmcs_id:i.value},i=>{let s=["products","addons","domains"];t=`<h3>WHMCS</h3><div><div class="sb-split"><div><div class="sb-title">${o("Number of services")}</div><span>${i.services_count} ${o("services")}</span></div><div><div class="sb-title">${o("Total spend")}</div><span>${i.total} ${i.currency_symbol}</span></div></div></div>`;for(var a=0;a<s.length;a++){let e=i[s[a]];if(e.length){t+=`<div class="sb-title">${o(SBF.slugToString(s[a]))}</div><div class="sb-list-items">`;for(var n=0;n<e.length;n++)t+=`<div>${e[n].name}</div>`;t+="</div>"}}t+=`<a href="${SB_ADMIN_SETTINGS.whmcs_url}/clientssummary.php?userid=${i["client-id"]}" target="_blank" class="sb-btn sb-whmcs-link">${o("View on WHMCS")}</a>`,e(this.panel).html(t).sbLoading(!1),l(this.panel,160)}),e(this.panel).html(t)}},perfex:{conversationPanel:function(){let e=s().getExtra("perfex-id");A.find(".sb-panel-perfex").html(e?`<a href="${SB_ADMIN_SETTINGS.perfex_url}/admin/clients/client/${e.value}" target="_blank" class="sb-btn sb-perfex-link">${o("View on Perfex")}</a>`:"")}},ump:{conversationPanel:function(){if(a(this.panel))return;this.panel||(this.panel=A.find(".sb-panel-ump"));let t,i="";SBF.ajax({function:"ump-get-conversation-details"},s=>{if((t=s.subscriptions).length){i='<i class="sb-icon-refresh"></i><h3>Membership</h3><div class="sb-list-names">';for(var a=0;a<t.length;a++){let e=t[a].expired;i+=`<div${e?' class="sb-expired"':""}><span>${t[a].label}</span><span>${o(e?"Expired on":"Expires on")} ${SBF.beautifyTime(t[a].expire_time,!1,!e)}</span></div>`}i+=`</div><span class="sb-title">${o("Total spend")} ${s.total} ${s.currency_symbol}</span>`}e(this.panel).html(i).sbLoading(!1),l(this.panel,160)})}},armember:{conversationPanel:function(){let t=s().getExtra("wp-id");if(this.panel||(this.panel=A.find(".sb-panel-armember")),SBF.null(t)||a(this.panel))e(this.panel).html("");else{let i,a="";t=t.value,SBF.ajax({function:"armember-get-conversation-details",wp_user_id:t},t=>{if((i=t.subscriptions).length){a=`<i class="sb-icon-refresh"></i><h3>${o("Plans")}</h3><div class="sb-list-names">`;for(var n=0;n<i.length;n++){let e=i[n].expired;a+=`<div${e?' class="sb-expired"':""}><span>${i[n].arm_current_plan_detail.arm_subscription_plan_name}</span><span>${"never"==i[n].expire_time?"":o(e?"Expired on":"Expires on")+" "+SBF.beautifyTime(i[n].expire_time,!1,!e)}</span></div>`}a+=`</div><span class="sb-title">${o("Total spend")} ${t.total} ${t.currency_symbol}<a href="${window.location.href.substr(0,window.location.href.lastIndexOf("/"))+"?page=arm_manage_members&member_id="+s().getExtra("wp-id").value}" target="_blank" class="sb-btn-text"><i class="sb-icon-user"></i> ${o("View member")}</a></span>`}e(this.panel).html(a).sbLoading(!1),l(this.panel,160)})}}},zendesk:{conversationPanel:function(){if(!SB_ADMIN_SETTINGS.zendesk_active)return;let t=s().getExtra("zendesk-id"),i=s().getExtra("phone"),n=s().get("email"),o=A.find(".sb-panel-zendesk");(t||i||n)&&!a(o)?SBF.ajax({function:"zendesk-get-conversation-details",conversation_id:SBChat.conversation.id,zendesk_id:!!t&&t.value,phone:!!i&&i.value,email:n},t=>{e(o).html(t).sbLoading(!1),o.find(".sb-zendesk-date").each(function(){e(this).html(SBF.beautifyTime(e(this).html()))}),l(o,160)}):e(o).html("")}},woocommerce:{timeout:!1,conversationPanel:function(){if(a(this.panel))return;this.panel||(this.panel=A.find(".sb-panel-woocommerce"));let t="";SBF.ajax({function:"woocommerce-get-conversation-details"},i=>{t=`<i class="sb-icon-refresh"></i><h3>WooCommerce</h3><div><div class="sb-split"><div><div class="sb-title">${o("Number of orders")}</div><span>${i.orders_count} ${o("orders")}</span></div><div><div class="sb-title">${o("Total spend")}</div><span>${i.total} ${i.currency_symbol}</span></div></div><div class="sb-title">${o("Cart")}<i class="sb-add-cart-btn sb-icon-plus"></i></div><div class="sb-list-items sb-list-links sb-woocommerce-cart">`;for(s=0;s<i.cart.length;s++){let e=i.cart[s];t+=`<a href="${e.url}" target="_blank" data-id="${e.id}"><span>#${e.id}</span> <span>${e.name}</span> <span>x ${e.quantity}</span><i class="sb-icon-close"></i></a>`}if(t+=(i.cart.length?"":"<p>"+o("The cart is currently empty.")+"</p>")+"</div>",i.orders.length){t+=`<div class="sb-title">${o("Orders")}</div><div class="sb-list-items sb-woocommerce-orders sb-accordion">`;for(var s=0;s<i.orders.length;s++){let e=i.orders[s],a=e.id;t+=`<div data-id="${a}"><span><span>#${a}</span> <span>${SBF.beautifyTime(e.date,!0)}</span><a href="${ye}/wp-admin/post.php?post=${a}&action=edit" target="_blank" class="sb-icon-next"></a></span><div></div></div>`}t+="</div>"}e(this.panel).html(t).sbLoading(!1),l(this.panel,160)})},conversationPanelOrder:function(e){let t=this.panel.find(`[data-id="${e}"] > div`);t.html(""),SBF.ajax({function:"woocommerce-get-order",order_id:e},e=>{let i="",s=this.panel.find(".sb-collapse-btn:not(.sb-active)");if(e){let t=e.products;i+=`<div class="sb-title">${o("Order total")}: <span>${e.total} ${e.currency_symbol}<span></div><div class="sb-title">${o("Order status")}: <span>${SBF.slugToString(e.status.replace("wc-",""))}<span></div><div class="sb-title">${o("Date")}: <span>${SBF.beautifyTime(e.date,!0)}<span></div><div class="sb-title">${o("Products")}</div>`;for(a=0;a<t.length;a++)i+=`<a href="${ye}?p=${t[a].id}" target="_blank"><span>#${t[a].id}</span> <span>${t[a].quantity} x</span> <span>${t[a].name}</span></a>`;for(var a=0;a<2;a++){let t=0==a?"shipping":"billing";e[t+"_address"]&&(i+=`<div class="sb-title">${o((0==a?"Shipping":"Billing")+" address")}</div><div class="sb-multiline">${e[t+"_address"].replace(/\\n/g,"<br>")}</div>`)}}s.length&&s.click(),t.html(i)})},conversationPanelUpdate:function(e,t="added"){let i=!1,s=0;this.timeout=setInterval(()=>{i||(SBF.ajax({function:"woocommerce-get-conversation-details"},a=>{let n=!0;for(var o=0;o<a.cart.length;o++)a.cart[o].id==e&&("added"==t?s=61:n=!1);(s>60||n)&&(this.conversationPanel(),A.find(".sb-add-cart-btn,.sb-woocommerce-cart > a i").sbLoading(!1),clearInterval(this.timeout)),s++,i=!1}),i=!0)},1e3)}},opencart:{conversationPanel:function(){let e=A.find(".sb-panel-opencart"),t=s().getExtra("opencart_id"),i=s().getExtra("opencart_store_url");if(!t)return e.html("");a(e)||SBF.ajax({function:"opencart-panel",opencart_id:t.value,store_url:!!i&&i.value},t=>{e.html(t).sbLoading(!1),l(this.panel,160)})},openOrder:function(e){SBF.ajax({function:"opencart-order-details",order_id:e},t=>{dt.infoPanel(t,"info",!1,"opencart-order-details",o("Order")+" #"+e,!0)})}},wordpress:{ajax:function(t,i,s){e.ajax({method:"POST",url:SB_WP_AJAX_URL,data:e.extend({action:"sb_wp_ajax",type:t},i)}).done(e=>{!1!==s&&s(e)})}},is:function(e){if(typeof SB_VERSIONS==Ke)return!1;switch(e){case"opencart":case"zendesk":case"twitter":case"wechat":case"line":case"viber":case"zalo":case"telegram":case"armember":case"aecommerce":case"martfury":case"whmcs":case"perfex":case"ump":case"messenger":case"whatsapp":case"woocommerce":case"dialogflow":case"slack":case"tickets":return typeof SB_VERSIONS[e]!=Ke&&SB_VERSIONS[e];case"wordpress":return typeof SB_WP!=Ke;case"sb":return!0}return!1},unsupportedRichMessages:function(e,i,s=[]){s.push("timetable","registration","table","inputs"),["Messenger","WhatsApp"].includes(i)||s.push("email");for(var a=0;a<s.length;a++)e.includes("["+s[a])&&t("The {R} rich message is not supported by {R2}. The rich message was not sent to {R2}.".replace(/{R}/g,SBF.slugToString(s[a])).replace(/{R2}/g,i),"error")},getName:function(e){let t={fb:"Facebook",wa:"WhatsApp",tm:"Text message",ig:"Instagram",tg:"Telegram",tk:"Tickets",wc:"WeChat",em:"Email",tw:"Twitter",bm:"Business Messages",vb:"Viber",ln:"LINE",za:"Zalo"};return e in t?t[e]:e},itemsPanel:{pagination_reference:1,panel_language:"",code:function(e,t,i=!0){let s="";for(var a=0;a<e.length;a++)s+=`<li data-id="${e[a].id.split("/").pop()}"><div class="sb-image" style="background-image:url('${e[a].image?"shopify"==t?e[a].image.replace(".jpg","_small.jpg"):e[a].image:SB_URL+"/media/thumb.svg"}')"></div><div><span>${e[a].name?e[a].name:e[a].title}</span><span>${e[a].price?e[a].price:e[a].variants.edges[0].node.price} ${SB_ADMIN_SETTINGS.currency}</span></div></li>`;return i?s||`<p class="sb-no-results">${o("No products found")}</p>`:s},getAppInfo:function(e){switch(e){case"woocommerce":return{area:Ee,ul:Le,search:"woocommerce-search-products",filter:"woocommerce-get-products",populate:"woocommerce-products-popup",pagination:"woocommerce-get-products"};case"shopify":return{area:SBCloud.shopify_products_box,ul:SBCloud.shopify_products_box_ul,search:"shopify-get-products",filter:"shopify-get-products",populate:"shopify-get-products",pagination:"shopify-get-products"}}},search:function(t,i){let s=this.getAppInfo(i);c(t,(t,a)=>{t?(this.pagination_reference=1,SBF.ajax({function:s.search,search:t},t=>{"shopify"==i&&(this.pagination_reference=t[1],t=t[0]),this.getAppInfo(i).ul.html(this.code(t,i)),e(a).sbLoading(!1)})):this.populate(i,function(){e(a).sbLoading(!1)})})},filter:function(t,i){let s=this.getAppInfo(i),n=e(t).data("value");a(s.ul)||(s.ul.html(""),this.pagination_reference=1,SBF.ajax({function:s.filter,user_language:this.panel_language,filters:{taxonomy:n},collection:n},e=>{"shopify"==i&&(this.pagination_reference=e[1],e=e[0]),s.ul.html(this.code(e,i)).sbLoading(!1)}))},populate:function(e,t=!1){let i=this.getAppInfo(e);this.panel_language=s()&&SB_ADMIN_SETTINGS.languages&&SB_ADMIN_SETTINGS.languages.includes(s().language)?s().language:"",this.pagination_reference=1,i.ul.html("").sbLoading(!0),SBF.ajax({function:i.populate,user_language:this.panel_language},s=>{let a="",n=i.area.find(".sb-select");for(var r=0;r<s[1].length;r++)a+=`<li data-value="${s[1][r].id}">${s[1][r].name}</li>`;s[2]&&(this.pagination_reference=s[2]),s[3]&&(SB_ADMIN_SETTINGS.currency=s[3]),n.find("> p").html(o("All")),n.find("ul").html(`<li data-value="" class="sb-active">${o("All")}</li>`+a),i.ul.html(this.code(s[0],e)).sbLoading(!1),!1!==t&&t()})},pagination:function(t,i){let s=this.getAppInfo(i),a=e(t).parent().find(".sb-select p").attr("data-value");this.pagination_reference&&(s.ul.sbLoading(t),SBF.ajax({function:s.pagination,filters:{taxonomy:a},collection:a,pagination:this.pagination_reference,user_language:this.panel_language},e=>{"shopify"==i?(this.pagination_reference=e[1],e=e[0]):this.pagination_reference++,s.ul.append(this.code(e,i,!1)).sbLoading(!1),e.length||(this.pagination_reference=0)}))}}},at={init:!1,save:function(i=!1){if(i&&a(i))return;let s={},n={};switch(H.find(" > .sb-tab > .sb-nav .sb-active").attr("id")){case"tab-automations":let a=J.find(".sb-active").attr("data-id");at.automations.save(s=>{t(!0===s?"Automations saved":s),at.automations.populate(),J.find(`[data-id="${a}"]`).click(),i&&e(i).sbLoading(!1)});break;case"tab-translations":this.translations.updateActive(),SBF.ajax({function:"save-translations",translations:JSON.stringify(this.translations.to_update)},()=>{t("Translations saved"),i&&e(i).sbLoading(!1)});break;default:H.find(".sb-setting").each((t,i)=>{let a=this.get(i),o=e(i).data("setting");if(a[0])if(typeof o!=Ke){let t=!1;if(e(i).find("[data-language]").length){let s=e(i).find("[data-language].sb-active");if(t=a[0]in this.translations.originals&&this.translations.originals[a[0]],this.translations.save(i,!!s.length&&s.attr("data-language")),t&&"string"!=typeof t)for(var r in t)t[r]=[t[r],a[1][r][1]]}o in s||(s[o]={}),s[o][a[0]]=[t||a[1],a[2]]}else n[a[0]]=[a[1],a[2]]}),SBF.ajax({function:"save-settings",settings:JSON.stringify(n),external_settings:s,external_settings_translations:this.translations.translations},()=>{i&&(t("Settings saved. Reload to apply the changes."),e(i).sbLoading(!1))})}},get:function(t){let i=(t=e(t)).attr("id"),s=t.data("type");switch(s){case"upload":case"range":case"number":case"text":case"password":case"color":case"upload-file":return[i,t.find("input").val(),s];case"textarea":return[i,t.find("textarea").val(),s];case"select":return[i,t.find("select").val(),s];case"checkbox":return[i,t.find("input").is(":checked"),s];case"radio":let a=t.find("input:checked").val();return SBF.null(a)&&(a=""),[i,a,s];case"upload-image":let n=t.find(".image").attr("data-value");return SBF.null(n)&&(n=""),[i,n,s];case"multi-input":let o={};return t.find(".input > div").each((e,t)=>{let i=this.get(t);i[0]&&(o[i[0]]=[i[1],i[2]])}),[i,o,s];case"select-images":return[i,t.find(".input > .sb-active").data("value"),s];case"repeater":return[i,this.repeater.get(t.find(".repeater-item")),s];case"double-select":let r={};return t.find(".input > div").each(function(){let t=e(this).find("select").val();-1!=t&&(r[e(this).attr("data-id")]=[t])}),[i,r,s];case"select-checkbox":return[i,t.find(".sb-select-checkbox input:checked").map(function(){return e(this).attr("id")}).get(),s];case"timetable":let l={};return t.find(".sb-timetable > [data-day]").each(function(){let t=e(this).attr("data-day"),i=[];e(this).find("> div > div").each(function(){let t=e(this).html(),s=e(this).attr("data-value");SBF.null(s)?i.push(["",""]):"closed"==s?i.push(["closed","Closed"]):i.push([s,t])}),l[t]=i}),[i,l,s];case"color-palette":return[i,t.attr("data-value"),s]}return["","",""]},set:function(t,i){let s=e(i)[1],a=e(i)[0];switch(t=`#${t}`,s){case"color":case"upload":case"number":case"text":case"password":case"upload-file":H.find(`${t} input`).val(a);break;case"textarea":H.find(`${t} textarea`).val(a);break;case"select":H.find(`${t} select`).val(a);break;case"checkbox":H.find(`${t} input`).prop("checked","false"!=a&&a);break;case"radio":H.find(`${t} input[value="${a}"]`).prop("checked",!0);break;case"upload-image":a&&H.find(t+" .image").attr("data-value",a).css("background-image",`url("${a}")`);break;case"multi-input":for(var n in a)this.set(n,a[n]);break;case"range":let i=a;H.find(t+" input").val(i),H.find(t+" .range-value").html(i);break;case"select-images":H.find(t+" .input > div").sbActive(!1),H.find(t+` .input > [data-value="${a}"]`).sbActive(!0);break;case"select-checkbox":for(o=0;o<a.length;o++)H.find(`input[id="${a[o]}"]`).prop("checked",!0);H.find(t+" .sb-select-checkbox-input").val(a.join(", "));break;case"repeater":let r=this.repeater.set(a,H.find(t+" .repeater-item:last-child"));r&&H.find(t+" .sb-repeater").html(r);break;case"double-select":for(var n in a)H.find(`${t} .input > [data-id="${n}"] select`).val(a[n]);break;case"timetable":for(var n in a){let i=H.find(`${t} [data-day="${n}"] > div > div`);for(var o=0;o<i.length;o++)e(i[o]).attr("data-value",a[n][o][0]).html(a[n][o][1])}break;case"color-palette":a&&H.find(t).attr("data-value",a)}},repeater:{set:function(t,i){var s="";if(this.clear(e(i)),i=e(i).html(),t.length){e(i).find("> .sb-icon-close").remove();for(var a=0;a<t.length;a++){let o=e(e.parseHTML(`<div>${i}</div>`));for(var n in t[a])at.input.set(o.find(`[data-id="${n}"]`),t[a][n]);s+=`<div class="repeater-item">${o.html().replaceAll('<i class="sb-icon-close"></i>',"")}<i class="sb-icon-close"></i></div>`}}return s},get:function(t){let i=[];return e(t).each(function(){let t={},s=!0;e(this).find("[data-id]").removeClass("sb-exclude"),e(this).find(".sb-repeater [data-id]").addClass("sb-exclude"),e(this).find("[data-id]:not(.sb-exclude)").each(function(){let i=at.input.get(this);s&&i&&"hidden"!=e(this).attr("type")&&"auto-id"!=e(this).attr("data-type")&&(s=!1),t[e(this).attr("data-id")]=i}),s||i.push(t)}),i},add:function(t){let i=e(t).parent();t=e(e.parseHTML(`<div>${i.find("> .sb-repeater > .repeater-item:last-child").html()}</div>`)),this.clear(t),t.find(".repeater-item:not(:first-child)").remove(),t.find("[data-id]").each(function(){if(at.input.reset(this),"auto-id"==e(this).data("type")){let t=1;i.find('[data-type="auto-id"]').each(function(){let i=parseInt(e(this).val());i>t&&(t=i)}),e(this).attr("value",t+1)}}),i.find("> .sb-repeater").append(`<div class="repeater-item">${t.html()}</div>`)},delete:function(t){let i=e(t).parent(),s=i.parent();s.parent().find(".sb-repeater-upload").length&&SBF.ajax({function:"delete-file",path:i.find("input").val()}),s.find("> .repeater-item").length>1?i.remove():i.find('[data-id]:not([data-type="auto-id"]').each((e,t)=>{at.input.reset(t)})},clear:function(e){e.find(".sb-active").sbActive(!1),e.find('input:not([data-type="auto-id"]').removeAttr("value checked"),e.find("option").removeAttr("selected"),e.find(".sb-hide").removeClass("sb-hide")}},input:{set:function(t,i){if(t=e(t),"object"!=typeof i&&(i=e.trim(i)),t.is("select"))t.find(`option[value="${i}"]`).attr("selected","");else if(t.is(":checkbox")&&i&&"false"!=i)t.attr("checked","");else if(t.is("textarea"))t.html(i);else{let e=t.is("div");t.hasClass("sb-repeater")?t.html(at.repeater.set(i,"<div>"+t.find("> .repeater-item").eq(0).html()+"</div>").replaceAll("sb-icon-close","sb-icon-close sb-sub-repeater-close")):e||t.is("i")||t.is("li")?(t.attr("data-value",i),e&&t.hasClass("image")&&t.css("background-image",i?`url("${i}")`:"")):t.attr("value",i)}},get:function(t){if((t=e(t)).is(":checkbox"))return t.is(":checked");if(t.hasClass("sb-repeater"))return at.repeater.get(t.find("> .repeater-item"));if(t.is("div")||t.is("i")||t.is("li")){let e=t.attr("data-value");return e||""}return t.val()},reset:function(t){(t=e(t)).is("select")?t.val("").find("[selected]").removeAttr("selected"):t.is(":checkbox")?t.removeAttr("checked").prop("checked",!1):t.is("textarea")?(t.val(""),t.html("")):t.hasClass("sb-repeater")?t.find(".repeater-item:not(:first-child)").remove():t.removeAttr("value style data-value").val("")}},initColorPicker:function(t=!1){e(t||H).find(".sb-type-color input").colorPicker({renderCallback:function(t,i){e(t.context).closest(".input").find("input").css("background-color",t.text)}})},getSettingObject:function(t){return e(t)[0].hasAttribute("data-setting")?e(t):e(t).closest("[data-setting]")},visibility:function(e,t){let i=[["#push-notifications-onesignal-sw-url, #push-notifications-onesignal-app-id, #push-notifications-onesignal-api-key, #push-notifications-sw-path","#push-notifications-id, #push-notifications-key"],["#messenger-key, #messenger-path-btn","#messenger-sync-btn"],["#open-ai-assistant-id","#open-ai-prompt,#open-ai-model, #open-ai-tokens, #open-ai-temperature, #open-ai-presence-penalty, #open-ai-frequency-penalty, #open-ai-logit-bias, #open-ai-custom-model, #open-ai-source-links"]];H.find(i[e][0]).sbActive(!t),H.find(i[e][1]).setClass("sb-hide",!t)},open:function(e,t=!1){x.find(".sb-admin-nav #sb-settings").click(),t&&setTimeout(()=>{H.find("#tab-"+e).click().get(0).scrollIntoView()},300)},automations:{items:{messages:[],emails:[],sms:[],popups:[],design:[],more:[]},translations:{},conditions:function(){let e={datetime:["Date time",["Is between","Is exactly"],"dd/mm/yyy hh:mm - dd/mm/yyy hh:mm"],repeat:["Repeat",["Every day","Every week","Every month","Every year"]],browsing_time:["Browsing time",[],"seconds"],scroll_position:["Scroll position",[],"px"],url:["Current URL",["Contains","Does not contain"],"URLs parts separated by commas"],referring:["Referring URL",["Contains","Does not contain"],"URLs parts separated by commas"],user_type:["User type",["Is visitor","Is lead","Is user","Is not visitor","Is not lead","Is not user"]],returning_visitor:["Returning visitor",["First time visitor","Returning visitor"]],countries:["Country",["Is included","Is not included","Is set","Is not set"],"Country codes separated by commas"],languages:["Language",["Is included","Is not included","Is set","Is not set"],"Language codes separated by commas"],cities:["City",["Is included","Is not included","Is set","Is not set"],"Cities separated by commas"],website:["Website",["Contains","Does not contain","Is set","Is not set"],"URLs parts separated by commas"],birthdate:["Birthdate",["Is between","Is exactly","Is set","Is not set"],"dd/mm - dd/mm"],company:["Company",["Is included","Is not included","Is set","Is not set"],"Company names separated by commas"],postal_code:["Postal code",["Is included","Is not included","Is set","Is not set"],"Postal codes separated by commas"],email:["Email",["Contains","Does not contain","Is set","Is not set"],"Email addresses separated by commas"],phone:["Phone",["Contains","Does not contain","Is set","Is not set"],"Phone numbers separated by commas"],creation_time:["Creation time",["Is between","Is exactly"],"dd/mm/yyy hh:mm - dd/mm/yyy hh:mm"],custom_variable:["Custom variable",[],"variable=value"]},t=rt.getExtraDetailsList(!0);for(var i=0;i<t.length;i++)e[t[i][0]]=[t[i][1],["Contains","Does not contain","Is set","Is not set"],"Values separated by commas"];return e},get:function(e){SBF.ajax({function:"automations-get"},t=>{this.items=t[0],this.translations=Array.isArray(t[1])&&!t[1].length?{}:t[1],e(t)})},save:function(e=!1){this.updateActiveItem(),SBF.ajax({function:"automations-save",automations:this.items,translations:this.translations},t=>{e&&e(t)})},show:function(e=!1,t=!1){this.updateActiveItem();let i=t?t in this.translations?this.translations[t]:[]:this.items,s=W.find(" > .sb-tab > .sb-content");!1===e&&(e=this.activeID()),this.hide(!1);for(var a in i)for(var n=0;n<i[a].length;n++){let o=i[a][n];if(o.id==e){for(var a in o){let e=s.find(`[data-id="${a}"]`);e.hasClass("image")?(e.css("background-image",`url(${o[a]})`).attr("data-value",o[a]),o[a]||e.removeAttr("data-value")):"checkbox"==e.attr("type")?e.prop("checked",o[a]):e.val(o[a])}return this.setConditions(o.conditions,Y),Y.parent().setClass("sb-hide",t),s.sbLanguageSwitcher(this.getTranslations(e),"automations",t),!0}}return!1},add:function(){let e=SBF.random(),t=`${o("Item")} ${J.find("li:not(.sb-no-results)").length+1}`;this.updateActiveItem(),this.items[this.activeType()].push(this.itemArray(this.activeType(),e,t)),this.hide(!1),J.find(".sb-active").sbActive(!1),J.find(".sb-no-results").remove(),J.append(`<li class="sb-active" data-id="${e}">${t}<i class="sb-icon-delete"></i></li>`),W.find(".sb-automation-values").find("input, textarea").val(""),W.sbLanguageSwitcher([],"automations"),Y.html("")},delete:function(t){this.items[this.activeType()].splice(e(t).parent().index(),1),e(t).parent().remove(),this.hide(),0==this.items[this.activeType()].length&&J.html(`<li class="sb-no-results">${o("No results found.")}</li>`)},populate:function(e=!1){!1===e&&(e=this.activeType());let t="",i=this.items[e];if(this.updateActiveItem(),i.length)for(s=0;s<i.length;s++)t+=`<li data-id="${i[s].id}">${i[s].name}<i class="sb-icon-delete"></i></li>`;else t=`<li class="sb-no-results">${o("No results found.")}</li>`;switch(J.html(t),t="",e){case"emails":t=`<h2>${o("Subject")}</h2><div class="sb-setting sb-type-text"><div><input data-id="subject" type="text"></div></div>`;break;case"popups":t=`<h2>${o("Title")}</h2><div class="sb-setting sb-type-text"><div><input data-id="title" type="text"></div></div><h2>${o("Profile image")}</h2><div data-type="upload-image" class="sb-setting sb-type-upload-image"><div class="input"><div data-id="profile_image" class="image"><i class="sb-icon-close"></i></div></div></div><h2>${o("Message fallback")}</h2><div class="sb-setting sb-type-checkbox"><div><input data-id="fallback" type="checkbox"></div></div>`;break;case"design":t=`<h2>${o("Header title")}</h2><div class="sb-setting sb-type-text"><div><input data-id="title" type="text"></div></div>`;for(s=1;s<4;s++)t+=`<h2>${o((1==s?"Primary":2==s?"Secondary":"Tertiary")+" color")}</h2><div data-type="color" class="sb-setting sb-type-color"><div class="input"><input data-id="color_${s}" type="text"><i class="sb-close sb-icon-close"></i></div></div>`;for(var s=1;s<4;s++)t+=`<h2>${o(1==s?"Header background image":2==s?"Header brand image":"Chat button icon")}</h2><div data-type="upload-image" class="sb-setting sb-type-upload-image"><div class="input"><div data-id="${1==s?"background":2==s?"brand":"icon"}" class="image"><i class="sb-icon-close"></i></div></div></div>`;break;case"more":t=`<h2>${o("Department ID")}</h2><div class="sb-setting sb-type-number"><div><input data-id="department" type="number"></div></div><h2>${o("Agent ID")}</h2><div class="sb-setting sb-type-number"><div><input data-id="agent" type="number"></div></div><h2>${o("Tags")}</h2><div class="sb-setting sb-type-text"><div><input data-id="tags" type="text"></div></div><h2>${o("Article IDs")}</h2><div class="sb-setting sb-type-number"><div><input data-id="articles" type="text"></div></div><h2>${o("Articles category")}</h2><div class="sb-setting sb-type-number"><div><input data-id="articles_category" type="text"></div></div>`}W.find(".sb-automation-extra").html(t),W.attr("data-automation-type",e),at.initColorPicker(W),this.hide()},updateActiveItem:function(){let t=this.activeID();if(t){let s=W.find(".sb-language-switcher [data-language].sb-active").attr("data-language"),a=this.activeType(),n=s?s in this.translations?this.translations[s][a]:[]:this.items[a];for(var i=0;i<n.length;i++)if(n[i].id==t){n[i]={id:t,conditions:[]},W.find(".sb-automation-values").find('input,textarea,[data-type="upload-image"] .image').each(function(){n[i][e(this).attr("data-id")]=e(this).hasClass("image")&&e(this)[0].hasAttribute("data-value")?e(this).attr("data-value"):"checkbox"==e(this).attr("type")?e(this).is(":checked"):e(this).val()}),n[i].conditions=this.getConditions(Y),SBF.null(n[i].name)&&this.delete(J.find(`[data-id="${t}"] i`));break}}},getConditions:function(t){let i=[];return t.find(" > div").each(function(){let t=[];e(this).find("input,select").each(function(){t.push(e(this).val())}),t[0]&&t[1]&&(2==t.length||t[2]||["is-set","is-not-set"].includes(t[1]))&&i.push(t)}),i},setConditions:function(e,t){if(t.html(""),e)for(var i in e){this.addCondition(t);let s=t.find(" > div:last-child");s.find("select").val(e[i][0]),this.updateCondition(s.find("select")),s.find(" > div").eq(1).find("select,input").val(e[i][1]),e[i].length>2&&(["is-set","is-not-set"].includes(e[i][1])?s.find(" > div").eq(2).addClass("sb-hide"):s.find(" > div").eq(2).find("input").val(e[i][2]))}},addCondition:function(e){e.append(`<div><div class="sb-setting sb-type-select sb-condition-1"><select>${this.getAvailableConditions()}</select></div></div>`)},updateCondition:function(t){e(t).parent().siblings().remove();let i=e(t).parents().eq(1);if(e(t).val()){let a=this.conditions()[e(t).val()],n="";if(a[1].length){n='<div class="sb-setting sb-type-select sb-condition-2"><select>';for(var s=0;s<a[1].length;s++)n+=`<option value="${SBF.stringToSlug(a[1][s])}">${o(a[1][s])}</option>`;n+="</select></div>"}i.append(n+(a.length>2?`<div class="sb-setting sb-type-text"><input placeholder="${o(a[2])}" type="text"></div>`:"")),i.siblings().find(".sb-condition-1 select").each(function(){let t=e(this).val();e(this).html(at.automations.getAvailableConditions([t])),e(this).val(t)})}else i.remove()},getAvailableConditions:function(t=[],i=[]){let s='<option value=""></option>',a=[],n=this.conditions();Y.find(".sb-condition-1 select").each(function(){a.push(e(this).val())});for(var r in n)i.includes(r)||a.includes(r)&&!t.includes(r)||(s+=`<option value="${r}">${o(n[r][0])}</option>`);return s},addTranslation:function(e=!1,t=!1,i){if(!1===e&&(e=this.activeID()),!1===t&&(t=this.activeType()),this.getTranslations(e).includes(e))return console.warn("Automation translation already in array.");i in this.translations||(this.translations[i]={messages:[],emails:[],sms:[],popups:[],design:[]}),t in this.translations[i]||(this.translations[i][t]=[]),this.translations[i][t].push(this.itemArray(t,e))},getTranslations:function(e=!1){let t=[];!1===e&&(e=this.activeID());for(var i in this.translations){let n=this.translations[i];for(var s in n){let o=n[s];for(var a=0;a<o.length;a++)if(o[a].id==e){t.push(i);break}}}return t},deleteTranslation:function(e=!1,t){if(!1===e&&(e=this.activeID()),t in this.translations){let a=this.translations[t];for(var i in a){let n=a[i];for(var s=0;s<n.length;s++)if(n[s].id==e)return this.translations[t][i].splice(s,1),!0}}return!1},activeID:function(){let e=J.find(".sb-active");return!!e.length&&e.attr("data-id")},activeType:function(){return z.find("li.sb-active").data("value")},itemArray:function(t,i,s="",a=""){return e.extend({id:i,name:s,message:a},"emails"==t?{subject:""}:"popups"==t?{title:"",profile_image:""}:"design"==t?{title:"",color_1:"",color_2:"",color_3:"",background:"",brand:"",icon:""}:{})},hide:function(e=!0){W.find(" > .sb-tab > .sb-content").setClass("sb-hide",e)}},translations:{translations:{},originals:{},to_update:{},add:function(e){let t=at.getSettingObject(fe),i=t.attr("id"),s=fe.find("[data-language].sb-active");this.save(t,!!s.length&&s.attr("data-language")),t.find('textarea,input[type="text"]').val(""),this.save(t,e),fe.remove(),t.sbLanguageSwitcher(this.getLanguageCodes(i),"settings",e)},delete:function(e,t){let i=(e=at.getSettingObject(e)).attr("id");delete this.translations[t][i],e.find(`.sb-language-switcher [data-language="${t}"]`).remove(),this.activate(e)},activate:function(e,t=!1){let i=(e=at.getSettingObject(e)).attr("id"),s=t?this.translations[t][i]:this.originals[i];if(h(s))e.find("input, textarea").val(s);else for(var a in s)e.find("#"+a).find("input, textarea").val(h(s[a])?s[a]:s[a][0])},updateActive:function(){let t=H.find(".sb-translations-list"),i={front:{},admin:{},"admin/js":{},"admin/settings":{}},s=t.attr("data-value");if(!SBF.null(s)){for(var a in i)t.find(' > [data-area="'+a+'"] .sb-setting:not(.sb-new-translation)').each(function(){i[a][e(this).find("label").html()]=e(this).find("input").val()}),t.find('> [data-area="'+a+'"] .sb-new-translation').each(function(){let t=e(this).find("input:first-child").val(),s=e(this).find("input:last-child").val();t&&s&&(i[a][t]=s)});this.to_update[s]=i}},save:function(t,i=!1){t=at.getSettingObject(t);let s={},a=e(t).attr("id");"multi-input"==t.data("type")?t.find(".multi-input-textarea,.multi-input-text").each(function(){s[e(this).attr("id")]=e(this).find("input, textarea").val()}):s=t.find("input, textarea").val(),i?(i in this.translations||(this.translations[i]={}),this.translations[i][a]=s):this.originals[a]=s},load:function(e){let t=H.find(".sb-translations > .sb-content");t.find(" > .sb-hide").removeClass("sb-hide"),this.updateActive(),SBF.ajax({function:"get-translation",language_code:e},i=>{e in this.to_update&&(i=this.to_update[e]);let s="",a=["front","admin","admin/js","admin/settings"];for(var n=0;n<a.length;n++){let e=i[a[n]];s+=`<div${n?"":' class="sb-active"'} data-area="${a[n]}">`;for(var o in e)s+=`<div class="sb-setting sb-type-text"><label>${o}</label><div><input type="text" value="${e[o]}"></div></div>`;s+="</div>"}t.find(".sb-translations-list").attr("data-value",e).html(s),t.find(".sb-menu-wide li").sbActive(!1).eq(0).sbActive(!0),t.sbLoading(!1)}),t.sbLoading(!0)},getLanguageCodes:function(e){let t=[];for(var i in this.translations)e in this.translations[i]&&t.push(i);return t}}},nt={category_list:[],page_url:!1,get:function(e,t=!1,i=!1,s=!0,a=!1){SBF.ajax({function:"get-articles",id:t,categories:i,articles_language:a,full:s},t=>{e(t)})},save:function(e=!1){let t,i=this.activeID();if(!(t={id:i,title:X.find(".sb-article-title input").val(),content:X.find(".sb-article-content textarea").val(),link:X.find(".sb-article-link input").val(),parent_category:ee.val(),category:K.val(),language:X.find(".sb-language-switcher [data-language].sb-active").attr("data-language")}).title&&!t.content)return e(!1);t.language&&(t.parent_id=this.activeID(!0)),tt&&typeof tt.save!==Ke?tt.save().then(i=>{t.editor_js=i,t.content=function(e){let t="";return e.map(e=>{switch(e.type){case"header":t+=`<h${e.data.level}>${e.data.text}</h${e.data.level}>`;break;case"paragraph":t+=`<p>${e.data.text}</p>`;break;case"image":t+=`<img class="img-fluid" src="${e.data.file.url}" title="${e.data.caption}" /><em>${e.data.caption}</em>`;break;case"list":t+='<ul class="sb-ul-'+e.data.style+'">',e.data.items.forEach(function(e){t+=`<li>${e}</li>`}),t+="</ul>";break;case"code":t+=`<code>${e.data.code}</code>`;break;case"raw":t+=`<div class="bxc-raw-html">${e.data.html}</div>`}}),t}(i.blocks),this.save_2(t,e)}).catch(e=>{console.log(e)}):this.save_2(t,e)},save_2:function(e,i=!1){SBF.ajax({function:"save-article",article:JSON.stringify(e)},s=>{let a=!0!==s&&!isNaN(s);if(Pe=!1,a&&(Q.attr("data-id",s),e.id=s,this.viewButton(s),e.language)){let t=nt.translations.get(e.parent_id);Q.find(`.sb-language-switcher [data-language="${e.language}"]`).attr("data-id",s);for(var n=0;n<t.length;n++)if(t[n][0]==e.language){t[n][1]=e.id,nt.translations.list[e.parent_id]=t;break}}e.language||X.find(".ul-articles .sb-active").html(e.title+'<i class="sb-icon-delete"></i>').attr("data-id",e.id),i&&i(s),t(!0===s||a?"Article saved":s)})},show:function(e){e&&(a(Q),this.get(t=>{Q.sbLoading(!1),t=t[0],Q.sbLanguageSwitcher(this.translations.get(t.parent_id?t.parent_id:e),"articles",t.language),Q.attr("data-id",e),X.find(".sb-article-title input").val(t.title),X.find(".sb-article-link input").val(t.link),X.find("#sb-article-id").html(`ID <span>${e}</span>`),X.find(".sb-article-categories").setClass("sb-hide",t.language),tt||X.find("#editorjs").length?u(t.editor_js?h(t.editor_js)?JSON.parse(t.editor_js):t.editor_js:t.content):X.find(".sb-article-content textarea").val(t.content),t.language||(ee.val(t.parent_category),K.val(t.category)),this.viewButton(e),Pe=!1},e))},add:function(){let e=X.find(".ul-articles");e.find(".sb-active").sbActive(!1),e.append('<li class="sb-active"></li>'),Q.sbLanguageSwitcher([],"articles"),this.clear()},clear:function(){Q.removeAttr("data-id").removeClass("sb-hide"),Q.find("input, textarea, select").val(""),Q.find("input").prop("checked",!1),u(),this.viewButton(),Pe=!1},delete:function(e,t=!1){SBF.ajax({function:"save-article",article:JSON.stringify({id:e,delete:!0})},e=>{this.clear(),t&&t(e)})},populate:function(t,i=!1){let s="";for(var a=0;a<t.length;a++)s+=`<li data-id="${t[a].id}">${t[a].title}<i class="sb-icon-delete"></i></li>`;X.find(i?".ul-categories":".ul-articles").html(s),X.find(i?".ul-categories > li":".ul-articles > li").eq(0).click(),t.length||e(i?Z:Q).sbLoading(!1).addClass("sb-hide")},activeID:function(e=!1){return e?X.find(".ul-articles .sb-active").attr("data-id"):Q.attr("data-id")},viewButton:function(e=!1){if(this.page_url){X.find(".sb-view-article").attr("href",e?this.page_url+(this.is_url_rewrite?("/"==this.page_url.charAt(this.page_url.length-1)?"":"/")+(this.cloud_chat_id?this.cloud_chat_id+"/":""):"?article_id=")+e:"")}},categories:{list:[],save:function(e=!1){this.updateActive(),SBF.ajax({function:"save-articles-categories",categories:JSON.stringify(this.list)},i=>{e&&e(i),t(!0===i?"Categories saved":i)})},show:function(t,i=!1){let s=this.getIndex(t);if(!1!==s){let t=i?this.list[s].languages[i]:this.list[s],a=Z.find("#category-image");this.updateActive(),Z.find("#category-title").val(t.title),Z.find("#category-description").val(t.description),Z.find("#category-parent").prop("checked",!!t.parent),Z.sbLanguageSwitcher(e.map(this.list[s].languages,function(e,t){return t}),"article-categories",i),t.image?a.attr("data-value",t.image).css("background-image",`url("${t.image}")`):a.removeAttr("data-value style"),Z.find(".category-parent").setClass("sb-hide",i)}Z.sbLoading(!1)},add:function(){let e=SBF.random();this.list.push({id:e,title:"",description:"",image:"",languages:[]});let t=`<li data-id="${e}">${o("New category")}<i class="sb-icon-delete"></i></li>`;X.find(".ul-categories").append(t),X.find(".ul-categories li").eq(X.find(".ul-categories li").length-1).click(),Z.removeClass("sb-hide")},delete:function(e){let t=this.getIndex(e),i=X.find(".ul-categories");return!1!==t&&(this.list.splice(t,1),i.find(`[data-id="${e}"]`).remove(),i.find("li").eq(0).click(),!0)},update:function(){let e=K.val(),t=ee.val(),i=["","<option></option>"],s=this.list.map(function(e){return e.id});for(var a=0;a<this.list.length;a++)i[this.list[a].parent?0:1]+=`<option value="${this.list[a].id}">${this.list[a].title}</option>`;ee.html(i[0]),K.html(i[1]),this.list.length&&(ee.val(s.includes(t)?t:ee[0].selectedIndex>-1?this.list[ee[0].selectedIndex].id:""),K.val(s.includes(e)?e:K[0].selectedIndex>-1?this.list[K[0].selectedIndex].id:""))},updateActive:function(){let e=this.activeID();if(e){let t=this.getIndex(e),i=Z.find(".sb-language-switcher .sb-active").attr("data-language"),s={title:Z.find("#category-title").val(),description:Z.find("#category-description").val(),image:Z.find("#category-image").attr("data-value")};i?this.list[t].languages[i]=s:(s.id=SBF.stringToSlug(s.title),s.parent=Z.find("#category-parent").is(":checked"),s.languages=this.list[t].languages,this.list[t]=s,X.find(".ul-categories .sb-active").html(s.title+'<i class="sb-icon-delete"></i>').attr("data-id",s.id))}},clear:function(){Z.find("input, textarea").val(""),Z.find("#category-image").removeAttr("data-value style")},getIndex:function(e){for(var t=0;t<this.list.length;t++)if(this.list[t].id==e)return t;return!1},activeID:function(){return X.find(".ul-categories .sb-active").attr("data-id")},translations:{add:function(t,i=!1){nt.categories.updateActive(),i||(i=nt.categories.activeID());let s=nt.categories.getIndex(i);SBF.null(nt.categories.list[s].languages)&&(nt.categories.list[s].languages={}),nt.categories.list[s].languages[t]={title:"",description:"",image:""},Z.sbLanguageSwitcher(e.map(nt.categories.list[s].languages,function(e,t){return t}),"article-categories",t),nt.categories.clear()},delete:function(e,t=!1){let i=nt.categories.activeID();t||(t=nt.categories.activeID(i)),delete nt.categories.list[nt.categories.getIndex(t)].languages[e],nt.categories.show(i)}}},translations:{list:{},add:function(e,t=!1){t||(t=nt.activeID(!0));let i=this.get(t);i.push([e,!1]),this.list[t]=i,Q.sbLanguageSwitcher(i,"articles",e),nt.clear()},delete:function(e,t=!1){t||(t=nt.activeID(!0));let i=this.get(t);for(var s=0;s<i.length;s++)if(i[s][0]==e){nt.delete(i[s][1],e=>{!0===e&&(i.splice(s,1),this.list[t]=i,nt.show(t))});break}return!1},get:function(e){return e in this.list?this.list[e]:[]}}},ot={chart:!1,active_report:!1,active_date_range:!1,initChart:function(e,t="line",i=1){let s=[],a=[],n=SB_ADMIN_SETTINGS.color?[SB_ADMIN_SETTINGS.color]:["#049CFF","#74C4F7","#B9E5FF","#0562A0","#003B62","#1F74C4","#436786"];for(var o in e)s.push(e[o][0]),a.push(o);if("line"!=t&&s.length>6)for(var r=0;r<s.length;r++)n.push("hsl(210, "+Math.floor(100*Math.random())+"%, "+Math.floor(100*Math.random())+"%)");this.chart&&this.chart.destroy(),this.chart=new Chart(Te.find("canvas"),{type:t,data:{labels:a,datasets:s&&Array.isArray(s[0])?[{data:s.map(e=>e[0]),backgroundColor:"#13ca7e"},{data:s.map(e=>e[1]),backgroundColor:"#ca3434"}]:[{data:s,backgroundColor:"line"==t?SB_ADMIN_SETTINGS.color?"#cbcbcb82":"#028be530":n,borderColor:"line"==t?SB_ADMIN_SETTINGS.color?SB_ADMIN_SETTINGS.color:"#049CFF":"#FFFFFF",borderWidth:0}]},options:{legend:{display:!1},scales:{yAxes:[{ticks:{callback:function(e,t,s){return 1==i?e:2==i?new Date(1e3*e).toISOString().substr(11,8):e},beginAtZero:!0}}],xAxes:[{ticks:{beginAtZero:!0}}]},tooltips:{callbacks:{label:function(e,t){let a=e.index,n=t.datasets[e.datasetIndex].data[a];switch(i){case 1:return n;case 2:return new Date(1e3*s[a]).toISOString().substr(11,8);case 3:return n+"%";case 4:let e=Te.find(".sb-table tbody tr").eq(a).find("td");return e.eq(0).text()+" "+e.eq(1).text()}}},displayColors:!1}}})},initTable:function(e,t,i=!1){let s="<thead><tr>",a=t[Object.keys(t)[0]].length-1,n=[];for(r=0;r<e.length;r++)s+=`<th>${e[r]}</th>`;s+="</tr></thead><tbody>";for(var o in t)0!=t[o][a]&&n.push([o,t[o][a]]);i&&n.reverse();for(var r=0;r<n.length;r++)s+=`<tr><td><div>${n[r][0]}</div></td><td>${n[r][1]}</td></tr>`;s+="</tbody>",Te.find("table").html(s)},initReport:function(e=!1,t=!1){let i=Te.find(".sb-tab > .sb-content");t=SBF.null(t)?[!1,!1]:t.split(" - "),i.sbLoading(!0),e&&(this.active_report=e),this.active_report&&(this.active_date_range=t,this.getData(this.active_report,t[0],t[1],e=>{0==e?i.addClass("sb-no-results-active"):(i.removeClass("sb-no-results-active"),this.initChart(e.data,e.chart_type,e.label_type),this.initTable(e.table,e.data,e.table_inverse),Te.find(".sb-reports-title").html(e.title),Te.find(".sb-reports-text").html(e.description),Te.find(".sb-collapse-btn").remove(),We||l(Te.find(".sb-collapse"),Te.find("canvas").outerHeight()-135)),i.sbLoading(!1)}))},getData:function(e,t=!1,i=!1,s){SBF.ajax({function:"reports",name:e,date_start:t,date_end:i,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone},e=>{s(e)})},initDatePicker:function(){let e={ranges:{},locale:{format:"DD/MM/YYYY",separator:" - ",applyLabel:o("Apply"),cancelLabel:o("Cancel"),fromLabel:o("From"),toLabel:o("To"),customRangeLabel:o("Custom"),weekLabel:o("W"),daysOfWeek:[o("Su"),o("Mo"),o("Tu"),o("We"),o("Th"),o("Fr"),o("Sa")],monthNames:[o("January"),o("February"),o("March"),o("April"),o("May"),o("June"),o("July"),o("August"),o("September"),o("October"),o("November"),o("December")],firstDay:1},showCustomRangeLabel:!0,alwaysShowCalendars:!0,autoApply:!0,opens:k.hasClass("sb-rtl")?"left":"right"};e.ranges[o("Today")]=[moment(),moment()],e.ranges[o("Yesterday")]=[moment().subtract(1,"days"),moment().subtract(1,"days")],e.ranges[o("Last 7 Days")]=[moment().subtract(6,"days"),moment()],e.ranges[o("Last 30 Days")]=[moment().subtract(29,"days"),moment()],e.ranges[o("This Month")]=[moment().startOf("month"),moment().endOf("month")],e.ranges[o("Last Month")]=[moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")],Te.find("#sb-date-picker").daterangepicker(e).val("")},export:function(e){SBF.ajax({function:"reports-export",name:this.active_report,date_start:this.active_date_range[0],date_end:this.active_date_range[1],timezone:Intl.DateTimeFormat().resolvedOptions().timeZone},t=>{e(t)})},open:function(e){x.find(".sb-admin-nav #sb-reports").click(),setTimeout(()=>{Te.find("#"+e).click().get(0).scrollIntoView()},300)}},rt={real_time:null,datetime_last_user:"2000-01-01 00:00:00",sorting:["creation_time","DESC"],user_types:["visitor","lead","user"],user_main_fields:["id","first_name","last_name","email","password","profile_image","user_type","creation_time","token","last_activity","department"],search_query:"",init:!1,busy:!1,table_extra:!1,history:[],get:function(e,t=!1,i=!1){let s=[];for(var n=0;n<q.length;n++)s.push(q.eq(n).find("li.sb-active").data("value"));i||a(U),SBF.ajax({function:t?"get-online-users":"get-users",sorting:t?this.sorting[0]:this.sorting,pagination:!!i&&je,user_types:this.user_types,search:this.search_query,extra:this.table_extra,department:s[0],source:s[1],tag:s[2]},t=>{e(t),U.sbLoading(!1)})},filter:function(e){e="all"==e?["visitor","lead","user"]:"agent"==e?["agent","admin"]:[e],this.user_types=e,je=1,Ge=1,this.get(e=>{this.populate(e)},"online"==e[0])},sort:function(e,t="DESC"){this.sorting=[e,t],je=1,Ge=1,this.get(e=>{this.populate(e)})},search:function(t){c(t,(t,i)=>{je=1,Ge=1,this.search_query=t,this.get(t=>{this.user_types=["visitor","lead","user"],this.populate(t),e(i).sbLoading(!1),P.find("li").sbActive(!1).eq(0).sbActive(!0)})})},populate:function(t){let i="",s=t.length;if(s)for(var a=0;a<s;a++)i+=this.getRow(new SBUser(t[a],t[a].extra));else i=`<p class="sb-no-results">${o("No users found.")}</p>`;U.parent().scrollTop(0),U.find("tbody").html(i),this.user_types.includes("agent")&&SBF.ajax({function:"get-online-users",agents:!0},t=>{let i=[];for(var s=0;s<t.length;s++)i.push(t[s].id);U.find("[data-user-id]").each(function(){e(this).find(".sb-td-profile").addClass("sb-"+(i.includes(e(this).attr("data-user-id"))?"online":"offline"))})})},update:function(){if(!this.busy){let e=["user","visitor","lead","agent"],t=e.includes(this.user_types[0])&&!this.search_query,i=P.find(".sb-active").data("type");"online"==i?this.filter(i):(this.busy=!0,SBF.ajax({function:"get-new-users",datetime:this.datetime_last_user},s=>{let a=s.length;if(this.busy=!1,a>0){let o="";for(n=0;n<a;n++){let e=new SBUser(s[n]);Re[e.id]=e,this.updateMenu("add",e.type),t&&(o+=this.getRow(e))}if(t&&(U.find("tbody").prepend(o),e.includes(i))){let t="";for(var n=0;n<e.length;n++)t+=e[n]==i?"":`[data-user-type="${e[n]}"],`;U.find(t.slice(0,-1)).remove()}this.datetime_last_user=s[0].creation_time}}))}},getRow:function(e){if(e instanceof SBUser){let i="";for(var t=0;t<this.table_extra.length;t++){let s=this.table_extra[t];i+=`<td class="sb-td-${s}">${this.user_main_fields.includes(s)?e.get(s):e.getExtra(s)}</td>`}return`<tr data-user-id="${e.id}" data-user-type="${e.type}"><td><input type="checkbox" /></td><td class="sb-td-profile"><a class="sb-profile"><img loading="lazy" src="${e.image}" /><span>${e.name}</span></a></td>${i}<td class="sb-td-email">${e.get("email")}</td><td class="sb-td-ut">${o(e.type)}</td><td>${SBF.beautifyTime(e.get("last_activity"),!0)}</td><td>${SBF.beautifyTime(e.get("creation_time"))}</td></tr>`}return SBF.error("User not of type SBUser","SBUsers.getRow"),!1},updateRow:function(e){let t=U.find(`[data-user-id="${e.id}"]`);if(t.length){let i=P.find(".sb-active").data("type");if(e.type==i||"admin"==e.type&&"agent"==i||"all"==i)t.replaceWith(this.getRow(e));else{let i=k.find(`[data-type="${"admin"==e.type?"agent":e.type}"] span`),s=parseInt(i.attr("data-count"));i.html(s+1).attr("data-count",s+1),t.remove()}}else U.find("tbody").append(this.getRow(e))},updateMenu:function(e="all",t=!1){let i=["all","user","lead","visitor"];"all"==e?SBF.ajax({function:"count-users"},e=>{for(var t=0;t<i.length;t++)this.updateMenuItem("set",i[t],e[i[t]])}):this.updateMenuItem(e,t)},updateMenuItem:function(e="set",t=!1,i=1){let s=P.find(`[data-type="${t}"] span`),a=["user","lead","visitor"];"set"!=e&&(i=parseInt(s.attr("data-count"))+1*("add"==e?1:-1)),s.html(`(${i})`).attr("data-count",i),i=0;for(var n=0;n<a.length;n++)i+=parseInt(P.find(`[data-type="${a[n]}"] span`).attr("data-count"));P.find('[data-type="all"] span').html(`(${i})`).attr("data-count",i)},delete:function(e){if(a(U),Array.isArray(e)){if(SB_ADMIN_SETTINGS.cloud&&!(e=SBCloud.removeAdminID(e)).length)return;SBF.ajax({function:"delete-users",user_ids:e},()=>{for(var i=0;i<e.length;i++)delete Re[e[i]],U.find(`[data-user-id="${e[i]}"]`).remove(),C.find(`[data-user-id="${e[i]}"]`).remove(),SBF.event("SBUserDeleted",e[i]);0==U.find("[data-user-id]").length&&this.filter(P.find(".sb-active").data("type")),t("Users deleted"),this.updateMenu(),U.sbLoading(!1)})}else Re[e].delete(()=>{let i=C.find(`[data-user-id="${e}"]`);s().id==e&&s(!1),i.sbActive()&&(SBChat.conversation=!1,setTimeout(()=>{lt.clickFirst()},300)),delete Re[e],U.find(`[data-user-id="${e}"]`).remove(),i.remove(),k.sbHideLightbox(),t("User deleted"),this.updateMenu(),U.sbLoading(!1)})},startRealTime:function(){SBPusher.active||(this.stopRealTime(),this.real_time=setInterval(()=>{this.update()},1e3))},stopRealTime:function(){clearInterval(this.real_time)},csv:function(){SBF.ajax({function:"csv-users",users_id:rt.getSelected()},e=>{_(e,"sb-export-users-close","Users exported"),window.open(e)})},updateUsersActivity:function(){SBF.updateUsersActivity(Qe?SB_ACTIVE_AGENT.id:-1,s()?s().id:-1,function(e){rt.setActiveUserStatus("online"==e)})},setActiveAgentStatus:function(e=!0){let t=e?"online":"offline";Qe=e,x.find('[data-value="status"]').html(o(SBF.slugToString(t))).attr("class","sb-"+t),SBPusher.active&&(e?(SBPusher.presence(),"queue"!=SB_ADMIN_SETTINGS.routing&&"routing"!=SB_ADMIN_SETTINGS.routing||SBF.ajax({function:"assign-conversations-active-agent",is_queue:"queue"==SB_ADMIN_SETTINGS.routing},()=>{lt.update()})):SBPusher.presenceUnsubscribe()),SB_ADMIN_SETTINGS.reports_disabled||SBF.ajax({function:"reports-update",name:t})},setActiveUserStatus:function(e=!0){let t=A.find(".sb-conversation .sb-top > .sb-labels");t.find(".sb-status-online").remove(),e&&t.prepend(`<span class="sb-status-online">${o("Online")}</span>`),SBChat.user_online=e},onlineUserNotification:function(e){let t=SB_ADMIN_SETTINGS.online_users_notification;if(t){let i=e.info.first_name+" "+e.info.last_name,s=this.userProfileImage(e.info.profile_image);SB_ADMIN_SETTINGS.push_notifications&&e.info.id&&!this.history.includes(e.info.id)?SBF.ajax({function:"push-notification",title:t,message:i,icon:s,interests:SB_ACTIVE_AGENT.id,user_id:e.info.id}):lt.desktop_notifications&&SBChat.desktopNotification(t,i,s,!1,e.info.id),this.history.push(e.info.id)}},userProfileImage:function(e){return!e||e.indexOf("user.svg")?SB_ADMIN_SETTINGS.notifications_icon:e},getSelected:function(){let t=[];return U.find("tr").each(function(){e(this).find('td input[type="checkbox"]').is(":checked")&&t.push(e(this).attr("data-user-id"))}),t},getExtraDetailsList:function(t=!1){return V.find(".sb-additional-details .sb-edit-box > "+(t?".sb-custom-detail":".sb-input")).map(function(){return[[e(this).attr("id"),e(this).find("span").html().trim()]]}).get()}},lt={real_time:null,datetime_last_conversation:!1,user_typing:!1,desktop_notifications:!1,flash_notifications:!1,busy:!1,busy_2:!1,is_search:!1,menu_count_ajax:!1,previous_editor_text:!1,open:function(e=-1,t){-1!=e&&this.openConversation(e,t),k.sbHideLightbox(),x.find(".sb-admin-nav a").sbActive(!1).parent().find("#sb-conversations").sbActive(!0),k.find(" > main > div").sbActive(!1),A.sbActive(!0).find(".sb-board").removeClass("sb-no-conversation"),Ie.find(" > p").attr("data-id","").attr("data-value","").html(o("None")),this.notes.update([]),this.tags.update([]),this.startRealTime()},openConversation:function(i,a=!1,n=!0){if(SBChat.label_date.sbActive(!1),SBChat.label_date_show=!1,this.busy_2!=i)if(!1===a&&i)this.busy_2=i,SBF.ajax({function:"get-user-from-conversation",conversation_id:i},e=>{this.busy_2=!1,SBF.null(e.id)?SBF.error("Conversation not found","SBAdmin.openConversation"):this.openConversation(i,e.id,n)});else{let r=SBF.null(Re[a])||!Re[a].details.email,l=A.find(`[data-conversation-id="${i}"]`),c=C.find("li");T.html(""),T.sbLoading(!0),r?(s(new SBUser({id:a})),s().update(()=>{Re[a]=s(),this.updateUserDetails()})):(s(Re[a]),this.updateCurrentURL()),SBPusher.active&&(SBPusher.event("client-typing",e=>{e.user_id==s().id&&(lt.typing(!0),clearTimeout(me),me=setTimeout(()=>{lt.typing(!1)},1e3))}),SBPusher.event("new-message",()=>{SBChat.update()}),SBPusher.event("agent-active-conversation-changed",e=>{e.previous_conversation_id==i&&A.find(".sb-conversation-busy").remove()},"agents"),SBPusher.event("init",e=>{lt.updateCurrentURL(e.current_url)}),SBPusher.event("message-status-update",e=>{SBChat.conversation&&SBChat.conversation.updateMessagesStatus(e.message_ids)})),SB_ADMIN_SETTINGS.smart_reply&&R.html(""),c.sbActive(!1),SB_ADMIN_SETTINGS.departments_show||c.attr("data-color",""),l.sbActive(!0),-1!=i?(this.busy_2=i,s().getFullConversation(i,a=>{let r=a.status_code,d=F.eq(0),u=d.find(".sb-active").attr("data-value");if(this.busy_2=!1,SBChat.setConversation(a),SBChat.populate(),this.setReadIcon(r),A.find(".sb-conversation-busy").remove(),this.updateUserDetails(),A.find(".sb-top > a").html(a.get("title")),dt.must_translate=SB_ADMIN_SETTINGS.translation&&s().language&&SB_ADMIN_SETTINGS.active_agent_language!=s().language,dt.must_translate){let e=[],t=[],s=[],n=[];for(f=0;f<a.messages.length;f++){let i=a.messages[f];i.message&&(i.payload("original-message")&&(!i.payload("original-message-language")||i.payload("original-message-language")==SB_ADMIN_SETTINGS.active_agent_language)||i.payload("translation")&&(!i.payload("translation-language")||i.payload("translation-language")==SB_ADMIN_SETTINGS.active_agent_language)?n.push(i):(e.push(i.message),t.push(i.id),s.push(i.get("user_type"))))}e.length?st.dialogflow.translate(e,SB_ADMIN_SETTINGS.active_agent_language,e=>{if(e)for(var i=0;i<e.length;i++){let a=SBChat.conversation.getMessage(t[i]);a.payload("translation",e[i]),a.payload("translation-language",SB_ADMIN_SETTINGS.active_agent_language),this.openConversation_2(t[i],s[i])}SB_ADMIN_SETTINGS.smart_reply&&this.openConversation_1(SBChat.conversation,R)},t,i):SB_ADMIN_SETTINGS.smart_reply&&this.openConversation_1(SBChat.conversation,R);for(f=0;f<n.length;f++)this.openConversation_2(n[f].id,n[f].get("user_type"))}if(Ie.length){let e=!!a.get("department")&&this.getDepartments(a.get("department")),t=e?e["department-color"]:"";SB_ADMIN_SETTINGS.departments_show||c.attr("data-color",""),w(i).attr("data-color",t),Ie.find(" > p").attr("data-id",e?e["department-id"]:"").attr("data-value",t).html(e?e["department-name"]+"<span></span>":o("None"))}let p=A.find("#conversation-agent");if(p.length){let e=p.find(`[data-id="${a.get("agent_id")}"]`);p.find(" > p").attr("data-value",e.data("id")).html(e.html())}[1,2,"1","2"].includes(r)&&(r=0),u==r||e($).find(".sb-search-btn").sbActive()||lt.filters()[1]||lt.filters()[3]||(d.find(`[data-value="${r}"]`).click(),d.find("ul").sbActive(!1)),We&&this.mobileOpenConversation(),l.length||u!=r&&(0!=u||1!=r)||C.prepend(lt.getListCode(a)),C.find("li").sbActive(!1),l.sbActive(!0),n&&this.scrollTo(),this.notificationsCounterReset(i,l),T.sbInitTooltips();let h=a.get("busy");h&&A.find(".sb-editor > .sb-labels").prepend(`<span data-agent="${h.id}" class="sb-status-warning sb-conversation-busy">${h.first_name} ${h.last_name} ${o("is replying to this conversation")}</span>`),st.is("woocommerce")&&st.woocommerce.conversationPanel(),st.is("ump")&&st.ump.conversationPanel(),st.is("perfex")&&st.perfex.conversationPanel(),st.is("whmcs")&&st.whmcs.conversationPanel(),st.is("aecommerce")&&st.aecommerce.conversationPanel(),st.is("martfury")&&st.martfury.conversationPanel(),st.is("armember")&&st.armember.conversationPanel(),st.is("zendesk")&&st.zendesk.conversationPanel(),st.is("opencart")&&st.opencart.conversationPanel(),s()&&SB_ADMIN_SETTINGS.cloud&&(SBCloud.shopify.panel&&SBCloud.shopify.panel.html(""),SBCloud.shopify.conversationPanel()),this.notes.update(a.details.notes),this.tags.update(a.details.tags),this.attachments(),SB_ADMIN_SETTINGS.smart_reply&&!dt.must_translate&&this.openConversation_1(a,R);for(f=a.messages.length-1;f>0;f--){let e=a.messages[f].get("payload");if(e.rating){A.find(".sb-profile-list > ul").append(`<li data-id="rating"><i class="sb-icon sb-icon-${1==e.rating?"like":"dislike"}"></i><span>${o("User rating")}</span><label>${o(1==e.rating?"Helpful":"Not helpful")}${e.message?" - "+e.message:""}</label></li>`);break}}if("em"==a.get("source")&&this.cc(a.get("extra").split(",")),"wa"==a.get("source")){let e=a.getLastUserMessage();e&&new Date-new Date(e.get("creation_time").replace(" ","T"))>864e5&&t(o("You can't send a WhatsApp message more than 24 hours after the user's last message—use a template instead.")+'<i id="sb-whatsapp-alert-btn" class="sb-icon-social-wa"</i>',"info")}if(k.on("click","#sb-whatsapp-alert-btn",function(){lt.showDirectMessageBox("whatsapp",[s().id])}),s().getConversations(function(e){A.find(".sb-user-conversations").html(1==e.length?"":s().getConversationsCode(e)).prev().setClass("sb-hide",1==e.length)}),this.is_search){let t=$.find(".sb-search-btn input").val();for(var f=0;f<a.messages.length;f++)if(a.messages[f].message.toLowerCase().includes(t)){let t=a.messages[f].id;setTimeout(()=>{let i="";T.find("> div").each(function(){let s=e(this);s.attr("data-id")==t?(s.addClass("sb-highlight"),setTimeout(()=>{s.removeClass("sb-highlight")},3600),SBChat.label_date.html(i),SBChat.label_date_show=!0,s.index()?s.prev()[0].scrollIntoView():s[0].scrollIntoView()):s.hasClass("sb-label-date")&&(i=s.html())})},300)}}T.sbLoading(!1)})):(SBChat.clear(),C.find("li").sbActive(!1),T.sbLoading(!1),A.find(".sb-top > a").html(""),r||this.updateUserDetails()),A.find(".sb-board").removeClass("sb-no-conversation"),rt.updateUsersActivity(),this.startRealTime(),SBF.getURL("conversation")!=i&&-1!=i&&f("?conversation="+i)}},openConversation_1:function(e,t){let i=e.getLastUserMessage();t.html(""),i&&i.payload("sb-human-takeover")&&(i=e.getLastUserMessage(i.get("index"))),i&&st.dialogflow.smartReply(i.message)},openConversation_2:function(e,t){let i=SBChat.conversation.getMessage(e);if(i){let s=SBF.isAgent(t)&&"bot"!=t;T.find(`[data-id="${e}"]`).replaceWith(i.getCode()),T.find(`[data-id="${e}"] .sb-menu`).prepend(`<li data-value="${s?"translation":"original"}">${o(s?"View translation":"View original message")}</li>`)}},populate:function(e,t,i){this.openConversation(e,t,i)},populateList:function(e){let t="";Ce=[];for(var i=0;i<e.length;i++)t+=this.getListCode(e[i]),Ce.push(new SBConversation([new SBMessage(e[i])],e[i]));t||(t=`<p class="sb-no-results">${o("No conversations found.")}</p>`),C.html(t),this.updateMenu(),SBF.event("SBAdminConversationsLoaded",{conversations:e})},update:function(){if(!this.busy&&0==F.eq(0).find("p").attr("data-value")&&this.datetime_last_conversation){let t=lt.filters();if(this.busy=!0,SBF.ajax({function:"get-new-conversations",datetime:this.datetime_last_conversation,department:t[1],source:t[2],tag:t[3]},t=>{if(this.busy=!1,t.length){let n,o="",r="",l=SBChat.conversation?SBChat.conversation.id:-1,c=!1,d=[];t[0].last_update_time&&(this.datetime_last_conversation=t[0].last_update_time);for(i=0;i<t.length;i++)if(!d.includes(t[i].id)){let e=new SBMessage(t[i]),a=new SBConversation([e],t[i]),u=a.status_code,p=2==u||SB_ADMIN_SETTINGS.order_by_date&&(0==u||1==u),h=a.user_id,f=a.id,g=w(f),b=g.length,v=e.get("user_type"),m=a.get("message"),_=l==f,S=!SBF.isAgent(v);if(!m&&e.payload("preview")&&(m=e.payload("preview")),p&&(!_||"hidden"==SBF.visibility_status)&&(S||e.payload("human-takeover-message-confirmation"))){let t=SBF.storage("notifications-counter");t||(t={}),t[f]||(t[f]=[]),t[f].includes(e.id)||(t[f].push(e.id),SBF.storage("notifications-counter",t))}let y=this.getListCode(a,null);_?(this.updateUserDetails(),b?(m&&g.replaceWith(y),this.setStatus(u,f)):c=!0):b&&(Ce[g.index()]=a,w(f).remove()),h in Re||(Re[h]=new SBUser({id:h,first_name:a.get("first_name"),last_name:a.get("last_name"),profile_image:a.get("profile_image"),user_type:v})),_&&b||(p?(o+=y,Ce.unshift(a)):0!=u&&1!=u||((n=C.find('[data-conversation-status="2"]').last()).length?(Ce.splice(n.index()+1,0,a),r+=y):o+=y),s()&&h==s().id&&s().getConversations(e=>{A.find(".sb-user-conversations").html(s().getConversationsCode(e))}),SBF.event("SBAdminNewConversation",{conversation:a})),!s()||"update-user"!=e.payload("event")&&Re[h].type==v||s().update(()=>{this.updateUserDetails(),Re[s().id]=s()});let k=e.payload("preview");if(!SBChat.tab_active&&2==u&&(S||k)&&(m||a.getAttachments().length||k)){if(this.desktop_notifications){let e=[Re[h].nameBeautified,Re[h].image];SBChat.desktopNotification(e[0],k||m,e[1],f,h)}this.flash_notifications&&SBChat.flashNotification(),SBChat.audio&&SB_ADMIN_SETTINGS.sound&&SBChat.playSound()}d.push(f)}lt.is_search||(o&&C.prepend(o),r&&e(r).insertAfter(n),c&&this.scrollTo(),this.updateMenu());for(var i=0;i<SBChat.notifications.length;i++){let e=!1;for(var a=0;a<Ce.length;a++)if(Ce[a].id==SBChat.notifications[i][0]){e=2==Ce[a].status_code;break}e||(SBChat.notifications.splice(i,1),i--)}}}),SB_ADMIN_SETTINGS.assign_conversation_to_agent||SB_ACTIVE_AGENT.department){let t=C.find(" > li").map(function(){return e(this).attr("data-conversation-id")}).get();t.length&&SBF.ajax({function:"check-conversations-assignment",conversation_ids:t,agent_id:!!SB_ADMIN_SETTINGS.assign_conversation_to_agent&&SB_ACTIVE_AGENT.id,department:SB_ACTIVE_AGENT.department},e=>{if(e)for(var t=0;t<e.length;t++)w(e[t]).remove()})}}},updateMenu:function(){let e=C.find('[data-conversation-status="2"]').length,t=F.eq(0),i=t.find(" > p span");if(100==e||this.menu_count_ajax||SB_ADMIN_SETTINGS.order_by_date){let e=t.find("li.sb-active").data("value");this.menu_count_ajax=!0,SBF.ajax({function:"count-conversations",status_code:0==e?2:e},e=>{i.html(`(${e})`)})}else i.html(`(${e})`)},messageMenu:function(e,t=!1,i=!1){return`<i class="sb-menu-btn sb-icon-menu"></i><ul class="sb-menu">${(t&&SB_ADMIN_SETTINGS.chatbot_features?`<li data-value="bot">${o("Train chatbot")}</li>`:"")+(e&&!SB_ADMIN_SETTINGS.supervisor&&SB_ADMIN_SETTINGS.allow_agent_delete_message||SB_ADMIN_SETTINGS.supervisor&&SB_ADMIN_SETTINGS.allow_supervisor_delete_message?`<li data-value="delete">${o("Delete")}</li>`:"")+(i?`<li data-value="reply">${o("Reply")}</li>`:"")}</ul>`},updateUserDetails(){s()&&(A.find(`[data-user-id="${s().id}"] .sb-name`).html(s().name),A.find(".sb-top > a").html(SBChat.conversation?SBChat.conversation.title:s().name),I.find(".sb-profile").setProfile(),ct.populate(s(),A.find(".sb-profile-list")))},setReadIcon(e){let t=2==e;A.find('.sb-top [data-value="read"],.sb-top [data-value="unread"]').sbActive([0,1,2].includes(parseInt(e))).attr("data-value",t?"read":"unread").attr("data-sb-tooltip",o(t?"Mark as read":"Mark as unread")).parent().sbInitTooltips().find("i").attr("class",t?"sb-icon-check-circle":"sb-icon-circle")},setStatus(e,t=!1,i=!1){t||(t=SBChat.conversation.id),t&&(Ce[w(t).index()].set("status_code",e),this.setReadIcon(e),i&&C.find(`[data-conversation-id="${t}"]`).attr("data-conversation-status",e))},getListCode:function(e,t){e instanceof SBConversation||(e=new SBConversation([new SBMessage(e)],e));let i=e.getCode(!0),s=SB_ADMIN_SETTINGS.tags_show?e.get("tags"):"",a=e.get("department"),n="",o=e.get("last_update_time"),r=SBChat.conversation&&SBChat.conversation.id==e.id;return SBF.null(t)&&(t=e.status_code),s&&(s=lt.tags.codeLeft(s)),SBChat.conversation&&r&&"hidden"!=SBF.visibility_status||((n=SBF.storage("notifications-counter"))&&n[e.id]&&n[e.id].length?2==t?n=`<span class="sb-notification-counter">${n[e.id].length}</span>`:(n[e.id]=[],SBF.storage("notifications-counter",n),n=""):n=""),o||(o=e.getLastMessage()?e.getLastMessage().get("creation_time"):e.get("creation_time")),`<li${r?' class="sb-active"':""} data-user-id="${e.get("user_id")}" data-conversation-id="${e.id}" data-conversation-status="${t}"${a?` data-department="${a}"${SB_ADMIN_SETTINGS.departments_show?' data-color="'+this.getDepartments(a)["department-color"]+'"':""}`:""}${SBF.null(e.get("source"))?"":` data-conversation-source="${e.get("source")}"`}>${""+n}<div class="sb-profile"><img loading="lazy" src="${e.get("profile_image")}"><span class="sb-name">${e.get("first_name")+" "+e.get("last_name")}</span>${s}<span class="sb-time">${SBF.beautifyTime(o)}</span></div><p>${i}</p></li>`},startRealTime:function(){SBPusher.active||(this.stopRealTime(),this.real_time=setInterval(()=>{this.update(),this.updateCurrentURL()},1e4),SBChat.startRealTime())},stopRealTime:function(){clearInterval(this.real_time),SBChat.stopRealTime()},transcript:function(e,t,i=!1,a=!1){SBF.ajax({function:"transcript",conversation_id:e},e=>{"email"==i?s()&&s().id==t&&!s().get("email")||SBChat.sendEmail(SB_ADMIN_SETTINGS.transcript_message,[[e,e]],t,t=>{a&&a(!0===t?e:t)}):(a&&a(e),window.open(e))})},typing:function(e){e?(SBChat.user_online||rt.setActiveUserStatus(!0),this.user_typing||(A.find(".sb-conversation .sb-top > .sb-labels").append('<span class="sb-status-typing">'+o("Typing")+"</span>"),this.user_typing=!0)):this.user_typing&&(A.find(".sb-conversation .sb-top .sb-status-typing").remove(),this.user_typing=!1)},scrollTo:function(){let e=C.find(".sb-active"),t=e.length?e[0].offsetTop:0;C.parent().scrollTop(t-(We?120:80))},search:function(t){t&&c(t,(t,i)=>{if(Me=1,t.length>1)SBF.ajax({function:"search-conversations",search:t},t=>{lt.populateList(t),e(i).sbLoading(!1),this.scrollTo(),this.is_search=!0});else{let t=lt.filters();De=1,SBF.ajax({function:"get-conversations",status_code:t[0],department:t[1],source:t[2],tag:t[3]},t=>{lt.populateList(t),e(i).sbLoading(!1),this.is_search=!1,SBChat.conversation&&(w(SBChat.conversation.id).sbActive(!0),this.scrollTo())})}})},notificationsCounterReset:function(e,t=!1){let i=SBF.storage("notifications-counter");if(i&&i[e]){t||(t=C.find('[data-conversation-id="'+e+'"]'));let s=t.find(".sb-notification-counter");i[e]=[],SBF.storage("notifications-counter",i),s.addClass("sb-fade-out"),setTimeout(()=>{s.remove()},200)}},updateCurrentURL:function(e=!1){e?this.ucurl(e):SBChat.user_online&&s()&&s().getExtra("current_url")&&SBF.ajax({function:"current-url"},e=>{e&&this.ucurl(e)})},ucurl(e){let t=s().getExtra("current_url");e=b(e),A.find('.sb-profile-list [data-id="current_url"] label').attr("data-value",e).html(e),t&&(t.value=e,s().setExtra("current_url",t))},assignDepartment:function(e,t,i){SBF.ajax({function:"update-conversation-department",conversation_id:e,department:t,message:SBChat.conversation.getLastMessage().message},e=>{i(e)})},assignAgent:function(e,t,i=!1){SBF.ajax({function:"update-conversation-agent",conversation_id:e,agent_id:t,message:SBChat.conversation.getLastMessage().message},e=>{i&&i(e)})},setActiveDepartment:function(e){if(SBChat.conversation&&SBChat.conversation.get("department")==e)return;let i=!!e&&this.getDepartments(e),s=i?i["department-color"]:"",a=w(SBChat.conversation.id),n=lt.filters()[1];Ie.find(" > p").attr("data-id",i?i["department-id"]:"").attr("data-value",s).html((i?i["department-name"]:o("None"))+"<span></span>").next().sbActive(!1),SBChat.conversation.set("department",e),n&&n!=e||"agent"==SB_ACTIVE_AGENT.user_type&&SB_ACTIVE_AGENT.department&&SB_ACTIVE_AGENT.department!=e?(a.remove(),lt.clickFirst()):a.attr("data-color",s),t("Department updated. The agents have been notified.")},getDepartments:function(e=!1){if(e){for(var t=0;t<SB_ADMIN_SETTINGS.departments.length;t++)if(SB_ADMIN_SETTINGS.departments[t]["department-id"]==e)return SB_ADMIN_SETTINGS.departments[t];return!1}return SB_ADMIN_SETTINGS.departments},setActiveAgent:function(e){let i=A.find("#conversation-agent"),s=i.find(`[data-id="${e}"]`);SBChat.conversation.set("agent_id",e),i.find(" > p").attr("data-value",s.data("id")).html(s.html()).next().sbActive(!1),"agent"!=SB_ACTIVE_AGENT.user_type||SB_ADMIN_SETTINGS.assign_conversation_to_agent&&!e||(w(SBChat.conversation.id).remove(),lt.clickFirst()),e&&t("Agent assigned. The agent has been notified.")},mobileOpenConversation:function(){A.find(".sb-admin-list").sbActive(!1),B.sbActive(!0),x.addClass("sb-hide")},mobileCloseConversation:function(){C.find("li.sb-active").sbActive(!1),A.find(".sb-admin-list").sbActive(!0),A.find(".sb-conversation,.sb-user-details").removeClass("sb-active"),k.find('.sb-menu-mobile [data-value="panel"]').sbActive(!1),x.removeClass("sb-hide"),window.history.replaceState({},document.title,SBF.URL())},clickFirst:function(e=!1){e||(e=C.find("li:first-child")),e.length?(e.click(),lt.scrollTo()):(A.find(".sb-board").addClass("sb-no-conversation"),C.find("li").length||C.html(`<p class="sb-no-results">${o("No conversations found.")}</p>`),SBF.getURL("conversation")&&window.history.replaceState({},document.title,SBF.URL().replace("?conversation="+SBF.getURL("conversation"),"")))},savedReplies:function(t,i){let s=i.charAt(t.selectionStart-1),a=i.substr(0,i.lastIndexOf("#"));if("#"==s){if(i.length>1&&"#"==i.charAt(t.selectionStart-2))return e(t).val(a.substr(0,a.length-1)),B.find(".sb-btn-saved-replies").click();SBChat.editor_listening=!0}if(SBChat.editor_listening&&" "==s){let s=i.substr(i.lastIndexOf("#")+1).replace(" ","");SBChat.editor_listening=!1;for(var n=0;n<Ne.length;n++)if(Ne[n]["reply-name"]==s)return void e(t).val(a+Ne[n]["reply-text"])}},attachments:function(){if(L.length){let i=SBChat.conversation.getAttachments(),s="",a="",n=[];for(t=i.length-1;t>-1;t--){let e=SBF.getFileType(i[t][1]);s+=`<a href="${i[t][1]}" target="_blank"><i class="sb-icon sb-icon-download"></i>${i[t][0]}</a>`,n.includes(e)||n.push(e)}if(i.length>4&&n.length>1){a=`<div id="sb-attachments-filter" class="sb-select"><p>${o("All")}</p><ul><li data-value="">${o("All")}</li>`;for(var t=0;t<n.length;t++)a+=`<li data-value="${n[t]}">${o(SBF.slugToString(n[t])+"s")}</li>`;a+="</ul></div>"}e(L).html(s?`<h3${a?' class="sb-flex"':""}>${o("Attachments")}${a}</h3><div class="sb-list-items sb-list-links sb-list-icon">${s}</div>`:""),l(L,160)}},filters:function(){let e=[];for(var t=0;t<F.length;t++)e.push(F.eq(t).find("li.sb-active").data("value"));return e},notes:{busy:!1,add:function(e,t,i,s,a=!1,n=!1){SBF.ajax({function:n?"update-note":"add-note",conversation_id:e,user_id:t,note_id:n,name:i,message:s},e=>{a&&a(e)})},update:function(e,t=!1){if(N.length){let s="",a=N.find(" > div");if(e){for(var i=0;i<e.length;i++){let t=e[i];s+=`<div data-id="${t.id}"><span${SB_ADMIN_SETTINGS.notes_hide_name?' class="sb-noname-note"':""}>${SB_ADMIN_SETTINGS.notes_hide_name?"":t.name}${SB_ACTIVE_AGENT.id==t.user_id?'<i class="sb-edit-note sb-icon-edit"></i><i class="sb-delete-note sb-icon-close"></i>':""}</span><span class="sb-note-text">${t.message.replace(/\n/g,"<br>")}</span></div>`}s=s.autoLink({target:"_blank"})}t?a.append(s):a.html(s),a.attr("style",""),N.find(".sb-collapse-btn").remove(),l(N,155),this.busy=!1}},delete:function(e,t,i=!1){this.busy||(this.busy=!0,SBF.ajax({function:"delete-note",conversation_id:e,note_id:t},e=>{this.busy=!1,i&&i(e)}))}},tags:{busy:!1,update:function(e){if(E.length){let i="",s=E.find(" > div");for(var t=0;t<e.length;t++){let s=this.get(e[t]);s&&(i+=this.code(s))}s.html(i),this.busy=!1}},get:function(e){for(var t=0;t<SB_ADMIN_SETTINGS.tags.length;t++)if(e==SB_ADMIN_SETTINGS.tags[t]["tag-name"])return SB_ADMIN_SETTINGS.tags[t]},getAll:function(e=[]){let t="";for(var i=0;i<SB_ADMIN_SETTINGS.tags.length;i++)t+=this.code(i,e.includes(SB_ADMIN_SETTINGS.tags[i]["tag-name"]));return t},code:function(e,t){let i=isNaN(e)?e:SB_ADMIN_SETTINGS.tags[e];if(i){let e=i["tag-name"];return`<span data-value="${e}" data-color="${i["tag-color"]}"${t?' class="sb-active"':""}>${o(e)}</span>`}return""},codeLeft:function(e){let t='<span class="sb-tags-area">';for(var i=0;i<e.length;i++){let s=this.get(e[i]);s&&(t+=`<i class="sb-icon-tag" data-color-text="${s["tag-color"]}"></i>`)}return t+"</span>"}},showDirectMessageBox:function(e,t=[]){if("whatsapp"==e)m(t);else{let i="custom_email"==e;SBForm.clear(D),D.find(".sb-direct-message-users").val(t.length?t.join(","):"all"),D.find(".sb-bottom > div").html(""),D.find(".sb-top-bar > div:first-child").html(o(`Send a ${{sms:"text message",custom_email:"email",message:"chat message"}[e]}`)),D.find(".sb-loading").sbLoading(!1),D.find(".sb-direct-message-subject").sbActive(i).find("input").attr("required",i),D.attr("data-type",e),D.sbShowLightbox()}},getDeliveryFailedMessage:function(e){return`<i class="sb-icon-warning sb-delivery-failed" data-sb-tooltip="${o("Message not delivered to {R}.").replace("{R}",st.getName(e))}" data-sb-tooltip-init></i>`},cc:function(e){let t="";for(var i=0;i<e.length;i++)e[i]&&(t+=`<li data-id="cc"><i class="sb-icon sb-icon-envelope"></i><span>CC</span><label>${e[i]}</label></li>`);A.find('[data-id="cc"]').remove(),A.find('[data-id="email"]').attr("data-em","true").after(t)}},ct={getAll:function(e){return SBForm.getAll(e)},get:function(e){return SBForm.get(e)},set:function(e,t){return SBForm.set(e,t)},show:function(e){n(),s(new SBUser({id:e})),s().update(()=>{this.populate(s(),O.find(".sb-profile-list")),O.find(".sb-profile").setProfile(),s().getConversations(t=>{let i=s().type,a=s().getConversationsCode(t);SBF.isAgent(i)&&this.agentData(),O.find(".sb-user-conversations").html(a).prev().setClass("sb-hide",!a),O.find(".sb-top-bar [data-value]").sbActive(!1),SBF.null(s().get("email"))||O.find('.sb-top-bar [data-value="email"]').sbActive(!0),s().getExtra("phone")&&(SB_ADMIN_SETTINGS.sms&&O.find('.sb-top-bar [data-value="sms"]').sbActive(!0),O.find('.sb-top-bar [data-value="whatsapp"]').sbActive(!0)),this.boxClasses(O,i),O.attr("data-user-id",s().id).sbShowLightbox(),n(!1,!1),SBF.event("SBProfileBoxOpened",{user_id:e})}),Re[e]=s(),SBF.getURL("user")==e||SBF.getURL("conversation")||f("?user="+e)})},showEdit:function(e){if(!(e instanceof SBUser))return SBF.error("User not of type SBUser","SBUsers.showEdit"),!1;{let i=V.find("#password input"),s=e.type,a=V.find("#user_type select");V.removeClass("sb-user-new").attr("data-user-id",e.id),V.find(".sb-top-bar .sb-save").html(`<i class="sb-icon-check"></i>${o("Save changes")}`),V.find(".sb-profile").setProfile(),V.find(".sb-unlisted-detail").remove(),V.find("input,select,textara").removeClass("sb-error");let n="",r=V.find(".sb-additional-details [id]").map(function(){return this.id}).get().concat(["wp-id","perfex-id","whmcs-id","aecommerce-id","facebook-id","ip","os","current_url","country_code","browser_language","browser","martfury-id","martfury-session"]);for(var t in e.extra)r.includes(t)||(n+=`<div id="${t}" data-type="text" class="sb-input sb-unlisted-detail"><span>${o(e.extra[t].name)}</span><input type="text"></div>`);V.find(".sb-additional-details .sb-edit-box").append(n),this.populateEdit(e,V),this.updateRequiredFields(s),"admin"==SB_ACTIVE_AGENT.user_type&&SBF.isAgent(s)&&a.html(`<option value="agent">${o("Agent")}</option><option value="admin"${"admin"==s?" selected":""}>${o("Admin")}</option>`),i.val()&&i.val("********"),SB_ADMIN_SETTINGS.cloud&&V.setClass("sb-cloud-admin",1==e.id),this.boxClasses(V,s),V.sbShowLightbox(),SBF.event("SBProfileEditBoxOpened",{user_id:e.id})}},populate:function(e,t){let i=["first_name","last_name","password","profile_image"],s="";if(t.hasClass("sb-profile-list-conversation")&&SBChat.conversation){let e=SBChat.conversation.get("source");s=this.profileRow("conversation-id",SBChat.conversation.id,o("Conversation ID")),SBF.null(e)||(s+=this.profileRow("conversation-source",st.getName(e),o("Source")))}"admin"!=SB_ACTIVE_AGENT.user_type&&i.push("token");for(var a in e.details)i.includes(a)||(s+=this.profileRow(a,e.get(a),"id"==a?"User ID":a));if(t.html(`<ul>${s}</ul>`),s="",e.isExtraEmpty())SBF.ajax({function:"get-user-extra",user_id:e.id},i=>{for(var a=0;a<i.length;a++){let t=i[a].slug;e.setExtra(t,i[a]),s+=this.profileRow(t,i[a].value,i[a].name)}t.find("ul").append(s),l(t,145),SBF.event("SBUserLoaded",e)});else{for(var a in e.extra){let t=e.getExtra(a);s+=this.profileRow(a,t.value,t.name)}t.find("ul").append(s),l(t,145),SBF.event("SBUserLoaded",e)}},profileRow:function(e,t,i=e){if(!t)return"";let s,a={id:"user",full_name:"user",email:"envelope",phone:"phone",user_type:"user",last_activity:"calendar",creation_time:"calendar",token:"shuffle",currency:"currency",location:"marker",country:"marker",address:"marker",city:"marker",postal_code:"marker",browser:"desktop",os:"desktop",current_url:"next",timezone:"clock"},n=`<i class="sb-icon sb-icon-${e in a?a[e]:"plane"}"></i>`,r=!1;switch(e){case"last_activity":case"creation_time":t=SBF.beautifyTime(t);break;case"user_type":t=SBF.slugToString(t);break;case"country":case"country_code":case"language":case"browser_language":let i=t;"ar"!=t||"language"!=e&&"browser_language"!=e||(i="arc"),n=`<img src="${SB_URL}/media/flags/${i.toLowerCase()}.png" />`;break;case"browser":(s=t.toLowerCase()).includes("chrome")?r="chrome":s.includes("edge")?r="edge":s.includes("firefox")?r="firefox":s.includes("opera")?r="opera":s.includes("safari")&&(r="safari");break;case"os":(s=t.toLowerCase()).includes("windows")?r="windows":s.includes("mac")||s.includes("apple")||s.includes("ipad")||s.includes("iphone")?r="apple":s.includes("android")?r="android":s.includes("linux")?r="linux":s.includes("ubuntu")&&(r="ubuntu");break;case"conversation-source":r=t.toLowerCase();case"browser":case"os":case"conversation-source":r&&(n=`<img src="${SB_URL}/media/${"conversation-source"==e?"apps":"devices"}/${r}.svg" />`);break;case"current_url":t=b(t)}return`<li data-id="${e}">${n}<span>${o(SBF.slugToString(i))}</span><label>${t}</label></li>`},populateEdit:function(t,i){i.find(".sb-details .sb-input").each((i,s)=>{this.set(s,t.details[e(s).attr("id")])}),i.find(".sb-additional-details .sb-input").each((i,s)=>{let a=e(s).attr("id");a in t.extra?this.set(s,t.extra[a].value):this.set(s,"")})},clear:function(e){SBForm.clear(e)},errors:function(e){return SBForm.errors(e.find(".sb-details"))},showErrorMessage:function(e,t){SBForm.showErrorMessage(e,t)},agentData:function(){let e=`<div class="sb-title">${o("Feedback rating")}</div><div class="sb-rating-area sb-loading"></div>`,t=O.find(".sb-agent-area");t.html(e),SBF.ajax({function:"get-rating"},i=>{if(0==i[0]&&0==i[1])e=`<p class="sb-no-results">${o("No ratings yet.")}</p>`;else{let t=i[0]+i[1],s=100*i[0]/t,a=100*i[1]/t;e=`<div><div>${o("Helpful")}</div><span data-count="${i[0]}" style="width: ${Math.round(2*s)}px"></span><div>${s.toFixed(2)} %</div></div><div><div>${o("Not helpful")}</div><span data-count="${i[1]}" style="width: ${Math.round(2*a)}px"></span><div>${a.toFixed(2)} %</div></div><p class="sb-rating-count">${t} ${o("Ratings")}</p>`}t.find(".sb-rating-area").html(e).sbLoading(!1)})},boxClasses:function(t,i=!1){e(t).removeClass("sb-type-admin sb-type-agent sb-type-lead sb-type-user sb-type-visitor").addClass(`${0!=i?`sb-type-${i}`:""} sb-agent-${SB_ACTIVE_AGENT.user_type}`)},updateRequiredFields:function(e){let t=SBF.isAgent(e);V.find("#password input").prop("required",t),V.find("#email input").prop("required",t)}},dt={infoBottom:function(e,t=!1){var i=k.find(".sb-info-card");t?"error"==t?i.addClass("sb-info-card-error"):i.addClass("sb-info-card-info"):(i.removeClass("sb-info-card-error sb-info-card-warning sb-info-card-info"),clearTimeout(ge),ge=setTimeout(()=>{i.sbActive(!1)},5e3)),i.html(`<h3>${o(e)}</h3>`).sbActive(!0)},infoPanel:function(t,i="info",s=!1,a="",n="",r=!1,l=!1,c=!1){if(l&&s)return s();let d=k.find(".sb-dialog-box").attr("data-type",i),u=d.find("p");d.attr("id",a).setClass("sb-scroll-area",r).css("height",r?parseInt(e(window).height())-200+"px":""),d.find(".sb-title").html(o(n)),u.html(("alert"==i?o("Are you sure?")+" ":"")+o(t)),d.sbActive(!0).css({"margin-top":d.outerHeight()/-2+"px","margin-left":d.outerWidth()/-2+"px"}),we.sbActive(!0),be=s,ve=c,setTimeout(()=>{dt.open_popup=d},500)},genericPanel:function(e,t,i,s=[],a="",n=!1){let r="",l=k.find("#sb-generic-panel");for(var c=0;c<s.length;c++)s[c]=h(s[c])||s[c]instanceof String?[s[c],!1]:s[c],r+=`<a id="sb-${SBF.stringToSlug(s[c][0])}" class="sb-btn${s[c][1]?" sb-icon":""}">${s[c][1]?`<i class="sb-icon-${s[c][1]}"></i>`:""} ${o(s[c][0])}</a>`;l.html(`<div class="sb-lightbox sb-${e}-box"${a}><div class="sb-info"></div><div class="sb-top-bar"><div>${o(t)}</div>\n                        <div>\n                            ${r}\n                            <a class="sb-close sb-btn-icon sb-btn-red">\n                                <i class="sb-icon-close"></i>\n                            </a>\n                        </div>\n                    </div>\n                    <div class="sb-main${n?" sb-scroll-area":""}">\n                        ${i}\n                    </div>\n             </div>`),l.find("> div").sbShowLightbox()},activeUser:function(e){if(typeof e==Ke)return window.sb_current_user;window.sb_current_user=e},loadingGlobal:function(t=!0,i=!0){k.find(".sb-loading-global").sbActive(t),i&&(we.sbActive(t),e("body").setClass("sb-lightbox-active",t))},loading:function(t){return!!e(t).sbLoading()||(e(t).sbLoading(!0),!1)},collapse(t,i){let s=(t=e(t)).find("> div, > ul");s.css({height:"","max-height":""}),t.find(".sb-collapse-btn").remove(),t.hasClass("sb-collapse")&&e(s).prop("scrollHeight")>i&&(t.sbActive(!0).attr("data-height",i),t.append(`<a class="sb-btn-text sb-collapse-btn">${o("View more")}</a>`),s.css({height:i+"px","max-height":i+"px"}))},open_popup:!1,must_translate:!1,is_logout:!1,conversations:lt,users:rt,settings:at,profile:ct,apps:st};window.SBAdmin=dt,e(document).ready(function(){function c(e,i,s,a=0,n=[],r,l=!1,d=!1,u=!1,p=!1,h=!1){let f={whatsapp:["whatsapp-send-template","messages"," a phone number.","direct-whatsapp"],email:["create-email","emails"," an email address.",!1],custom_email:["send-custom-email","emails"," an email address.","direct-emails"],sms:["send-sms","text messages"," a phone number.","direct-sms"]}[r];SBF.ajax({function:f[0],to:e[i][a].value,recipient_id:e[i][a].id,sender_name:SB_ACTIVE_AGENT.full_name,sender_profile_image:SB_ACTIVE_AGENT.profile_image,subject:l,message:s,template_name:d,parameters:u,template_languages:p,template:!1,phone_id:h,user_name:!!e.first_name&&(e.first_name[a].value+" "+e.last_name[a].value).trim(),user_email:!!e.email&&e.email[a].value},g=>{let b=e[i].length,v="whatsapp"==r?k.find("#sb-whatsapp-send-template-box"):D;if(v.find(".sb-bottom > div").html(`${o("Sending")} ${o(f[1])}... ${a+1} / ${b}`),g){if(!0!==g&&("status"in g&&400==g.status||"error"in g&&![131030,131009].includes(g.error.code)))return SBForm.showErrorMessage(v,g.error?g.error.message:`${g.message} Details at ${g.more_info}`),console.error(g),v.find(".sb-loading").sbLoading(!1),void v.find(".sb-bottom > div").html("");if(a<b-1)return c(e,i,s,a+1,n,r,l,d,u,p,h);n.length?c(n,i,s,0,[],"sms",!1):(k.sbHideLightbox(),f[3]&&SBF.ajax({function:"reports-update",name:f[3],value:s.substr(0,18)+" | "+b})),t(1==b?"The message has been sent.":o("The message was sent to all users who have"+f[2]))}else console.warn(g)})}if(k=e(".sb-admin"),x=k.find("> .sb-header"),A=k.find(".sb-area-conversations"),B=A.find(".sb-conversation"),T=A.find(".sb-conversation .sb-list"),$=A.find(".sb-admin-list"),C=$.find(".sb-scroll-area ul"),F=$.find(".sb-select"),I=A.find(".sb-user-details"),G=k.find(".sb-area-users"),U=G.find(".sb-table-users"),P=G.find(".sb-menu-users"),q=G.find(".sb-filter-btn .sb-select"),O=k.find(".sb-profile-box"),V=k.find(".sb-profile-edit-box"),H=k.find(".sb-area-settings"),W=H.find(".sb-automations-area"),Y=W.find(".sb-conditions"),z=W.find(" > .sb-select"),J=W.find(" > .sb-tab > .sb-nav > ul"),Te=k.find(".sb-area-reports"),X=k.find(".sb-area-articles"),Q=X.find(".sb-content-articles"),Z=X.find(".sb-content-categories"),ee=k.find("#article-parent-categories"),K=k.find("#article-categories"),Fe=A.find(".sb-replies"),we=k.find(".sb-lightbox-overlay"),ye=typeof SB_URL!=Ke?SB_URL.substr(0,SB_URL.indexOf("-content")-3):"",Ee=A.find(".sb-woocommerce-products"),Le=Ee.find(" > div > ul"),N=A.find(".sb-panel-notes"),E=A.find(".sb-panel-tags"),L=A.find(".sb-panel-attachments"),D=k.find(".sb-direct-message-box"),et=st.is("wordpress")&&e(".wp-admin").length,M=k.find(".sb-dialogflow-intent-box"),R=A.find(".sb-editor > .sb-suggestions"),j=A.find(".sb-btn-open-ai"),Ie=A.find("#conversation-department"),de=k.find(".sb-upload-form-admin .sb-upload-files"),te=k.find(".sb-area-chatbot"),ie=te.find("#sb-table-chatbot-files"),se=te.find("#sb-table-chatbot-website"),ae=te.find("#sb-chatbot-qea"),ne=te.find(".sb-playground-editor"),oe=te.find(".sb-playground .sb-scroll-area"),le=te.find('[data-id="flows"] > .sb-content'),re=te.find("#sb-flows-nav"),window.onpopstate=function(){k.sbHideLightbox(),We&&A.sbActive()&&B.sbActive()&&lt.mobileCloseConversation(),SBF.getURL("user")?(G.sbActive()||x.find(".sb-admin-nav #sb-users").click(),ct.show(SBF.getURL("user"))):SBF.getURL("area")?x.find(".sb-admin-nav #sb-"+SBF.getURL("area")).click():SBF.getURL("conversation")?(A.sbActive()||x.find(".sb-admin-nav #sb-conversations").click(),lt.openConversation(SBF.getURL("conversation"))):SBF.getURL("setting")?(H.sbActive()||x.find(".sb-admin-nav #sb-settings").click(),H.find("#tab-"+SBF.getURL("setting")).click()):SBF.getURL("report")&&(Te.sbActive()||x.find(".sb-admin-nav #sb-reports").click(),Te.find("#"+SBF.getURL("report")).click())},SBF.getURL("area")&&setTimeout(()=>{x.find(".sb-admin-nav #sb-"+SBF.getURL("area")).click()},300),typeof SB_ADMIN_SETTINGS==Ke){let t=k.find(".sb-intall"),i=window.location.href.replace("/admin","").replace(".php","").replace(/#$|\/$/,"");return e(k).on("click",".sb-submit-installation",function(){if(a(this))return;let s=!1,n=t.find("#first-name").length;SBForm.errors(t)?s=n?"All fields are required. Minimum password length is 8 characters. Be sure you've entered a valid email.":"All fields are required.":n&&t.find("#password input").val()!=t.find("#password-check input").val()?s="The passwords do not match.":(SBF.cookie("SA_VGCKMENS",0,0,"delete"),i.includes("?")&&(i=i.substr(0,i.indexOf("?"))),e.ajax({method:"POST",url:i+"/include/ajax.php",data:{function:"installation",details:e.extend(SBForm.getAll(t),{url:i})}}).done(a=>{if(h(a)&&(a=JSON.parse(a)),0!=a){if(!0===(a=a[1]))return void setTimeout(()=>{window.location.href=i+"/admin.php?refresh=true"},1e3);switch(a){case"connection-error":s="Support Board cannot connect to the database. Please check the database information and try again.";break;case"missing-details":s="Missing database details! Please check the database information and try again.";break;case"missing-url":s="Support Board cannot get the plugin URL.";break;default:s=a}}else s=a;!1!==s&&(SBForm.showErrorMessage(t,s),e("html, body").animate({scrollTop:0},500)),e(this).sbLoading(!1)})),!1!==s&&(SBForm.showErrorMessage(t,s),e("html, body").animate({scrollTop:0},500),e(this).sbLoading(!1))}),void fetch("https://board.support/synch/verification.php?x="+i)}if(!k.length)return;if(n(),k.removeAttr("style"),(window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone||document.referrer.includes("android-app://"))&&k.addClass("sb-pwa"),Je&&r(),k.find(" > .sb-rich-login").length)return;SBF.storage("notifications-counter",[]),SB_ADMIN_SETTINGS.pusher?(SBPusher.active=!0,SBPusher.init(()=>{SBPusher.presence(1,()=>{rt.updateUsersActivity()}),SBPusher.event("update-conversations",()=>{lt.update()},"agents"),SBPusher.event("set-agent-status",e=>{e.agent_id==SB_ACTIVE_AGENT.id&&(rt.setActiveAgentStatus("online"==e.status),Ue=!1)},"agents"),y()})):(y(),setInterval(function(){rt.updateUsersActivity()},1e4)),rt.table_extra=U.find("th[data-extra]").map(function(){return e(this).attr("data-field")}).get(),typeof SB_CLOUD_FREE!=Ke&&SB_CLOUD_FREE&&setTimeout(()=>{location.reload()},36e5),e(window).on("beforeunload",function(){s()&&e.ajax({method:"POST",url:SB_AJAX_URL,data:{function:"on-close"}})}),e(window).keydown(function(e){let t=e.which,i=!1;if(Ae=t,[13,27,32,37,38,39,40,46,90].includes(t)){if(k.find(".sb-dialog-box").sbActive()){let e=k.find(".sb-dialog-box");switch(t){case 46:case 27:e.find(".sb-cancel").click();break;case 32:case 13:e.find("info"!=e.attr("data-type")?".sb-confirm":".sb-close").click()}i=!0}else if([38,40,46,90].includes(t)&&A.sbActive()&&!k.find(".sb-lightbox").sbActive()){let s=A.find(".sb-editor textarea"),a=s.is(":focus");if(46==t){if(a||"INPUT"==e.target.tagName)return;let t=A.find(" > div > .sb-conversation");t.find('.sb-top [data-value="'+(3==t.attr("data-conversation-status")?"delete":"archive")+'"]').click(),i=!0}else if(e.ctrlKey){let e=C.find(".sb-active");40==t?e.next().click():38==t?e.prev().click():90==t&&a&&lt.previous_editor_text&&(s.val(lt.previous_editor_text),lt.previous_editor_text=!1,i=!0),38!=t&&40!=t||(i=!0,lt.scrollTo())}}else if([37,39].includes(t)&&G.sbActive()&&k.find(".sb-lightbox").sbActive()){let e=U.find(`[data-user-id="${s().id}"]`);(e=39==t?e.next():e.prev()).length&&(k.sbHideLightbox(),ct.show(e.attr("data-user-id"))),i=!0}else if(27==t&&k.find(".sb-lightbox").sbActive())k.sbHideLightbox(),i=!0;else if(46==t){let e=k.find(".sb-search-btn.sb-active");e.length&&(e.find("i").click(),i=!0)}else 13==t&&te.find(".sb-playground-editor textarea").is(":focus")&&(te.find('.sb-playground-editor [data-value="send"]').click(),i=!0);i&&e.preventDefault()}}),e(window).keyup(function(e){Ae=!1}),e(document).on("click keydown mousemove",function(){SBF.debounce(()=>{SBChat.tab_active||SBF.visibilityChange(),SBChat.tab_active=!0,clearTimeout(Ze),Ze=setTimeout(()=>{SBChat.tab_active=!1},1e4)},"#3",8e3),!We&&SB_ADMIN_SETTINGS.away_mode&&SBF.debounce(()=>{Ue&&(rt.setActiveAgentStatus(),clearTimeout(_e),_e=setTimeout(()=>{rt.setActiveAgentStatus(!1)},6e5))},"#4",558e3)}),document.onpaste=function(e){let t=e.clipboardData.items[0];if(0===t.type.indexOf("image")){var i=new FileReader;i.onload=function(e){let t=e.target.result.split(","),i=t[0].indexOf("base64")>=0?atob(t[1]):decodeURI(t[1]),s=new Uint8Array(i.length);for(let e=0;e<i.length;e++)s[e]=i.charCodeAt(e);let a=new FormData;a.append("file",new Blob([s],{type:t[0].split(":")[1].split(";")[0]}),"image_print.jpg"),SBF.upload(a,function(e){SBChat.uploadResponse(e)})},i.readAsDataURL(t.getAsFile())}};let u=[o("Please go to Settings > Miscellaneous and enter the Envato Purchase Code of Support Board."),`${o("Your license key is expired. Please purchase a new license")} <a href="https://board.support/shop/{R}" target="_blank">${o("here")}</a>.`];e(x).on("click",".sb-version",function(){let e=k.find(".sb-updates-box");SBF.ajax({function:"get-versions"},t=>{let i="",s={sb:"Support Board",slack:"Slack",dialogflow:"Artificial Intelligence",tickets:"Tickets",woocommerce:"Woocommerce",ump:"Ultimate Membership Pro",perfex:"Perfex",whmcs:"WHMCS",aecommerce:"Active eCommerce",messenger:"Messenger",whatsapp:"WhatsApp",armember:"ARMember",telegram:"Telegram",viber:"Viber",line:"LINE",wechat:"WeChat",zalo:"Zalo",twitter:"Twitter",zendesk:"Zendesk",martfury:"Martfury",opencart:"OpenCart",zalo:"Zalo"},a=!1;for(var r in t)if(st.is(r)){let e=SB_VERSIONS[r]==t[r];e||(a=!0),i+=`<div class="sb-input"><span>${s[r]}</span><div${e?' class="sb-green"':""}>${o(e?"You are running the latest version.":"Update available! Please update now.")} ${o("Your version is")} V ${SB_VERSIONS[r]}.</div></div>`}a?e.find(".sb-update").removeClass("sb-hide"):e.find(".sb-update").addClass("sb-hide"),n(!1),e.find(".sb-main").prepend(i),e.sbShowLightbox()}),n(),e.sbActive(!1),e.find(".sb-input").remove()}),e(k).on("click",".sb-updates-box .sb-update",function(){if(a(this))return;let i=k.find(".sb-updates-box");SBF.ajax({function:"update",domain:SB_URL},s=>{let a="";if(SBF.errorValidation(s,"envato-purchase-code-not-found"))a=u[0];else if(SBF.errorValidation(s))a=SBF.slugToString(s[1]);else{let e=!0;for(var n in s)if("success"!=s[n]){e=!1,"expired"==s[n]&&(a=u[1].replace("{R}",n)),"license-key-not-found"==s[n]&&(a=o("License key for the {R} app missing. Add it in Settings > Apps.").replace("{R}",SBF.slugToString(n.replace("dialogflow","Artificial Intelligence"))));break}e||a||(a=JSON.stringify(s))}r(),a?SBForm.showErrorMessage(i,a):(t("Update completed."),location.reload()),e(this).sbLoading(!1)})}),setTimeout(function(){let e=SBF.storage("last-update-check"),i=[Ye.getMonth(),Ye.getDate()];SB_ADMIN_SETTINGS.cloud||(0==e||i[0]!=e[0]||i[1]>e[1]+10)&&(SBF.storage("last-update-check",i),SB_ADMIN_SETTINGS.auto_updates?SBF.ajax({function:"update",domain:SB_URL},e=>{h(e)||Array.isArray(e)||(t("Automatic update completed. Reload the admin area to apply the update."),r())}):"admin"==SB_ACTIVE_AGENT.user_type&&SBF.ajax({function:"updates-available"},e=>{!0===e&&t(`${o("Update available.")} <span onclick="$('.sb-version').click()">${o("Click here to update now")}</span>`,"info")}))},1e3),e(k).on("click",".sb-apps > div:not(.sb-disabled)",function(){let t=k.find(".sb-app-box"),i=e(this).data("app"),s=SB_ADMIN_SETTINGS.cloud,a=st.is(i)&&(!s||SB_CLOUD_ACTIVE_APPS.includes(i)),n="?utm_source=plugin&utm_medium=admin_area&utm_campaign=plugin";s||SBF.ajax({function:"app-get-key",app_name:i},e=>{t.find("input").val(e)}),t.setClass("sb-active-app",a),t.find("input").val(""),t.find(".sb-top-bar > div:first-child").html(e(this).find("h2").html()),t.find("p").html(e(this).find("p").html()),t.attr("data-app",i),t.find(".sb-btn-app-setting").sbActive(a),t.find(".sb-btn-app-puchase").attr("href","https://board.support/shop/"+i+n),t.find(".sb-btn-app-details").attr("href",(s?WEBSITE_URL:"https://board.support/")+i+n),t.sbShowLightbox()}),e(k).on("click",".sb-app-box .sb-activate",function(){let i=k.find(".sb-app-box"),s=i.find("input").val(),n=i.attr("data-app");if(s||SB_ADMIN_SETTINGS.cloud){if(a(this))return;SBF.ajax({function:"app-activation",app_name:n,key:s},a=>{if(SBF.errorValidation(a)){let t="";t="envato-purchase-code-not-found"==(a=a[1])?u[0]:"invalid-key"==a?"It looks like your license key is invalid. If you believe this is an error, please contact support.":"expired"==a?u[1].replace("{R}",s):"app-purchase-code-limit-exceeded"==a?SBF.slugToString(n)+" app purchase code limit exceeded.":"Error: "+a,SBForm.showErrorMessage(i,t),e(this).sbLoading(!1)}else t("Activation complete! Page reload in progress..."),setTimeout(function(){location.reload()},1e3)})}else SBForm.showErrorMessage(i,"Please insert the license key.")}),e(k).on("click",".sb-app-box .sb-btn-app-setting",function(){H.find("#tab-"+e(this).closest("[data-app]").attr("data-app")).click(),k.sbHideLightbox()}),typeof Notification===Ke||"all"!=SB_ADMIN_SETTINGS.desktop_notifications&&"agents"!=SB_ADMIN_SETTINGS.desktop_notifications||SB_ADMIN_SETTINGS.push_notifications||(lt.desktop_notifications=!0),["all","agents"].includes(SB_ADMIN_SETTINGS.flash_notifications)&&(lt.flash_notifications=!0),Ye.getDate()!=SBF.storage("admin-clean")&&setTimeout(function(){SBF.ajax({function:"cron-jobs"}),SBF.storage("admin-clean",Ye.getDate())},1e4),e(k).on("click",".sb-collapse-btn",function(){let t=e(this).sbActive(),i=t?e(this).parent().data("height")+"px":"";e(this).html(o(t?"View more":"Close")),e(this).parent().find("> div, > ul").css({height:i,"max-height":i}),e(this).sbActive(!t)}),e(k).on("click",".sb-popup-close",function(){k.sbHideLightbox()}),We?(I.find("> .sb-scroll-area").prepend('<div class="sb-profile"><img><span class="sb-name"></span></div>'),e(k).on("click",".sb-menu-mobile > i",function(){e(this).toggleClass("sb-active"),dt.open_popup=e(this).parent()}),e(k).on("click",".sb-menu-mobile a",function(){e(this).closest(".sb-menu-mobile").find(" > i").sbActive(!1)}),e(k).on("click",".sb-menu-wide,.sb-nav",function(){e(this).toggleClass("sb-active")}),e(k).on("click",".sb-menu-wide > ul > li, .sb-nav > ul > li",function(t){let i=e(this).parent().parent();return i.find("li").sbActive(!1),i.find("> div:not(.sb-menu-wide):not(.sb-btn)").html(e(this).html()),i.sbActive(!1),i.find("> .sb-menu-wide").length&&i.closest(".sb-scroll-area").scrollTop(i.next()[0].offsetTop-(k.hasClass("sb-header-hidden")?70:130)),t.preventDefault(),!1}),e(k).find(".sb-admin-list .sb-scroll-area, main > div > .sb-scroll-area,.sb-area-settings > .sb-tab > .sb-scroll-area,.sb-area-reports > .sb-tab > .sb-scroll-area").on("scroll",function(){let t=e(this).scrollTop();ze.last<t-10&&ze.header?(k.addClass("sb-header-hidden"),ze.header=!1):ze.last>t+10&&!ze.header&&!ze.always_hidden&&(k.removeClass("sb-header-hidden"),ze.header=!0),ze.last=t}),e(k).on("click",".sb-search-btn i,.sb-filter-btn i",function(){e(this).parent().sbActive()?(k.addClass("sb-header-hidden"),ze.always_hidden=!0):(ze.always_hidden=!1,C.parent().scrollTop()<10&&k.removeClass("sb-header-hidden"))}),e(k).on("click",".sb-top .sb-btn-back",function(){lt.mobileCloseConversation()}),e(U).find("th:first-child").html(o("Order by")),e(U).on("click","th:first-child",function(){e(this).parent().toggleClass("sb-active")}),document.addEventListener("touchstart",e=>{xe=e.changedTouches[0].clientX,Be=e.changedTouches[0].clientY},!1),document.addEventListener("touchend",()=>{S()},!1),document.addEventListener("touchmove",e=>{var t=e.changedTouches[0].clientX,i=xe-t,a=e.changedTouches[0].clientY,n=Be-a;if(Math.abs(i)>Math.abs(n)){var o=[];ke=A.sbActive()?[C.find(".sb-active"),T,1]:[U.find(`[data-user-id="${s().id}"]`),O,2],i>150?(1==ke[2]?ke[0].next().click():o=ke[0].next(),S()):i<-150&&(1==ke[2]?ke[0].prev().click():o=ke[0].prev(),S()),2==ke[2]&&o.length&&(k.sbHideLightbox(),ct.show(o.attr("data-user-id"))),(i>80||i<-80)&&(ke[1].css("transform","translateX("+-1*i+"px)"),ke[1].addClass("sb-touchmove"))}},!1)):SB_ADMIN_SETTINGS.hide_conversation_details?A.find('.sb-menu-mobile [data-value="panel"]').sbActive(!0):I.sbActive(!0),e(window).width()<913&&e(A).on("click","> .sb-btn-collapse",function(){e(this).toggleClass("sb-active"),A.find(e(this).hasClass("sb-left")?".sb-admin-list":".sb-user-details").toggleClass("sb-active")}),e(x).on("click"," .sb-admin-nav a",function(){switch($e=e(this).attr("id").substr(3),dt.active_admin_area=$e,x.find(".sb-admin-nav a").sbActive(!1),k.find(" > main > div").sbActive(!1),k.find(".sb-area-"+$e).sbActive(!0),e(this).sbActive(!0),SBF.deactivateAll(),$e){case"conversations":We||SBF.getURL("conversation")||lt.clickFirst(),lt.update(),lt.startRealTime(),rt.stopRealTime();break;case"users":rt.startRealTime(),lt.stopRealTime(),rt.init||(n(),je=1,Ge=1,rt.get(e=>{rt.populate(e),rt.updateMenu(),rt.init=!0,rt.datetime_last_user=SBF.dateDB("now"),n(!1)}));break;case"settings":at.init||(n(),SBF.ajax({function:"get-all-settings"},i=>{if(i){let e=i["external-settings-translations"];if(i["slack-agents"]){let e="";for(var s in i["slack-agents"][0])e+=`<div data-id="${s}"><select><option value="${i["slack-agents"][0][s]}"></option></select></div>`;H.find("#slack-agents .input").html(e)}at.translations.translations=Array.isArray(e)&&!e.length?{}:e,delete i["external-settings-translations"];for(var s in i)at.set(s,i[s])}SBF.getURL("refresh_token")&&(k.find("#google-refresh-token input").val(SBF.getURL("refresh_token")),at.save(),t("Synchronization completed."),k.find("#google")[0].scrollIntoView()),H.find("textarea").each(function(){e(this).autoExpandTextarea(),e(this).manualExpandTextarea()}),H.find("[data-setting] .sb-language-switcher-cnt").each(function(){e(this).sbLanguageSwitcher(at.translations.getLanguageCodes(e(this).closest("[data-setting]").attr("id")),"settings")}),at.init=!0,n(!1),i&&!SB_ADMIN_SETTINGS.cloud&&at.visibility(0,i["push-notifications"]&&"pusher"==i["push-notifications"][0]["push-notifications-provider"][0]),at.visibility(1,!i.messenger||"manual"!=i.messenger[0]["messenger-sync-mode"][0]),at.visibility(2,!i["open-ai"]||"assistant"!=i["open-ai"][0]["open-ai-mode"][0]),SBF.event("SBSettingsLoaded",i)})),rt.stopRealTime(),lt.stopRealTime();break;case"reports":Te.sbLoading()&&e.getScript(SB_URL+"/vendor/moment.min.js",()=>{e.getScript(SB_URL+"/vendor/daterangepicker.min.js",()=>{e.getScript(SB_URL+"/vendor/chart.min.js",()=>{ot.initDatePicker(),ot.initReport("conversations"),Te.sbLoading(!1)})})}),rt.stopRealTime(),lt.stopRealTime();break;case"articles":let i=X.find(".sb-menu-wide li").eq(0);X.sbLoading()?(i.sbActive(!0).next().sbActive(!1),SBF.ajax({function:"init-articles-admin"},e=>{nt.categories.list=e[1],nt.translations.list=e[2],nt.page_url=e[3],nt.is_url_rewrite=e[4],nt.cloud_chat_id=e[5],nt.populate(e[0]),nt.populate(e[1],!0),nt.categories.update(),X.sbLoading(!1)})):i.click(),rt.stopRealTime(),lt.stopRealTime();break;case"chatbot":ie.sbLoading()&&st.openAI.init(),st.openAI.troubleshoot()}SBF.getURL("area")!=$e&&("conversations"==$e&&!SBF.getURL("conversation")||"users"==$e&&!SBF.getURL("user")||"settings"==$e&&!SBF.getURL("setting")||"reports"==$e&&!SBF.getURL("report")||"articles"==$e&&!SBF.getURL("article")||"chatbot"==$e&&!SBF.getURL("chatbot"))&&f("?area="+$e)}),e(x).on("click",".sb-profile",function(){e(this).next().toggleClass("sb-active")}),e(x).on("click",'[data-value="logout"],.logout',function(){dt.is_logout=!0,SBF.ajax({function:"on-close"}),rt.stopRealTime(),lt.stopRealTime(),setTimeout(()=>{SBF.logout()},300)}),e(x).on("click",'[data-value="edit-profile"],.edit-profile',function(){n();let e=new SBUser({id:SB_ACTIVE_AGENT.id});e.update(()=>{s(e),A.find(".sb-board").addClass("sb-no-conversation"),C.find(".sb-active").sbActive(!1),ct.showEdit(e)})}),e(x).on("click",'[data-value="status"]',function(){let t=!e(this).hasClass("sb-online");rt.setActiveAgentStatus(t),Ue=t}),e(x).find(".sb-account").setProfile(SB_ACTIVE_AGENT.full_name,SB_ACTIVE_AGENT.profile_image),e(C).on("click","li",function(){17==Ae?e(this).sbActive(!e(this).sbActive()):(lt.openConversation(e(this).attr("data-conversation-id"),e(this).attr("data-user-id"),!1),SBF.deactivateAll())}),e(k).on("click",".sb-user-conversations li",function(){lt.openConversation(e(this).attr("data-conversation-id"),s().id,e(this).attr("data-conversation-status"))}),e(A).on("click",".sb-top ul a",function(){let t,n=C.find(".sb-active").map(function(){return{id:e(this).attr("data-conversation-id"),user_id:e(this).attr("data-user-id"),status_code:e(this).attr("data-conversation-status")}}).toArray(),r=n.length,l=r>1?"All the selected conversations will be ":"The conversation will be ",c=(t,i)=>{let s=t.includes(".txt");e(this).sbLoading(!1),"email"==i&&actioninfoBottom(s?o("Transcript sent to user's email.")+' <a href="'+t+'" target="_blank">'+o("View transcript")+"</a>":"Transcript sending error: "+t,s?"":"error")};switch(e(this).attr("data-value")){case"inbox":t=1,l+="restored.";break;case"archive":l+="archived.",t=3;break;case"delete":l+="deleted.",t=4;break;case"empty-trash":t=5,l="All conversations in the trash (including their messages) will be deleted permanently.";break;case"transcript":let i=e(this).attr("data-action");"email"!=i||s()&&s().get("email")||(i=""),lt.transcript(n[0].id,n[0].user_id,i,e=>c(e,i)),a(this);break;case"read":t=1,l+="marked as read.";break;case"unread":t=2,l+="marked as unread.";break;case"panel":e([I,this]).toggleClass("sb-active")}t&&i(l,"alert",function(){let e=F.eq(0).find("p").attr("data-value"),i=n[r-1].id;for(var s=0;s<r;s++){let a=n[s];SBF.ajax({function:"update-conversation-status",conversation_id:a.id,status_code:t},()=>{let s=C.find(`[data-conversation-id="${a.id}"]`);if([0,3,4].includes(t))for(var n=0;n<Ce.length;n++)if(Ce[n].id==a.id){Ce[n].set("status_code",t);break}if(SB_ADMIN_SETTINGS.close_message&&3==t&&(SBF.ajax({function:"close-message",conversation_id:a.id,bot_id:SB_ADMIN_SETTINGS.bot_id}),SB_ADMIN_SETTINGS.close_message_transcript&&lt.transcript(a.id,a.user_id,"email",e=>c(e))),[0,1,2].includes(t)&&(s.attr("data-conversation-status",t),lt.updateMenu()),SBChat.conversation&&st.is("slack")&&[3,4].includes(t)&&SBF.ajax({function:"archive-slack-channels",conversation_user_id:SBChat.conversation.get("user_id")}),0==e&&[3,4].includes(t)||3==e&&[0,1,2,4].includes(t)||4==e&&4!=t){let e=!1;lt.updateMenu(),SBChat.conversation&&SBChat.conversation.id==a.id&&(e=s.prev(),SBChat.conversation=!1),s.remove(),a.id==i&&lt.clickFirst(e)}4==e&&5==t&&(C.find("li").remove(),lt.updateMenu(),lt.clickFirst())}),SBChat.conversation&&SBChat.conversation.id==a.id&&(SBChat.conversation.set("status_code",t),lt.setReadIcon(t))}})}),SBF.ajax({function:"saved-replies"},e=>{let t=`<p class="sb-no-results">${o("No saved replies found. Add new saved replies via Settings > Admin.")}</p>`;if(Array.isArray(e)&&e.length&&e[0]["reply-name"]){t="",Ne=e;for(var i=0;i<e.length;i++)t+=`<li><div>${e[i]["reply-name"]}</div><div>${e[i]["reply-text"].replace(/\\n/g,"\n")}</div></li>`}Fe.find(".sb-replies-list > ul").html(t).sbLoading(!1)}),e(A).on("click",".sb-btn-saved-replies",function(){Fe.sbTogglePopup(this),Fe.find(".sb-search-btn").sbActive(!0).find("input").get(0).focus()}),e(Fe).on("click",".sb-replies-list li",function(){SBChat.insertText(e(this).find("div:last-child").text().replace(/\\n/g,"\n")),SBF.deactivateAll(),k.removeClass("sb-popup-active")}),e(Fe).on("input",".sb-search-btn input",function(){v(e(this).val().toLowerCase())}),e(Fe).on("click",".sb-search-btn i",function(){SBF.searchClear(this,()=>{v("")})}),e(k).on("click",".sb-btn-open-ai",function(){if(!SBChat.conversation||a(this))return;let t=e(this).hasClass("sb-btn-open-ai-editor"),i=t?A.find(".sb-editor textarea"):M.find("textarea");st.openAI.rewrite(i.val(),s=>{e(this).sbLoading(!1),s[0]&&(i.val(t?"":s[1]),t&&SBChat.insertText(s[1]))})}),e($).find(".sb-scroll-area").on("scroll",function(){if(!Xe&&!lt.is_search&&d(this,!0)&&Me){let e=A.find(".sb-admin-list"),t=lt.filters();Xe=!0,e.append('<div class="sb-loading-global sb-loading"></div>'),SBF.ajax({function:"get-conversations",pagination:De,status_code:t[0],department:t[1],source:t[2],tag:t[3]},t=>{if(setTimeout(()=>{Xe=!1},500),Me=t.length){let e="";for(var i=0;i<Me;i++){let s=new SBConversation([new SBMessage(t[i])],t[i]);e+=lt.getListCode(s),Ce.push(s)}De++,C.append(e)}e.find(" > .sb-loading").remove(),SBF.event("SBAdminConversationsLoaded",{conversations:t})})}}),e(document).on("SBMessageDeleted",function(){let e=SBChat.conversation.getLastMessage();0!=e?C.find("li.sb-active p").html(e.message):(C.find("li.sb-active").remove(),lt.clickFirst(),lt.scrollTo())}),e(document).on("SBMessageSent",function(s,a){let n=a.conversation_id,r=(w(n),o("Error. Message not sent to")),l=a.conversation,c=a.user;a.conversation_status_code&&lt.updateMenu(),st.messenger.check(l)&&st.messenger.send(c.getExtra("facebook-id").value,l.get("extra"),a.message,a.attachments,a.message_id,a.message_id,e=>{for(var t=0;t<e.length;t++)e[t]&&e[t].error&&i(r+" Messenger: "+e[t].error.message,"info",!1,"error-fb")}),st.whatsapp.check(l)&&st.whatsapp.send(st.whatsapp.activeUserPhone(c),a.message,a.attachments,l.get("extra"),e=>{(e.ErrorCode||e.meta&&!e.meta.success)&&i(r+" WhatsApp: "+("ErrorCode"in e?e.errorMessage:e.meta.developer_message),"info",!1,"error-wa")}),st.telegram.check(l)&&st.telegram.send(l.get("extra"),a.message,a.attachments,n,e=>{e&&e.ok||i(r+" Telegram: "+JSON.stringify(e),"info",!1,"error-tg")}),st.viber.check(l)&&st.viber.send(c.getExtra("viber-id").value,a.message,a.attachments,e=>{e&&"ok"==e.status_message||i(r+" Viber: "+JSON.stringify(e),"info",!1,"error-vb")}),st.zalo.check(l)&&st.zalo.send(c.getExtra("zalo-id").value,a.message,a.attachments,e=>{e&&e.error.error&&i(r+" Zalo: "+e.error.message?e.error.message:e.message,"info",!1,"error-za")}),st.twitter.check(l)&&st.twitter.send(c.getExtra("twitter-id").value,a.message,a.attachments,e=>{e&&!e.event?i(JSON.stringify(e),"info",!1,"error-tw"):a.attachments.length>1&&t("Only the first attachment was sent to Twitter.")}),st.line.check(l)&&st.line.send(c.getExtra("line-id").value,a.message,a.attachments,n,e=>{e.error&&i(r+" LINE: "+JSON.stringify(e),"info",!1,"error-ln")}),st.wechat.check(l)&&st.wechat.send(c.getExtra("wechat-id").value,a.message,a.attachments,e=>{e&&"ok"==e.errmsg||i(r+" WeChat: "+JSON.stringify(e),"info",!1,"error-wc")}),SB_ADMIN_SETTINGS.smart_reply&&R.html(""),SB_ADMIN_SETTINGS.assign_conversation_to_agent&&SBF.null(l.get("agent_id"))&&lt.assignAgent(n,SB_ACTIVE_AGENT.id,()=>{SBChat.conversation.id==n&&(SBChat.conversation.set("agent_id",SB_ACTIVE_AGENT.id),e(A).find("#conversation-agent > p").attr("data-value",SB_ACTIVE_AGENT.id).html(SB_ACTIVE_AGENT.full_name))})}),e(document).on("SBNewMessagesReceived",function(e,s){let a=s.messages;for(var n=0;n<a.length;n++){let e=a[n],r=e.payload(),l=SBF.isAgent(e.get("user_type"));if(setTimeout(function(){B.find(".sb-top .sb-status-typing").remove()},300),dt.must_translate){let t=B.find(`[data-id="${e.id}"]`),i=!!r["original-message"]&&r["original-message"];i?(t.replaceWith(e.getCode()),B.find(`[data-id="${e.id}"] .sb-menu`).prepend(`<li data-value="translation">${o("View translation")}</li>`),SB_ADMIN_SETTINGS.smart_reply&&st.dialogflow.smartReply(SBF.escape(i))):e.message&&st.dialogflow.translate([e.message],SB_ADMIN_SETTINGS.active_agent_language,i=>{i&&(e.payload("translation",i[0]),e.payload("translation-language",SB_ADMIN_SETTINGS.active_agent_language),t.replaceWith(e.getCode()),B.find(`[data-id="${e.id}"] .sb-menu`).prepend(`<li data-value="original">${o("View original message")}</li>`)),SB_ADMIN_SETTINGS.smart_reply&&st.dialogflow.smartReply(i[0]),C.find(`[data-conversation-id="${s.conversation_id}"] p`).html(i[0])},[e.id],SBChat.conversation.id)}else SB_ADMIN_SETTINGS.smart_reply&&st.dialogflow.smartReply(e.message);r&&(r.department&&lt.setActiveDepartment(r.department),r.agent&&lt.setActiveAgent(r.agent)),("ErrorCode"in r||r.errors&&r.errors.length)&&i("Error. Message not sent to WhatsApp. Error message: "+(r.ErrorCode?r.ErrorCode:r.errors[0].title)),"whatsapp-templates"in r&&t(`Message sent as text message.${"whatsapp-template-fallback"in r?" The user has been notified via WhatsApp Template notification.":""}`),"whatsapp-template-fallback"in r&&!("whatsapp-templates"in r)&&t("The user has been notified via WhatsApp Template notification."),l||SBChat.conversation.id!=s.conversation_id||SBChat.user_online||rt.setActiveUserStatus()}lt.update()}),e(document).on("SBNewConversationCreated",function(){lt.update()}),e(document).on("SBEmailSent",function(){t("The user has been notified by email.")}),e(document).on("SBSMSSent",function(){t("The user has been notified by text message.")}),e(document).on("SBNotificationsSent",function(e,i){t(`The user ${i.includes("cron")?"will be":"has been"} notified by email${i.includes("sms")?" and text message":""}.`)}),e(document).on("SBTyping",function(e,t){lt.typing(t)}),e($).on("input",".sb-search-btn input",function(){lt.search(this)}),e(A).on("click",".sb-admin-list .sb-search-btn i",function(){SBF.searchClear(this,()=>{lt.search(e(this).next())})}),e(F).on("click","li",function(t){let i=C.parent();if(a(i))return t.preventDefault(),!1;setTimeout(()=>{let t=lt.filters();De=1,Me=1,SBF.ajax({function:"get-conversations",status_code:t[0],department:t[1],source:t[2],tag:t[3]},s=>{if(lt.populateList(s),B.attr("data-conversation-status",t[0]),s.length){if(!We){if(SBChat.conversation){let e=w(SBChat.conversation.id);e.length?e.sbActive(!0):t[0]==SBChat.conversation.status_code?C.prepend(lt.getListCode(SBChat.conversation)):lt.clickFirst()}else lt.clickFirst();lt.scrollTo()}}else A.find(".sb-board").addClass("sb-no-conversation"),SBChat.conversation=!1;e(this).closest(".sb-filter-btn").attr("data-badge",F.slice(1).toArray().reduce((t,i)=>t+!!e(i).find("li.sb-active").data("value"),0)),i.sbLoading(!1)})},100)}),e(A).on("click",".sb-user-details .sb-profile,.sb-top > a",function(){let e=C.find(".sb-active").attr("data-user-id");s().id!=e&&s(Re[e]),ct.show(s().id)}),e(k).on("click",".sb-profile-list-conversation li",function(){let a=e(this).find("label"),r=a.html();switch(e(this).attr("data-id")){case"location":i('<iframe src="https://maps.google.com/maps?q='+r.replace(", ","+")+'&output=embed"></iframe>',"map");break;case"timezone":SBF.getLocationTimeString(s().extra,e=>{n(!1),i(e)});break;case"current_url":window.open("//"+(SBF.null(a.attr("data-value"))?r:a.attr("data-value")));break;case"conversation-source":let c=r.toLowerCase();"whatsapp"==c&&s().getExtra("phone")?window.open("https://wa.me/"+st.whatsapp.activeUserPhone()):"facebook"==c?window.open("https://www.facebook.com/messages/t/"+SBChat.conversation.get("extra")):"instagram"==c?window.open("https://www.instagram.com/direct/inbox/"):"twitter"==c&&window.open("https://twitter.com/messages/");break;case"wp-id":window.open(window.location.href.substr(0,window.location.href.lastIndexOf("/"))+"/user-edit.php?user_id="+s().getExtra("wp-id").value);break;case"envato-purchase-code":n(),SBF.ajax({function:"envato",purchase_code:r},e=>{let s="";if(e&&e.item){e.name=e.item.name;for(var a in e)!h(e[a])&&isNaN(e[a])||(s+=`<b>${SBF.slugToString(a)}</b> ${e[a]} <br>`);n(!1),i(s,"info",!1,"sb-envato-box")}else t(SBF.slugToString(e))});break;case"email":case"cc":if(SBChat.conversation&&"em"==SBChat.conversation.get("source")){let e=SBChat.conversation.get("extra").split(","),t='<div data-type="repeater" class="sb-setting sb-type-repeater"><div class="input"><div class="sb-repeater">';for(var l=0;l<e.length;l++)t+=`<div class="repeater-item"><div><input data-id="cc" type="text" value="${e[l]}"></div><i class="sb-icon-close"></i></div>`;t+=`</div><div class="sb-btn sb-btn-white sb-repeater-add sb-icon"><i class="sb-icon-plus"></i>${o("Add new item")}</div></div></div>`,dt.genericPanel("cc","Manage CC",t,["Save changes"],"",!0)}}}),e(I).on("click",".sb-user-details-close",function(){A.find('.sb-menu-mobile [data-value="panel"]').click().sbActive(!0)}),e(M).on("click",'.sb-intent-add [data-value="add"]',function(){M.find("> div > .sb-type-text").last().after('<div class="sb-setting sb-type-text"><input type="text"></div>')}),e(M).on("click",'.sb-intent-add [data-value="previous"],.sb-intent-add [data-value="next"]',function(){let t=M.find(".sb-first input"),i=t.val(),s="next"==e(this).attr("data-value"),a=SBChat.conversation.getUserMessages(),n=a.length;for(var o=0;o<n;o++)if(a[o].message==i&&(s&&o<n-1||!s&&o>0)){o+=s?1:-1,t.val(a[o].message),M.attr("data-message-id",a[o].id),st.openAI.generateQuestions(a[o].message);break}}),e(M).on("click",".sb-send",function(){st.dialogflow.submitIntent(this)}),e(M).on("input",".sb-search-btn input",function(){st.dialogflow.searchIntents(e(this).val())}),e(M).on("click",".sb-search-btn i",function(){SBF.searchClear(this,()=>{st.dialogflow.searchIntents(e(this).val())})}),e(M).on("click","#sb-intent-preview",function(){st.dialogflow.previewIntentDialogflow(M.find("#sb-intents-select").val())}),e(M).on("click","#sb-qea-preview",function(){st.dialogflow.previewIntent(M.find("#sb-qea-select").val())}),e(M).on("change","#sb-intents-select",function(){let t=e(this).val();M.find(".sb-bot-response").css("opacity",t?.5:1).find("textarea").val(t?st.dialogflow.getIntent(t).messages[0].text.text[0]:st.dialogflow.original_response),M.find("#sb-train-chatbots").val(t?"dialogflow":"")}),e(M).on("change","#sb-qea-select",function(){let t=e(this).val();M.find(".sb-bot-response").setClass("sb-disabled",t).find("textarea").val(t?st.dialogflow.qea[t][1]:st.dialogflow.original_response),M.find("#sb-train-chatbots").val(t?"dialogflow":"")}),e(M).on("change","textarea",function(){clearTimeout(ge),ge=setTimeout(()=>{st.dialogflow.original_response=M.find("textarea").val()},500)}),e(M).on("change","#sb-train-chatbots",function(){M.find(".sb-type-text:not(.sb-first)").setClass("sb-hide","open-ai"==e(this).val())}),e(Ie).on("click","li",function(t){let s=e(this).parent().parent();return e(this).data("id")==s.find(" > p").attr("data-id")?(setTimeout(()=>{e(this).sbActive(!1)},100),!0):SBChat.conversation?(s.sbLoading()||i(`${o("All agents assigned to the new department will be notified. The new department will be")} ${e(this).html()}.`,"alert",()=>{let t=e(this).data("id");s.sbLoading(!0),lt.assignDepartment(SBChat.conversation.id,t,()=>{lt.setActiveDepartment(t),s.sbLoading(!1)})}),t.preventDefault(),!1):(e(this).parent().sbActive(!1),t.preventDefault(),!1)}),e(A).on("click","#conversation-agent li",function(t){let s=e(this).parent().parent(),a=e(this).data("id");return a==s.find(" > p").attr("data-value")||(SBChat.conversation?(s.sbLoading()||i(`${o("The new agent will be")} ${e(this).html()}.`,"alert",()=>{s.sbLoading(!0),lt.assignAgent(SBChat.conversation.id,a,()=>{lt.setActiveAgent(a),s.sbLoading(!1)})}),t.preventDefault(),!1):(e(this).parent().sbActive(!1),t.preventDefault(),!1))}),N.on("click","> i,.sb-edit-note",function(t){let i=!!e(this).hasClass("sb-edit-note")&&e(this).closest("[data-id]");if(dt.genericPanel("notes",i?"Edit note":"Add new note",`<div class="sb-setting sb-type-textarea"><textarea${i?' data-id="'+i.attr("data-id")+'"':""} placeholder="${o("Write here your note...")}">${i?i.find(".sb-note-text").html().replace(/<a\s+href="([^"]*)".*?>(.*?)<\/a>/gi,"$1").replaceAll("<br>","\n"):""}</textarea></div>`,[[i?"Update note":"Add note",i?"check":"plus"]]),st.is("dialogflow")&&SB_ADMIN_SETTINGS.note_data_scrape){let e="";for(var s in SB_ADMIN_SETTINGS.note_data_scrape)e+=`<option value="${s}">${SB_ADMIN_SETTINGS.note_data_scrape[s]}</option>`;k.find("#sb-add-note").parent().prepend(`<div id="note-ai-scraping" class="sb-setting sb-type-select"><select><option value="">${o("Data scraping")}</option>${e}</select></div>`)}return t.preventDefault(),!1}),e(k).on("change","#note-ai-scraping select",function(){let i=e(this).val();i&&!a(e(this).parent())&&SBF.ajax({function:"data-scraping",conversation_id:SBChat.conversation.id,prompt_id:i},i=>{if(i&&i.error)return console.error(i),t(i.error.message,"error");e(this).parent().sbLoading(!1);let s=k.find(".sb-notes-box textarea");s.val((s.val()+"\n"+i).trim())})}),N.on("click",".sb-delete-note",function(){let t=e(this).parents().eq(1);lt.notes.delete(SBChat.conversation.id,t.attr("data-id"),e=>{!0===e?t.remove():SBF.error(e)})}),e(k).on("click","#sb-add-note, #sb-update-note",function(){let i=e(this).parent().parents().eq(1).find("textarea"),s=i.val(),n=i.attr("data-id");if(0==s.length)SBForm.showErrorMessage(k.find(".sb-notes-box"),"Please write something...");else{if(a(this))return;s=SBF.escape(s),lt.notes.add(SBChat.conversation.id,SB_ACTIVE_AGENT.id,SB_ACTIVE_AGENT.full_name,s,a=>{Number.isInteger(a)||!0===a?(e(this).sbLoading(!1),k.sbHideLightbox(),n&&N.find(`[data-id="${n}"]`).remove(),lt.notes.update([{id:n||a,conversation_id:SBChat.conversation.id,user_id:SB_ACTIVE_AGENT.id,name:SB_ACTIVE_AGENT.full_name,message:s}],!0),i.val(""),t(n?"Note successfully updated.":"New note successfully added.")):SBForm.showErrorMessage(a)},n)}}),E.on("click","> i",function(e){let t=lt.tags.getAll(SBChat.conversation.details.tags);SBChat.conversation.details.tags;return dt.genericPanel("tags","Manage tags",t?'<div class="sb-tags-cnt">'+t+"</div>":"<p>"+o("Add tags from Settings > Admin > Tags.")+"</p>",["Save tags"]),e.preventDefault(),!1}),e(k).on("click",".sb-tags-cnt > span",function(){e(this).toggleClass("sb-active")}),e(k).on("click","#sb-add-tag",function(){e('<input type="text">').insertBefore(this)}),e(k).on("click","#sb-save-tags",function(){if(a(this))return;let i=k.find(".sb-tags-box").find("span.sb-active").map(function(){return e(this).attr("data-value")}).toArray(),s=SBChat.conversation.id;SBF.ajax({function:"update-tags",conversation_id:s,tags:i},a=>{if(e(this).sbLoading(!1),!0===a&&(lt.tags.update(i),SBChat.conversation&&s==SBChat.conversation.id)){let t=lt.filters()[3],a=w(s);if(SBChat.conversation.set("tags",i),t&&!i.includes(t))a.remove(),lt.clickFirst();else if(SB_ADMIN_SETTINGS.tags_show){let t=a.find(".sb-tags-area"),s=lt.tags.codeLeft(i);t.length?t.replaceWith(s):e(s).insertAfter(a.find(".sb-name"))}}k.sbHideLightbox(),t(!0===a?"Tags have been successfully updated.":a)})}),e(R).on("click","span",function(){SBChat.insertText(e(this).text()),R.html("")}),e(R).on("mouseover","span",function(){ge=setTimeout(()=>{e(this).addClass("sb-suggestion-full")},2500)}),e(R).on("mouseout","span",function(){clearTimeout(ge),R.find("span").removeClass("sb-suggestion-full")}),e(A).on("click",".sb-list .sb-menu > li",function(){let t=e(this).closest("[data-id]"),i=t.attr("data-id"),s=SBChat.conversation.getMessage(i),a=s.get("user_type"),n=e(this).attr("data-value");switch(n){case"delete":SBChat.user_online?SBF.ajax({function:"update-message",message_id:i,message:"",attachments:[],payload:{event:"delete-message"}},()=>{SBChat.conversation.deleteMessage(i),t.remove()}):SBChat.deleteMessage(i);break;case"translation":case"original":let r="translation"==n,l=["translation","translation-language","original-message","original-message-language"],c=l.map(e=>s.payload(e)).concat(s.details.message);s.set("message",r?s.payload("translation")||s.get("message"):s.payload("original-message")||s.get("message")),l.forEach(e=>delete s.details.payload[e]),t.replaceWith(s.getCode().replace('sb-menu">',`sb-menu"><li data-value="${r?"original":"translation"}">${o(r?"View original message":"View translation")}</li>`)),l.forEach((e,t)=>s.payload(e,c[t])),s.details.message=c[4];break;case"bot":st.dialogflow.showCreateIntentBox(e(this).closest("[data-id]").attr("data-id"));break;case"reply":let d=SBF.isAgent(a),u=s.get("message");u||s.attachments.forEach(e=>{u+='<i class="sb-icon-clip"></i>'+e[0]}),A.find(".sb-editor [data-reply]").remove(),A.find(".sb-editor").prepend(`<div data-reply="${i}"${d?' class="sb-reply-color"':""}><span>${d&&k||!d&&!k?o("You"):s.get("full_name")}</span>${u}<i class="sb-icon-close" onclick="SBChat.cancelReply()"></i></div>`),T.addClass("sb-reply-active")}}),e(A).on("click",".sb-filter-btn i",function(){e(this).parent().toggleClass("sb-active")}),e(A).on("click",".sb-filter-star",function(){e(this).parent().find("li").sbActive(!1),e(this).parent().find(`li[data-value="${e(this).sbActive()?"":e(this).attr("data-value")}"]`).last().sbActive(!0).click(),e(this).toggleClass("sb-active")}),L.on("click","#sb-attachments-filter li",function(){let t=L.find("a:not(.sb-collapse-btn)"),i=e(this).attr("data-value");t.each(function(){e(this).setClass("sb-hide",i&&SBF.getFileType(e(this).attr("href"))!=i)}),l(L,160)}),e(k).on("click",".sb-cc-box #sb-save-changes",function(){let t=k.find(".sb-cc-box .repeater-item input").map(function(){return e(this).val()}).get().join(",");a(this),SBF.ajax({function:"update-conversation-extra",conversation_id:SBChat.conversation.id,extra:t||"NULL"},()=>{SBChat.conversation.set("extra",t),lt.cc(t.split(",")),e(this).sbLoading(!1),k.sbHideLightbox()})}),SBF.getURL("user")&&(x.find(".sb-admin-nav #sb-users").click(),setTimeout(()=>{ct.show(SBF.getURL("user"))},500)),e(U).on("click","th :checkbox",function(){U.find("td :checkbox").prop("checked",e(this).prop("checked"))}),e(U).on("click",":checkbox",function(){let e=G.find('[data-value="delete"]');U.find("td input:checked").length?e.removeAttr("style"):e.hide()}),e(P).on("click","li",function(){rt.filter(e(this).data("type"))}),e(q).on("click","li",function(){let t=q.closest(".sb-filter-btn");setTimeout(()=>{rt.get(e=>{rt.populate(e)}),t.attr("data-badge",q.toArray().reduce((t,i)=>t+!!e(i).find("li.sb-active").data("value"),0))},100),t.sbActive(!1)}),e(G).on("input",".sb-search-btn input",function(){rt.search(this)}),e(G).on("click",".sb-search-btn i",function(){SBF.searchClear(this,()=>{rt.search(e(this).next())})}),e(U).on("click","th:not(:first-child)",function(){let t=e(this).hasClass("sb-order-asc")?"DESC":"ASC";e(this).toggleClass("sb-order-asc"),e(this).siblings().sbActive(!1),e(this).sbActive(!0),rt.sort(e(this).data("field"),t)}),e(U).parent().on("scroll",function(){!Xe&&!rt.search_query&&d(this,!0)&&Ge&&(Xe=!0,G.append('<div class="sb-loading-global sb-loading sb-loading-pagination"></div>'),rt.get(e=>{if(setTimeout(()=>{Xe=!1},500),Ge=e.length){let i="";for(var t=0;t<Ge;t++){let s=new SBUser(e[t],e[t].extra);i+=rt.getRow(s),Re[s.id]=s}je++,U.find("tbody").append(i)}G.find(" > .sb-loading-pagination").remove()},!1,!0))}),e(V).on("click",".sb-delete",function(){if(SB_ACTIVE_AGENT.id==s().id)return t("You cannot delete yourself.","error");i("This user will be deleted permanently including all linked data, conversations, and messages.","alert",function(){rt.delete(s().id)})}),e(U).on("click","td:not(:first-child)",function(){ct.show(e(this).parent().attr("data-user-id"))}),e(O).on("click",".sb-top-bar .sb-edit",function(){ct.showEdit(s())}),e(G).on("click",".sb-new-user",function(){V.addClass("sb-user-new"),V.find(".sb-top-bar .sb-profile span").html(o("Add new user")),V.find(".sb-top-bar .sb-save").html(`<i class="sb-icon-check"></i>${o("Add user")}`),V.find("input,select,textara").removeClass("sb-error"),V.removeClass("sb-cloud-admin"),"admin"==SB_ACTIVE_AGENT.user_type&&V.find("#user_type").find("select").html(`<option value="user">${o("User")}</option><option value="agent">${o("Agent")}</option><option value="admin">${o("Admin")}</option>`),ct.clear(V),ct.boxClasses(V),ct.updateRequiredFields("user"),V.sbShowLightbox()}),e(V).on("click",".sb-save",function(){if(a(this))return;let i=!!V.hasClass("sb-user-new"),n=V.attr("data-user-id"),r=ct.getAll(V.find(".sb-details")),l=ct.getAll(V.find(".sb-additional-details"));return e.map(r,function(e,t){r[t]=e[0]}),ct.errors(V)?(ct.showErrorMessage(V,SBF.isAgent(V.find("#user_type :selected").val())?"First name, last name, password and a valid email are required. Minimum password length is 8 characters.":V.find("#password").val().length<8?"Minimum password length is 8 characters.":"First name is required."),void e(this).sbLoading(!1)):SB_ACTIVE_AGENT.id==s().id&&"agent"==r.user_type[0]&&"admin"==SB_ACTIVE_AGENT.user_type?(ct.showErrorMessage(V,"You cannot change your status from admin to agent."),void e(this).sbLoading(!1)):(r.user_type||(r.user_type="user"),void SBF.ajax({function:i?"add-user":"update-user",user_id:n,settings:r,settings_extra:l},a=>{if(SBF.errorValidation(a,"duplicate-email")||SBF.errorValidation(a,"duplicate-phone"))return ct.showErrorMessage(V,`This ${SBF.errorValidation(a,"duplicate-email")?"email":"phone number"} is already in use.`),void e(this).sbLoading(!1);i&&(n=a,s(new SBUser({id:n}))),s().update(()=>{Re[n]=s(),i?(ct.clear(V),rt.update()):(rt.updateRow(s()),A.sbActive()&&lt.updateUserDetails(),n==SB_ACTIVE_AGENT.id&&(SBF.loginCookie(a[1]),SB_ACTIVE_AGENT.full_name=s().name,SB_ACTIVE_AGENT.profile_image=s().image,x.find(".sb-account").setProfile())),i&&V.find(".sb-profile").setProfile(o("Add new user")),e(this).sbLoading(!1),i||k.sbHideLightbox(),t(i?"New user added":"User updated")}),SBF.event("SBUserUpdated",{new_user:i,user_id:n})}))}),e(V).on("change","#user_type",function(){let t=e(this).find("option:selected").val();ct.boxClasses(V,t),ct.updateRequiredFields(t)}),e(O).on("click",".sb-user-conversations li",function(){lt.open(e(this).attr("data-conversation-id"),e(this).find("[data-user-id]").attr("data-user-id"))}),e(O).on("click",".sb-start-conversation",function(){lt.open(-1,s().id),lt.openConversation(-1,s().id),We&&lt.mobileOpenConversation()}),e(O).on("click",".sb-top-bar [data-value]",function(){lt.showDirectMessageBox(e(this).attr("data-value"),[s().id])}),e(G).on("click",".sb-top-bar [data-value]",function(){let s=e(this).data("value"),a=rt.getSelected();switch(s){case"whatsapp":m(a);break;case"message":case"custom_email":case"sms":lt.showDirectMessageBox(s,a);break;case"csv":rt.csv();break;case"delete":if(a.includes(SB_ACTIVE_AGENT.id))return t("You cannot delete yourself.","error");i("All selected users will be deleted permanently including all linked data, conversations, and messages.","alert",()=>{rt.delete(a),e(this).hide(),U.find("th:first-child input").prop("checked",!1)})}}),e(k).on("click",".sb-send-direct-message",function(){let i=e(this).attr("data-type")?e(this).attr("data-type"):D.attr("data-type"),s="whatsapp"==i,n=s?k.find("#sb-whatsapp-send-template-box"):D,o=n.find(".sb-direct-message-subject input").val(),r=s?"":n.find("textarea").val(),l=n.find(".sb-direct-message-users").val().replace(/ /g,""),d=!1,u=!1,p=[],h=!1;if(s){let e=n.find("#sb-whatsapp-send-template-list");d=e.val(),u=e.find("option:selected").attr("data-languages"),h=e.find("option:selected").attr("data-phone-id"),p=["header","body","button"].map(e=>n.find(`#sb-whatsapp-send-template-${e}`).val())}if(SBForm.errors(n))SBForm.showErrorMessage(n,"Please complete the mandatory fields.");else{if(a(this))return;let s=[];if((r.includes("recipient_name")||p.join("").includes("recipient_name"))&&s.push("first_name","last_name"),(r.includes("recipient_email")||p.join("").includes("recipient_email"))&&s.push("email"),"message"==i)SBF.ajax({function:"direct-message",user_ids:l,message:r},a=>{e(this).sbLoading(!1);let d=SB_ADMIN_SETTINGS.notify_user_email,u=SB_ADMIN_SETTINGS.sms_active_users;if(SBF.errorValidation(a))return SBForm.showErrorMessage(n,"An error has occurred. Please make sure all user ids are correct.");(d||u)&&SBF.ajax({function:"get-users-with-details",user_ids:l,details:s.concat(d&&u?["email","phone"]:[d?"email":"phone"])},e=>{d&&e.email.length?c(e,"email",r,0,u?e.phone:[],"email",o):u&&e.phone.length?c(e,"phone",r,0,[],"sms"):k.sbHideLightbox()}),t(`${SBF.slugToString(i)} sent to all users.`)});else{let t="custom_email"==i?"email":"phone";SBF.ajax({function:"get-users-with-details",user_ids:l,details:s.concat([t])},s=>{if(!s[t].length)return e(this).sbLoading(!1),SBForm.showErrorMessage(n,"No users found.");c(s,t,r,0,[],i,o,d,p,u,h)})}}}),e(G).on("click",".sb-filter-btn > i",function(){e(this).parent().toggleClass("sb-active")}),SBF.getURL("setting")&&at.open(SBF.getURL("setting"),!0),e(H).on("click"," > .sb-tab > .sb-nav [id]",function(){let t=e(this).attr("id").substr(4);SBF.getURL("setting")!=t&&f("?setting="+t)}),e(k).on("click",'.sb-repeater-upload, [data-type="upload-image"] .image, [data-type="upload-file"] .sb-btn, #sb-chatbot-add-files, #sb-import-settings a, #sb-import-users a',function(){let s="";ue=this,"sb-chatbot-add-files"==e(this).attr("id")?(s=".pdf,.txt",ie.find(".sb-pending").remove(),st.openAI.train.skip_files=[],pe=function(){let e=de.prop("files"),t="";for(var i=0;i<e.length;i++){let s=parseInt(e[i].size/1e3);t+=`<tr class="sb-pending" data-name="${e[i].name}"><td><input type="checkbox" /></td><td>${e[i].name}<label>${o("Pending")}</label></td><td>${s||1} KB</td><td><i class="sb-icon-delete"></i></td></tr>`}ie.append(t)}):"sb-import-settings"==e(this).parent().parent().attr("id")?(s=".json",he=(s=>{a(this)||(SBF.ajax({function:"import-settings",file_url:s},s=>{s?t("Settings saved. Reload to apply the changes."):i(s),e(this).sbLoading(!1)}),he=!1)})):"sb-import-users"==e(this).parent().parent().attr("id")?(s=".csv",he=(s=>{a(this)||(SBF.ajax({function:"import-users",file_url:s},s=>{s?t("Users imported successfully."):i(s),e(this).sbLoading(!1)}),he=!1)})):e(this).hasClass("image")?s=".png,.jpg,.jpeg,.gif,.webp":e(this).hasClass("sb-repeater-upload")&&(he=(t=>{let i=e(this).parent();i.find(".repeater-item:last-child input").val()&&at.repeater.add(this),i.find(".repeater-item:last-child input").val(t)})),de.attr("accept",s).prop("value","").click()}),e(H).on("click",'[data-type="upload-image"] .image > i',function(t){return SBF.ajax({function:"delete-file",path:e(this).parent().attr("data-value")}),e(this).parent().removeAttr("data-value").css("background-image",""),t.preventDefault(),!1}),e(k).on("click",".sb-repeater-add",function(){at.repeater.add(this)}),e(k).on("click",".repeater-item > i",function(){setTimeout(()=>{at.repeater.delete(this)},100)}),at.initColorPicker(),e(H).find('[data-type="color"]').focusout(function(){let t=e(this).find("input").val();"rgb(255, 255, 255)"==t&&["color-admin-1","color-1"].includes(e(this).attr("id"))&&(t=""),setTimeout(()=>{e(this).find("input").val(t),e(this).find(".color-preview").css("background-color",t)},300),at.set(e(this).attr("id"),[t,"color"])}),e(H).on("click",".sb-type-color .input i",function(t){e(this).parent().find("input").removeAttr("style").val("")}),e(H).on("click",".sb-color-palette span",function(){let t=e(this).sbActive();e(this).closest(".sb-repeater").find(".sb-active").sbActive(!1),e(this).sbActive(!t)}),e(H).on("click",".sb-color-palette ul li",function(){e(this).parent().parent().attr("data-value",e(this).data("value")).find("span").sbActive(!1)}),e(H).on("click",'[data-type="select-images"] .input > div',function(){e(this).siblings().sbActive(!1),e(this).sbActive(!0)}),e(H).on("click",".sb-select-checkbox-input",function(){e(this).toggleClass("sb-active")}),e(H).on("click",".sb-select-checkbox input",function(){let t=e(this).closest("[data-type]");t.find(".sb-select-checkbox-input").val(at.get(t)[1].join(", "))}),e(H).on("click",".sb-save-changes",function(){at.save(this)}),e(H).on("change",'#saved-replies [data-id="reply-name"], [data-id="rich-message-name"]',function(){e(this).val(e(this).val().replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,"").replace(/ /g,""))}),e(H).on("change",'#user-additional-fields [data-id="extra-field-name"]',function(){e(this).parent().next().find("input").val(SBF.stringToSlug(e(this).val()))}),e(H).on("click","#timetable-utc input",function(){e(this).val()||e(this).val(Ye.getTimezoneOffset()/60)}),e(H).on("click","#dialogflow-sync-btn a",function(e){let t="https://accounts.google.com/o/oauth2/auth?scope=https%3A%2F%2Fwww.googleapis.com/auth/dialogflow%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-translation%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-language&response_type=code&access_type=offline&redirect_uri="+SB_URL+"/apps/dialogflow/functions.php&client_id={client_id}&prompt=consent";if(!SB_ADMIN_SETTINGS.cloud||"auto"!=H.find("#google-sync-mode select").val()){let s=H.find("#google-client-id input").val();return s&&H.find("#google-client-secret input").val()?window.open(t.replace("{client_id}",s)):i("Before continuing enter Client ID and Client secret. Check the docs for more details."),e.preventDefault(),!1}if(SB_ADMIN_SETTINGS.credits)return window.open(t.replace("{client_id}",SB_ADMIN_SETTINGS.google_client_id)),e.preventDefault(),!1}),e(H).on("click","#dialogflow-redirect-url-btn a",function(e){return i(`<pre>${SB_URL}/apps/dialogflow/functions.php</pre>`),e.preventDefault(),!1}),e(H).on("click","#dialogflow-saved-replies a",function(t){return i("","alert",()=>{a(this)||SBF.ajax({function:"dialogflow-saved-replies"},()=>{e(this).sbLoading(!1)})}),t.preventDefault(),!1}),e(H).on("click","#test-email-user a, #test-email-agent a",function(){let t=e(this).parent().find("input").val();t&&t.indexOf("@")>0&&!a(this)&&SBF.ajax({function:"send-test-email",to:t,email_type:"test-email-user"==e(this).parent().parent().attr("id")?"user":"agent"},t=>{i(!0===t?"The message has been sent.":t,"info"),e(this).sbLoading(!1)})}),e(H).on("click","#email-server-troubleshoot a",function(e){return H.find("#test-email-user input").val(SB_ACTIVE_AGENT.email).next().click()[0].scrollIntoView({behavior:"smooth"}),e.preventDefault(),!1}),e(H).on("click","#test-sms-user a, #test-sms-agent a",function(){let t=e(this).parent().find("input").val();t&&!a(this)&&SBF.ajax({function:"send-sms",message:"Hello World!",to:t},t=>{i(t&&["sent","queued"].includes(t.status)?"The message has been sent.":`<pre>${JSON.stringify(t)}</pre>`),e(this).sbLoading(!1)})}),e(H).on("click",".sb-timetable > div > div > div",function(){let t=e(this).closest(".sb-timetable"),i=e(this).sbActive();if(e(t).find(".sb-active").sbActive(!1),i)e(this).sbActive(!1).find(".sb-custom-select").remove();else{let i=e(t).find("> .sb-custom-select").html();e(t).find(" > div .sb-custom-select").remove(),e(this).append(`<div class="sb-custom-select">${i}</div>`).sbActive(!0)}}),e(H).on("click",".sb-timetable .sb-custom-select span",function(){let t=[e(this).html(),e(this).attr("data-value")];e(this).closest(".sb-timetable").find("> div > div > .sb-active").html(t[0]).attr("data-value",t[1]),e(this).parent().sbActive(!1)}),e(H).on("click","#system-requirements a",function(e){let t="";return SBF.ajax({function:"system-requirements"},e=>{for(var i in e)t+=`<div class="sb-input"><span>${o(SBF.slugToString(i))}</span><div${e[i]?' class="sb-green"':""}>${o(e[i]?"Success":"Error")}</div></div>`;n(!1),dt.genericPanel("requirements","System requirements",t)}),n(),e.preventDefault(),!1}),e(H).on("click","#sb-path a",function(e){return SBF.ajax({function:"path"},e=>{i(`<pre>${e}</pre>`)}),e.preventDefault(),!1}),e(H).on("click","#sb-url a",function(e){return i(`<pre>${SB_URL}</pre>`),e.preventDefault(),!1}),e(H).on("click","#delete-leads a",function(t){return e(this).sbLoading()||i("All leads, including all the linked conversations and messages, will be deleted permanently.","alert",()=>{e(this).sbLoading(!0),SBF.ajax({function:"delete-leads"},()=>{i("Leads and conversations successfully deleted."),e(this).sbLoading(!1)})}),t.preventDefault(),!1}),e(H).on("click","#sb-export-settings a",function(t){if(t.preventDefault(),!a(this))return SBF.ajax({function:"export-settings"},t=>{_(t,"sb-export-settings-close","Settings exported"),e(this).sbLoading(!1)}),!1}),e(k).on("click","#sb-export-settings-close .sb-close, #sb-export-users-close .sb-close, #sb-export-report-close .sb-close",function(){SBF.ajax({function:"delete-file",path:k.find(".sb-dialog-box p pre").html()})}),SB_ADMIN_SETTINGS.cloud||(e(H).on("change","#push-notifications-provider select",function(){let t="pusher"==e(this).val();at.visibility(0,t),SBF.ajax({function:"update-sw",url:t?"https://js.pusher.com/beams/service-worker.js":"https://cdn.onesignal.com/sdks/web/v16/OneSignalSDK.sw.js"})}),e(H).on("click","#push-notifications-sw-path a",function(e){let t=b(location.href.replace(location.host,"")).replace("admin.php","");return t.includes("?")&&(t=t.substring(0,t.indexOf("?"))),i("<pre>"+t+"</pre>"),e.preventDefault(),!1})),e(H).on("click","#push-notifications-btn a",function(e){return SB_ADMIN_SETTINGS.cloud||"onesignal"==H.find("#push-notifications-provider select").val()?typeof OneSignal!=Ke?OneSignal.Slidedown.promptPush({force:!0}):SBF.serviceWorker.initPushNotifications():Notification.requestPermission(),e.preventDefault(),!1}),e(H).on("input","#sb-search-settings",function(){let t=e(this).val().toLowerCase();SBF.search(t,()=>{let i="",s=H.find(".sb-search-dropdown-items");if(t.length>2){let s=H.find("> .sb-tab > .sb-nav li").map(function(){return e(this).text().trim()}).get();H.find(".sb-setting").each(function(){let a=e(this).attr("data-keywords"),n=e(this).attr("id");if(a&&a.includes(t)||n&&n.replaceAll("-","-").includes(t)||e(this).find(".sb-setting-content").text().toLowerCase().includes(t)){let t=e(this).parent().index();i+=`<div data-tab-index="${t}" data-setting="${e(this).attr("id")}">${s[t]} > ${e(this).find("h2").text()}</div>`}})}s.html(i),s.outerHeight()>e(window).height()-100?(s.css("max-height",e(window).height()-100),s.addClass("sb-scroll-area")):s.removeClass("sb-scroll-area")})}),e(H).on("click",".sb-search-dropdown-items div",function(){let t=e(this).attr("data-tab-index"),i=H.find("> .sb-tab > .sb-nav li").eq(t);i.click(),i.get(0).scrollIntoView(),H.find("#"+e(this).attr("data-setting"))[0].scrollIntoView()}),e(H).on("click","#slack-button a",()=>(window.open("https://board.support/synch/?service=slack&plugin_url="+SB_URL+g()),!1)),e(H).on("click","#slack-test a",function(t){if(!a(this))return SBF.ajax({function:"send-slack-message",user_id:!1,full_name:SB_ACTIVE_AGENT.full_name,profile_image:SB_ACTIVE_AGENT.profile_image,message:"Lorem ipsum dolor sit amete consectetur adipiscing elite incidido labore et dolore magna aliqua.",attachments:[["Example link",SB_URL+"/media/user.svg"],["Example link two",SB_URL+"/media/user.svg"]],channel:H.find("#slack-channel input").val()},t=>{i(SBF.errorValidation(t)?"slack-not-active"==t[1]?"Please first activate Slack, then save the settings and reload the admin area.":"Error. Response: "+JSON.stringify(t):"success"==t[0]?"Slack message successfully sent. Check your Slack app!":JSON.stringify(t)),e(this).sbLoading(!1)}),t.preventDefault(),!1}),e(H).on("click","#tab-slack",function(){let e=H.find("#slack-agents .input");e.html('<div class="sb-loading"></div>'),SBF.ajax({function:"slack-users"},t=>{let i="";if(SBF.errorValidation(t,"slack-token-not-found"))i=`<p>${o("Synchronize Slack and save changes before linking agents.")}</p>`;else{let e='<option value="-1"></option>';for(s=0;s<t.agents.length;s++)e+=`<option value="${t.agents[s].id}">${t.agents[s].name}</option>`;for(var s=0;s<t.slack_users.length;s++)i+=`<div data-id="${t.slack_users[s].id}"><label>${t.slack_users[s].name}</label><select>${e}</select></div>`}e.html(i),at.set("slack-agents",[t.saved,"double-select"])})}),e(H).on("click","#slack-archive-channels a",function(t){t.preventDefault(),a(this)||SBF.ajax({function:"archive-slack-channels"},t=>{!0===t&&i("Slack channels archived successfully!"),e(this).sbLoading(!1)})}),e(H).on("click","#slack-channel-ids a",function(t){t.preventDefault(),a(this)||SBF.ajax({function:"slack-channels",code:!0},t=>{i(t,"info",!1,"","",!0),e(this).sbLoading(!1)})}),e(H).on("click",'#whatsapp-twilio-btn a, #whatsapp-twilio-get-configuartion-btn a, #sms-btn a, #wechat-btn a, #twitter-callback a, #viber-webhook a, #zalo-webhook a, [data-id="line-webhook"], #messenger-path-btn a',function(t){let s=e(this).closest("[id]").attr("id"),a="";if(t.preventDefault(),"line"==s){if(!(a=e(this).closest(".repeater-item").find('[data-id="line-secret"]').val()))return;a="?line_secret="+a}return i(`<pre>${SB_URL+("sms-btn"==s?"/include/api.php":"/apps/"+(s.includes("-")?s.substring(0,s.indexOf("-")):s)+"/post.php")+a+g().replace("&",a?"&":"?")}</pre>`),!1}),e(H).on("click",'[data-id="telegram-numbers-button"], #viber-button a, #whatsapp-360-button a',function(t){let s,n={"telegram-button":["#telegram-token input","telegram-synchronization",["result",!0]],"viber-button":["#viber-token input","viber-synchronization",["status_message","ok"]],"whatsapp-360-button":["#whatsapp-360-key input","whatsapp-360-synchronization",["success",!0]]},o=e(this).parent().attr("id"),r=!1;if(o)s=H.find(n[o][0]).val().trim();else{o={"telegram-numbers-button":"telegram-button"}[e(this).attr("data-id")],s=e(this).closest(".repeater-item").find(`[data-id="${{"telegram-button":"telegram-numbers-token"}[o]}"]`).val().trim(),r=!0}return n=n[o],t.preventDefault(),!(!s||a(this))&&(SBF.ajax({function:n[1],token:s,cloud_token:g(),is_additional_number:r},t=>{i(n[2][0]in t&&t[n[2][0]]==n[2][1]?"Synchronization completed.":JSON.stringify(t)),e(this).sbLoading(!1)}),!1)}),e(H).on("click","#whatsapp-test-template a",function(t){t.preventDefault();let s=e(this).parent().find("input").val();if(s&&!a(this))return SBF.ajax({function:"whatsapp-send-template",to:s},t=>{i(t?"error"in t?t.error.message?t.error.message:t.error:"Message sent, check your WhatsApp!":t),e(this).sbLoading(!1)}),!1}),e(H).on("click","#twitter-subscribe a",function(t){return t.preventDefault(),!a(this)&&(SBF.ajax({function:"twitter-subscribe",cloud_token:g()},t=>{i(!0===t?"Synchronization completed.":JSON.stringify(t)),e(this).sbLoading(!1)}),!1)}),SB_ADMIN_SETTINGS.cloud||e(H).on("click","#messenger-sync-btn a",()=>(window.open("https://board.support/synch/?service=messenger&plugin_url="+SB_URL+g()),!1)),e(H).on("change","#messenger-sync-mode select",function(){at.visibility(1,"manual"!=e(this).val())}),e(H).on("click","#messenger-unsubscribe a",function(t){return t.preventDefault(),i("","alert",()=>{if(a(this))return!1;SBF.ajax({function:"messenger-unsubscribe"},t=>{e(this).sbLoading(!1),t?i(JSON.stringify(t)):(H.find("#messenger-pages .repeater-item > i").click(),setTimeout(()=>{at.save(),i("Operation successful.")},300))})}),!1}),e(H).on("change","#open-ai-mode select",function(){at.visibility(2,"assistant"!=e(this).val())}),e(H).on("click","#wp-sync a",function(t){return t.preventDefault(),!a(this)&&(st.wordpress.ajax("wp-sync",{},t=>{!0===t||"1"===t?(rt.update(),i("WordPress users successfully imported.")):i("Error. Response: "+JSON.stringify(t)),e(this).sbLoading(!1)}),!1)}),e("body").on("click","#wp-admin-bar-logout",function(){SBF.logout(!1)}),e(H).on("click","#whatsapp-clear-flows a",function(e){e.preventDefault(),SBF.ajax({function:"whatsapp-clear-flows"})}),e(H).on("click","#tab-translations",function(){let e=H.find(".sb-translations > .sb-nav > ul");if(!e.html()){let i="";for(var t in SB_LANGUAGE_CODES)"en"!=t&&(i+=`<li data-code="${t}"><img src="${SB_URL}/media/flags/${t}.png" />${SB_LANGUAGE_CODES[t]}</li>`);e.html(i)}}),e(H).on("click",".sb-translations .sb-nav li",function(){at.translations.load(e(this).data("code"))}),e(H).on("click",".sb-translations .sb-menu-wide li",function(){H.find(`.sb-translations [data-area="${e(this).data("value")}"]`).sbActive(!0).siblings().sbActive(!1)}),e(H).on("click",".sb-add-translation",function(){H.find(".sb-translations-list > .sb-active").prepend(`<div class="sb-setting sb-type-text sb-new-translation"><input type="text" placeholder="${o("Enter original text...")}"><input type="text" placeholder="${o("Enter translation...")}"></div></div>`)}),e(H).on("input",".sb-search-translation input",function(){let t=e(this).val().toLowerCase();SBF.search(t,()=>{t.length>1&&H.find(".sb-translations .sb-content > .sb-active label").each(function(){let i=e(this).html().toLowerCase();if(i.includes(t)&&i!=Se){let t=H.find(".sb-scroll-area");return t[0].scrollTop=0,t[0].scrollTop=e(this).position().top-80,Se=i,!1}})})}),e(H).on("click",'[data-id="email-piping-sync"]',function(t){a(this)||(SBF.ajax({function:"email-piping",force:!0},t=>{i(!0===t?"Syncronization completed.":t),e(this).sbLoading(!1)}),t.preventDefault())}),e(H).on("click","#tab-automations",function(){at.automations.get(()=>{at.automations.populate(),n(!1)},!0),n()}),e(k).on("click",".sb-add-condition",function(){at.automations.addCondition(e(this).prev())}),e(k).on("change",".sb-condition-1 select",function(){at.automations.updateCondition(this)}),e(k).on("change",".sb-condition-2 select",function(){e(this).parent().next().setClass("sb-hide",["is-set","is-not-set"].includes(e(this).val()))}),e(z).on("click","li",function(){at.automations.populate(e(this).data("value"))}),e(J).on("click","li",function(){at.automations.show(e(this).attr("data-id"))}),e(W).on("click",".sb-add-automation",function(){at.automations.add()}),e(J).on("click","li i",function(){i("The automation will be deleted permanently.","alert",()=>{at.automations.delete(this)})});let me=["Settings > {R} won't work if Settings > {R2} is active."];e(H).on("click",".sb-setting",function(i){let s=e(this).attr("id");switch(e(this).hasClass("sb-type-multi-input")&&(s=e(i.target).parent().attr("id")),s){case"close-chat":case"close-message":H.find("#close-active input").is(":checked")&&H.find("#close-chat input").is(":checked")&&t(me[0].replace("{R}","Messages > Close message").replace("{R2}","Chat > Close chat"),"info");break;case"chat-timetable":case"follow-message":case"queue":case"routing":("queue"==s&&H.find("#queue-active input").is(":checked")||"routing"==s&&H.find("#routing input").is(":checked")||"follow-message"==s&&H.find("#follow-active input").is(":checked")||"chat-timetable"==s&&H.find("#chat-timetable-active input").is(":checked"))&&H.find("#dialogflow-human-takeover-active input").is(":checked")&&t("Since Settings > Artificial Intelligence > Human takeover is active, this option will only take effect during human takeover.","info");break;case"notify-agent-email":case"push-notifications":case"sms":("sms"==s&&H.find("#sms-active-agents input").is(":checked")||"push-notifications"==s&&H.find("#push-notifications-active input").is(":checked")||"notify-agent-email"==s&&H.find("#notify-agent-email input").is(":checked"))&&H.find("#dialogflow-human-takeover-active input").is(":checked")&&t("Since Settings > Artificial Intelligence > Human takeover is active, notifications will be sent only after the human takeover.","info");break;case"privacy":H.find("#privacy-active input").is(":checked")&&H.find("#registration-required select").val()&&t(me[0].replace("{R}","Messages > Privacy message").replace("{R2}","Users > Require registration"),"info");break;case"google-multilingual-translation":e(i.target).is(":checked")&&H.find("#open-ai-active input").is(":checked")&&!H.find("#open-ai-training-data-language select").val()&&t("If your OpenAI training data isn't in English, set the default language under OpenAI > Training data language.","info");break;case"open-ai-prompt":t("Custom prompts may break the chatbot, we advise leaving it empty.","info");break;case"open-ai-tokens":case"open-ai-temperature":case"open-ai-presence-penalty":case"open-ai-frequency-penalty":case"open-ai-logit-bias":case"open-ai-custom-model":t("This is an advanced setting, incorrect values may break the chatbot. If you're unsure, leave it empty.","info");break;case"open-ai-user-train-conversations":e(i.target).is(":checked")&&t("This method is not recommended. See the docs for details.","info")}}),e(P).on("click",'[data-type="online"]',function(){SB_ADMIN_SETTINGS.visitors_registration||t("Settings > Users > Register all visitors must be enabled to see online users.","info")}),e(H).on("click","#dialogflow-active,#dialogflow-welcome,#dialogflow-departments",function(e){t("Warning! We will stop supporting Dialogflow by the end of 2025. All its features will be available in Support Board through OpenAI. Please use OpenAI instead of Dialogflow.","info")}),e(H).on("change","#registration-required select",function(){let i=e(this).val();["registration-login"].includes(i)&&!H.find('[id="reg-email"] input').is(":checked")&&t("The email field is required to activate the login form.","info"),i&&H.find("#privacy-active input").is(":checked")&&t(me[0].replace("{R}","Messages > Privacy message").replace("{R2}","Users > Require registration"),"info")}),e(te).on("click","#sb-flow-add",function(){dt.genericPanel("flow-add","Enter the flow name",'<div class="sb-setting"><input type="text"></div>',["Add new flow"])}),e(k).on("click","#sb-add-new-flow",function(){st.openAI.flows.set(k.find(".sb-flow-add-box input").val().replace(/[^a-zA-Z\u00C0-\u1FFF\u2C00-\uD7FF\uAC00-\uD7A3]/g,"")),k.sbHideLightbox()}),e(le).on("click",".sb-flow-block",function(){le.find(".sb-flow-block,.sb-flow-add-block").sbActive(!1),e(this).sbActive(!0);let t,i=st.openAI.flows.blocks.get(),s="",a=e(this).attr("data-type"),n=`<div class="sb-title">{R}</div><div data-type="repeater" class="sb-setting sb-type-repeater"><div class="input"><div class="sb-repeater">{R2}</div><div class="sb-btn sb-btn-white sb-repeater-add sb-icon"><i class="sb-icon-plus"></i>${o("Add new item")}</div></div></div>`,r=`<div class="sb-title">${o("Conditions")}</div><div class="sb-flow-conditions"></div><div class="sb-add-condition sb-btn sb-icon sb-btn-white"><i class="sb-icon-plus"></i>${o("Add condition")}</div>`,l=`<div class="sb-title">${o("Message")}</div><div class="sb-setting"><textarea placeholder="${o("The message sent to the user...")}">${i.message}</textarea></div>`,c=st.openAI.getCode.select_user_details();switch(a){case"start":t=`<div class="sb-title">${o("Start event")}</div><div class="sb-setting"><select class="sb-flow-start-select"><option value="message"${"message"==i.start?" selected":""}>${o("User message")}</option><option value="conversation"${"conversation"==i.start?" selected":""}>${o("New conversation started")}</option><option value="load"${"load"==i.start?" selected":""}>${o("On page load")}</option></select></div><div class="sb-title sb-title-flow-start${"message"==i.start?"":" sb-hide"}">${o("User message")}</div><div data-type="repeater" class="sb-setting sb-flow-start-messages sb-type-repeater"><div class="input"><div class="sb-repeater"><div class="repeater-item"><div class="sb-setting"><textarea data-id="message"></textarea></div><i class="sb-icon-close"></i></div></div><div class="sb-btn sb-btn-white sb-repeater-add sb-icon"><i class="sb-icon-plus"></i>${o("Add message")}</div></div></div>${r}<div class="sb-title">${o("Disabled")}</div><div class="sb-setting"><input type="checkbox" id="sb-flow-disabled"${i.disabled?" checked":""}></div>`;break;case"button_list":i.options&&i.options.length||(i.options=[""]);for(d=0;d<i.options.length;d++)s+=`<div class="repeater-item"><div><input data-id type="text" value="${i.options[d]}"></div><i class="sb-icon-close"></i></div>`;t=l+n.replace("{R}",o("Buttons")).replace("{R2}",s);break;case"message":i.attachments&&i.attachments.length||(i.attachments=[""]);for(d=0;d<i.attachments.length;d++)s+=`<div class="repeater-item"><div><input data-id type="text" value="${i.attachments[d]}" placeholder="${o("Enter a link...")}"></div><i class="sb-icon-close"></i></div>`;t=l+n.replace("{R}",o("Attachments")).replace("{R2}",s).replace("</div></div></div>",'</div><i class="sb-repeater-upload sb-btn-icon sb-icon-clip"></i></div></div>');break;case"video":t=`${l}<div class="sb-title">${o("Video URL")}</div><div class="sb-setting"><input type="url" placeholder="${o("Enter a YouTube or Vimeo link...")}" value="${i.url}"></div>`;break;case"get_user_details":i.details&&i.details.length||(i.details=[["","",!1]]);for(d=0;d<i.details.length;d++)s+=`<div class="repeater-item"><div>${c.replace(`"${i.details[d][0]}"`,`"${i.details[d][0]}" selected`)}<div class="sb-setting"><input type="text" placeholder="${o("Enter a description...")}" value="${i.details[d][1]}" /></div><div class="sb-setting"><label>${o("Required")}</label><input type="checkbox" ${i.details[d][2]?" checked":""}></div></div><i class="sb-icon-close"></i></div>`;t=l+n.replace("{R}",o("User details")).replace("{R2}",s).replace("sb-type-repeater","sb-type-repeater sb-repeater-block-user-details");break;case"set_data":t=st.openAI.getCode.set_data(i.data);break;case"action":t=st.openAI.getCode.actions(i.actions);break;case"rest_api":let e=["headers","save_response"];t=`<div class="sb-title">${o("URL")}</div><div class="sb-setting"><input type="url" class="sb-rest-api-url" value="${i.url}"></div><div class="sb-title">${o("Method")}</div><div class="sb-setting"><select class="sb-rest-api-method"><option value="GET"${"GET"==i.method?" selected":""}>GET</option><option value="POST"${"POST"==i.method?" selected":""}>POST</option><option value="PUT"${"PUT"==i.method?" selected":""}>PUT</option><option value="PATH"${"PATH"==i.method?" selected":""}>PATH</option><option value="DELETE"${"DELETE"==i.method?" selected":""}>DELETE</option></select></div><div class="sb-title">${o("Body")}</div><div class="sb-setting"><textarea placeholder="JSON">${i.body}</textarea></div>`;for(var d=0;d<e.length;d++){let s=i[e[d]];s&&s.length||(s=[["",""]]),t+=`<div class="sb-title">${o(SBF.slugToString(e[d]))}</div><div data-type="repeater" class="sb-setting sb-type-repeater sb-repeater-block-rest-api sb-rest-api-${e[d]}"><div class="input"><div class="sb-repeater">`;for(var u=0;u<s.length;u++)t+=`<div class="repeater-item"><div>${1==d?c.replace(`"${s[u][0]}"`,`"${s[u][0]}" selected`):`<div class="sb-setting"><input type="text" placeholder="${o("Key")}" value="${s[u][0]}" /></div>`}<div class="sb-setting"><input type="text" placeholder="${o(1==d?"e.g. data.id":"Value")}" value="${s[u][1]}" /></div></div><i class="sb-icon-close"></i></div>`;t+=`</div><div class="sb-btn sb-btn-white sb-repeater-add sb-icon"><i class="sb-icon-plus"></i>${o("Add new item")}</div></div></div>`}break;case"condition":t=r}if(t+=`<div id="sb-block-delete" class="sb-btn-text"><i class="sb-icon-delete"></i>${o("Delete")}</div>`,dt.genericPanel("flow-block",SBF.slugToString(a),t,["Save changes"],"",!0),"start"==a||"condition"==a){Array.isArray(i.message)||(i.message=[{message:i.message}]);let e=k.find(".sb-flow-start-messages .sb-repeater"),t=at.repeater.set(i.message,e.find(".repeater-item:last-child"));at.automations.setConditions(i.conditions,k.find(".sb-flow-conditions")),t&&e.html(t)}He=[],le.find("> div").each(function(){He.push(e(this).find("> div")[0].scrollTop)})}),e(k).on("click",".sb-flow-block-box #sb-save-changes",function(){let t=st.openAI.flows.blocks.get(),i=k.find(".sb-flow-block-box");switch(t.message=i.find("textarea").val(),t.type){case"start":if(t.message=at.repeater.get(i.find(".sb-flow-start-messages .repeater-item")),t.start=i.find("select").val(),t.disabled=i.find("#sb-flow-disabled").is(":checked"),t.conditions=at.automations.getConditions(i.find(".sb-flow-conditions")),t.message.length&&t.message[0].message.trim().split(" ").length<3)return i.find(".sb-info").sbActive(!0).html(o("The message must contain at least 3 words."));break;case"button_list":t.options=i.find(".sb-repeater input").map(function(){return e(this).val().trim()}).get().filter(function(e){return""!=e});break;case"message":t.attachments=i.find(".sb-repeater input").map(function(){return e(this).val().trim()}).get().filter(function(e){return""!=e});break;case"video":t.url=i.find("input").val();break;case"get_user_details":t.details=i.find(".repeater-item").map(function(){return[[e(this).find("select").val(),e(this).find("input[type=text]").val(),e(this).find("input[type=checkbox]").is(":checked")]]}).get();break;case"action":case"set_data":t["action"==t.type?"actions":"data"]=i.find(".repeater-item").map(function(){return[[e(this).find("select").val(),e(this).find("input").length?e(this).find("input").val().replace(/https?:\/\/|["|:]/g,""):""]]}).get();break;case"rest_api":t.headers=i.find(".sb-rest-api-headers .repeater-item").map(function(){return[[e(this).find("input").eq(0).val(),e(this).find("input").eq(1).val()]]}).get(),t.save_response=i.find(".sb-rest-api-save_response .repeater-item").map(function(){return[[e(this).find("select").val(),e(this).find("input").val()]]}).get(),t.url=i.find(".sb-rest-api-url").val(),t.method=i.find(".sb-rest-api-method").val(),t.body=i.find("textarea").val(),delete t.message;break;case"condition":t.conditions=at.automations.getConditions(i.find(".sb-flow-conditions"))}st.openAI.flows.blocks.set(t),le.find("> div").each(function(){e(this).find("> div")[0].scrollTop=He[e(this).index()]}),k.sbHideLightbox()}),e(k).on("change",".sb-repeater-block-actions select",function(){e(this).parent().next().remove(),e(this).parent().parent().append(st.openAI.getCode.action(e(this).val(),""))}),e(k).on("change",".sb-flow-start-select",function(){k.find(".sb-title-flow-start, .sb-flow-start-messages").setClass("sb-hide","message"!=e(this).val())}),e(le).on("mouseleave",".sb-flow-connectors > div, .sb-flow-block",function(){le.find(".sb-flow-block-cnt").sbActive(!1),Oe=!1,e(this).parent().hasClass("sb-flow-connectors")?st.openAI.flows.blocks.activateLinkedCnts(e(this).closest(".sb-flow-block")):le.find(".sb-flow-connectors > div").sbActive(!1)}),e(le).on("mouseenter",".sb-flow-connectors > div",function(){let t=e(this).closest(".sb-flow-block").parent(),i=st.openAI.flows.blocks.getNextCntIndexes(st.openAI.flows.getActiveIndex(),t.parent().parent().index(),t.index());Oe=!0,le.find("> div").eq(t.parent().parent().index()+1).find(".sb-flow-block-cnt").sbActive(!1).eq(i[e(this).index()]).sbActive(!0)}),e(le).on("mouseenter",".sb-flow-block",function(){st.openAI.flows.blocks.activateLinkedCnts(this)}),e(le).on("click",".sb-flow-add-block",function(){le.find(".sb-flow-block,.sb-flow-add-block").sbActive(!1),e(this).sbActive(!0);let t=st.openAI.flows.steps.get()[st.openAI.flows.blocks.getActiveCntIndex()].map(e=>e.type),i=!t.some(e=>["message","button_list","video","get_user_details","condition"].includes(e)),s=[["set_data","Set data"],["action","Action"],["condition","Condition"],["rest_api","REST API"]],a="";for(var n=0;n<s.length;n++)t.includes(s[n][0])||(a+=`<li data-value="${s[n][0]}">${o(s[n][1])}</li>`);dt.genericPanel("flows-blocks-nav","",`<ul class="sb-menu">${i?`<li>${o("Messages")} <ul><li data-value="message">${o("Send message")}</li><li data-value="button_list">${o("Send button list")}</li><li data-value="video">${o("Send video")}</li></ul></li>`:""}<li>${o("More")} <ul>${i?`<li data-value="get_user_details">${o("Get user details")}</li>`:""}${a}</ul></li></ul>`)}),e(k).on("click","#sb-block-delete",function(){st.openAI.flows.blocks.delete(),k.sbHideLightbox()}),e(re).on("click","li",function(){st.openAI.flows.show(e(this).attr("data-value"))}),e(re).on("click","li i",function(t){return i("The flow will be deleted.","alert",()=>{st.openAI.flows.delete(e(this).parent().attr("data-value"))}),t.preventDefault(),!1}),e(k).on("click",".sb-flows-blocks-nav-box [data-value]",function(){st.openAI.flows.blocks.add(e(this).data("value")),k.sbHideLightbox()}),e(k).on("mouseenter",".sb-flow-scroll",function(){let t=e(this).hasClass("sb-icon-arrow-left");ce=setInterval(()=>{le[0].scrollLeft+=10*(t?-1:1)},10)}),e(k).on("mouseleave",".sb-flow-scroll",function(){clearInterval(ce)}),e(te).on("click","#sb-train-chatbot",function(t){let s="The chatbot has been successfully trained.",n=te.find('.sb-nav [data-value="conversations"]');if(i("<br><br><br><br><br>","info",!1,"sb-embeddings-box"),t.preventDefault(),SB_ADMIN_SETTINGS.cloud&&SBCloud.creditsAlert(this,t))return!1;if(a(this))return!1;if("flows"==te.find(".sb-menu-chatbot .sb-active").attr("data-type"))return st.openAI.flows.save(e=>{i(s,"info",!1,!1,"Success")}),void e(this).sbLoading(!1);if(n.sbActive()){let t=[];for(var r=0;r<Ve.length;r++){let i=te.find(`#sb-chatbot-conversations [data-index="${r}"]`);i.length?(i=[i.find("input").val(),i.find("textarea").val()])[0]==e("<textarea />").html(Ve[r].question).text()&&i[1]==e("<textarea />").html(Ve[r].answer).text()||(Ve[r].question=i[0],Ve[r].answer=i[1],t.push(Ve[r])):(Ve[r].question=!1,Ve[r].answer=!1,t.push(Ve[r]))}return void(t.length?SBF.ajax({function:"open-ai-save-conversation-embeddings",qea:t},t=>{!0===t[0]?i(s,"info",!1,!1,"Success"):st.openAI.train.isError(t[0])||i(t[0]),n.click(),e(this).sbLoading(!1)}):(e(this).sbLoading(!1),i(s,"info",!1,!1,"Success")))}st.openAI.train.errors=[];return st.openAI.train.files(t=>{st.openAI.train.urls=te.find('[data-id="open-ai-sources-url"]').map(function(){return e(this).val().trim()}).get(),st.openAI.train.extract_url=te.find('[data-id="open-ai-sources-extract-url"]').map(function(){return e(this).is(":checked")}).get(),st.openAI.train.website(t=>{st.openAI.train.qea(t=>{st.openAI.train.articles(t=>{st.openAI.init(),te.find("#sb-repeater-chatbot-website .repeater-item i").click(),st.openAI.train.errors.length?(i(o("The chatbot has been trained with errors. Check the console for more details.")+"\n\n<pre>"+st.openAI.train.errors.join("<br>")+"</pre>","info",!1,"sb-errors-list-box",!1,!0),console.error(st.openAI.train.errors)):st.openAI.train.isError(t)||i(s,"info",!1,!1,"Success"),e(this).sbLoading(!1)})})})}),!1}),e(te).on("click","#sb-table-chatbot-files td i, #sb-table-chatbot-website td i, #sb-chatbot-delete-files, #sb-chatbot-delete-website, #sb-chatbot-delete-all-training, #sb-chatbot-delete-all-training-conversations",function(){let s=e(this).is("i"),n=!!s&&e(this).closest("tr");return s&&n.hasClass("sb-pending")?(st.openAI.train.skip_files.push(n.attr("data-name")),void n.remove()):(i("The training data will be permanently deleted.","alert",()=>{let o=[],r=e(this).attr("id");if(s)o=[n.attr("data-url")];else if("sb-chatbot-delete-all-training"==r)o="all";else if("sb-chatbot-delete-all-training-conversations"==r)o="all-conversations";else{let t=e("sb-chatbot-delete-files"==r?ie:se);t.find("input:checked").length||t.find("input").prop("checked",!0),t.find("tr").each(function(){if(e(this).find("input:checked").length)if(e(this).hasClass("sb-pending"))st.openAI.train.skip_files.push(e(this).attr("data-name")),e(this).remove();else{let t=e(this).attr("data-url");o.push(t),st.openAI.train.sitemap_processed_urls.indexOf(t)>-1&&(st.openAI.train.sitemap_processed_urls[st.openAI.train.sitemap_processed_urls.indexOf(t)]=!1)}})}if(o.length){if(a(this))return;SBF.ajax({function:"open-ai-embeddings-delete",sources_to_delete:o},s=>{st.openAI.init(),"all"==o?te.find('.sb-nav [data-value="info"]').click():"all-conversations"==o&&te.find('.sb-nav [data-value="conversations"]').click(),!0===s?t("Training data deleted."):i(s),e(this).sbLoading(!1)})}}),!1)}),e(te).on("click",'.sb-nav [data-value="conversations"]',function(){let e=te.find("#sb-chatbot-conversations");a(e)||SBF.ajax({function:"open-ai-get-conversation-embeddings"},t=>{if(t.length){let s="";Ve=t;for(var i=0;i<t.length;i++)s+=`<div class="repeater-item" data-value="${t[i].id}" data-index=${i}><div><label>${o("Question")}</label><input data-id="q" type="text" value="${t[i].question}" /></div><div class="sb-qea-repeater-answer"><label>${o("Answer")}</label><textarea data-id="a">${t[i].answer}</textarea></div><i class="sb-icon-close"></i></div>`;e.find(".sb-repeater").html(s)}else e.html(`<p class="sb-no-results">${o("No conversations found.")}</p>`);e.sbLoading(!1)})}),e(te).on("click",'.sb-nav [data-value="info"]',function(){let e=te.find("#sb-chatbot-info");a(e)||SBF.ajax({function:"open-ai-get-information"},t=>{let i=[["files","Files"],["website","Website URLs"],["qea","Q&A"],["flows","Flows"],["articles","Articles"],["conversations","Conversations"]],s=`<h2>${o("Sources")}</h2><p>`;for(var a=0;a<i.length;a++)s+=t[i[a][0]]?`${t[i[a][0]][1]} ${o(i[a][1])} (${t[i[a][0]][0]} ${o("chars")})<br>`:"";s+=`</p><h2>${o("Total detected characters")}</h2><p>${t.total} ${o("chars")+(t.limit?" / "+t.limit+" "+o("limit"):"")}</p><hr><div id="sb-chatbot-delete-all-training" class="sb-btn sb-btn-white">${o("Delete all training data")}</div>`,e.html(s),e.sbLoading(!1)})}),e(te).on("click",".sb-menu-chatbot [data-type]",function(t){let i=e(this).data("type");switch(i){case"flows":case"training":case"playground":let e=te.find(`> [data-id="${i}"]`);te.find("> [data-id]").sbActive(!1),e.sbActive(!0),"flows"==i&&e.sbLoading()&&SBF.ajax({function:"open-ai-flows-get"},t=>{for(s=0;s<t.length;s++)t[s]&&t[s].steps&&t[s].name&&st.openAI.flows.flows.push(t[s]);let i="";for(var s=0;s<t.length;s++)t[s]&&(i+=st.openAI.flows.navCode(t[s].name));re.html(i),re.find("li:first-child").click(),e.sbLoading(!1)});break;case"settings":return at.open("dialogflow",!0),t.preventDefault,!1}}),e(ae).on("click",".sb-enlarger-function-calling",function(){e(this).parent().parent().find(".sb-qea-repeater-answer").addClass("sb-hide")}),e(ae).on("change",'[data-id="open-ai-faq-set-data"] select',function(){e(this).parent().next().find("input").setClass("sb-hide",["transcript","transcript_email","human_takeover","archive_conversation"].includes(e(this).val()))}),e(ae).on("input click",'[data-id="open-ai-faq-answer"]',function(){e(this).prev().find("i").sbActive(e(this).val().length>2&&e(this).val().indexOf(" "))}),e(ae).on("click",".sb-qea-repeater-answer > label > i",function(){let t=e(this).closest(".repeater-item").find('[data-id="open-ai-faq-answer"]');a(this)||st.openAI.rewrite(t.val(),i=>{e(this).sbLoading(!1),i[0]&&t.val(i[1])})}),e(ne).on("click",'[data-value="add"], [data-value="send"]',function(){let t=ne.find("textarea"),s=t.val().trim();if(t.val(""),s&&st.openAI.playground.addMessage(s,ne.find('[data-value="user"], [data-value="assistant"]').attr("data-value")),"send"==e(this).data("value")){let t=st.openAI.playground.messages.length;t&&!a(this)&&SBF.ajax({function:"open-ai-playground-message",messages:st.openAI.playground.messages},s=>{if(s[0]){if(s[1]&&(st.openAI.playground.addMessage(s[1],"assistant",s[6]),s[4])){let e="";for(var a in s[4].usage)["string","number"].includes(typeof s[4].usage[a])&&(e+=`<b>${SBF.slugToString(a)}</b>: ${s[4].usage[a]}<br>`);st.openAI.playground.last_response=s[4],te.find(".sb-playground-info").html(e+`<div id="sb-playground-query" class="sb-btn-text">${o("View code")}</div>${s[4].embeddings?`<div id="sb-playground-embeddings" class="sb-btn-text">${o("Embeddings")}</div>`:""}`),s[4].payload&&st.openAI.playground.messages[t-1].push(s[4].payload)}s[8]&&C.find(`[data-conversation-id="${s[8]}"]`).remove()}else i(s),console.error(s);e(this).sbLoading(!1)})}}),e(te).on("click","#sb-playground-query",function(){i("<pre>"+JSON.stringify(st.openAI.playground.last_response.query,null,4).replaceAll('\\"','"')+"</pre>","info",!1,"sb-playground-query-panel",!1,!0)}),e(te).on("click","#sb-playground-embeddings",function(){let e="",t=st.openAI.playground.last_response.embeddings;for(var s=t.length-1;s>-1;s--)e+=`<span><b>${o("Source")}</b>: ${t[s].source?t[s].source.autoLink({target:"_blank"}):""}<br><b>${o("Score")}</b>: ${t[s].score}<br><span>${t[s].text}</span></span>`;i(e,"info",!1,"sb-playground-embeddings-panel",!1,!0)}),e(ne).on("click",'[data-value="clear"]',function(){st.openAI.playground.messages=[],oe.html(""),te.find(".sb-playground-info").html("")}),e(oe).on("click",".sb-icon-close",function(){let t=e(this).closest("[data-type]");st.openAI.playground.messages.splice(t.index(),1),t.remove()}),e(oe).on("click",".sb-rich-chips .sb-btn",function(){ne.find("textarea").val(e(this).html()),ne.find('[data-value="send"]').click()}),e(ne).on("click",'[data-value="user"], [data-value="assistant"]',function(){let t="user"==e(this).attr("data-value");e(this).attr("data-value",t?"assistant":"user").html('<i class="sb-icon-reload"></i> '+o(t?"Assistant":"User"))}),e(k).on("click","#open-ai-troubleshoot a, #google-troubleshoot a",function(s){let n=e(this).parent().attr("id");return s.preventDefault(),!("google-troubleshoot"!=n&&![!0,"mode"].includes(st.openAI.troubleshoot()))&&(a(this)?void 0:(SBF.ajax({function:e(this).parent().attr("id")},s=>{!0===s?t("Success. No issues found."):i(s),e(this).sbLoading(!1),e(A).find(".sb-admin-list .sb-select li.sb-active").click()}),!1))}),e(X).on("click",".ce-settings__button--delete.ce-settings__button--confirm",function(){let e=X.find(".image-tool--filled img").attr("src");e&&SBF.ajax({function:"delete-file",path:e})}),e(X).on("click",".ul-articles li",function(t){i("The changes will be lost.","alert",()=>{nt.show(e(this).attr("data-id")),X.find(".sb-scroll-area:not(.sb-nav)").scrollTop(0)},!1,!1,!1,!Pe,()=>{e(this).parent().find("li").sbActive(!1),e(this).parent().find(`[data-id="${nt.activeID()}"]`).sbActive(!0)})}),e(X).on("click",".ul-categories li",function(t){nt.categories.show(e(this).attr("data-id"))}),e(X).on("click",".sb-add-article",function(){nt.add()}),e(X).on("click",".sb-add-category",function(){nt.categories.add()}),e(X).on("click",".sb-nav i",function(t){let s=e(this).parent(),n=s.closest("ul"),o=n.hasClass("ul-categories");return i(`The ${o?"category":"article"} will be deleted permanently.`,"alert",()=>{let t=s.attr("data-id");if(o)nt.categories.delete(t);else{if(X.find("#editorjs .image-tool__image-picture").each(function(){SBF.ajax({function:"delete-file",path:e(this).attr("src")})}),!t)return s.remove();a(Q),nt.delete(t,e=>{Q.sbLoading(!1),p(),n.find("li").length>1&&setTimeout(()=>{s.prev().length?s.prev().click():s.next().click(),s.remove()},300)})}}),t.preventDefault(),!1}),e(X).on("click",".sb-menu-wide li",function(){let t=e(this).data("type");"settings"==t?at.open("articles",!0):"reports"==t?ot.open("articles-searches"):(X.attr("data-type",t),nt.categories.update())}),e(X).on("click",".sb-save-articles",function(){a(this)||("categories"==X.attr("data-type")?nt.categories.save(t=>{e(this).sbLoading(!1)}):nt.save(t=>{e(this).sbLoading(!1)}))}),e(X).on("change input","input, textarea, select",function(){Pe=!0}),e(K).on("change",function(){ee.val()||(t("Select a parent category first.","error"),e(this).val(""))}),e(Te).on("click",".sb-nav [id]",function(){let t=e(this).attr("id");ot.active_report=!1,Te.find("#sb-date-picker").val(""),Te.attr("class","sb-area-reports sb-active sb-report-"+t),ot.initReport(e(this).attr("id")),SBF.getURL("report")!=t&&f("?report="+t)}),e(Te).on("change","#sb-date-picker",function(){ot.initReport(!1,e(this).val())}),e(Te).on("click",".sb-report-export",function(){e(this).sbLoading()||ot.export(t=>{e(this).sbLoading(!1),t&&_(t,"sb-export-report-close","Report exported")})}),SBF.getURL("report")&&(Te.sbActive()||x.find(".sb-admin-nav #sb-reports").click(),setTimeout(()=>{Te.find("#"+SBF.getURL("report")).click()},500)),e(A).on("click",".sb-panel-woocommerce > i",function(){st.woocommerce.conversationPanel()}),e(A).on("click",".sb-woocommerce-orders > div > span",function(t){let i=e(this).parent();e(t.target).is("span")&&(i.sbActive()||st.woocommerce.conversationPanelOrder(i.attr("data-id")))}),e(A).on("click",".sb-btn-woocommerce",function(){(Le.sbLoading()||0!=s()&&s().language!=st.itemsPanel.panel_language)&&st.itemsPanel.populate("woocommerce"),Ee.find(".sb-search-btn").sbActive(!0).find("input").get(0).focus(),Ee.sbTogglePopup(this)}),e(Ee).find(".sb-woocommerce-products-list").on("scroll",function(){d(this,!0)&&st.itemsPanel.pagination(this,"woocommerce")}),e(Ee).on("click",".sb-select li",function(){st.itemsPanel.filter(this,"woocommerce")}),e(Ee).on("input",".sb-search-btn input",function(){st.itemsPanel.search(this,"woocommerce")}),e(Ee).on("click",".sb-search-btn i",function(){SBF.searchClear(this,()=>{st.itemsPanel.search(e(this).next(),"woocommerce")})}),e(Ee).on("click",".sb-woocommerce-products-list li",function(){let t=Ee.attr("data-action"),i=e(this).data("id");SBF.null(t)?SBChat.insertText(`{product_card id="${i}"}`):(Le.sbLoading(!0),A.find(".sb-add-cart-btn").sbLoading(!0),SBChat.sendMessage(-1,"",[],e=>{e&&(st.woocommerce.conversationPanelUpdate(i),k.sbHideLightbox())},{event:"woocommerce-update-cart",action:"cart-add",id:i})),SBF.deactivateAll(),k.removeClass("sb-popup-active")}),e(A).on("click",".sb-panel-woocommerce .sb-add-cart-btn",function(){e(this).sbLoading()||(SBChat.user_online?(st.itemsPanel.populate("woocommerce"),Ee.sbShowLightbox(!0,"cart-add")):i("The user is offline. Only the carts of online users can be updated."))}),e(A).on("click",".sb-panel-woocommerce .sb-list-items > a > i",function(t){let i=e(this).parent().attr("data-id");return SBChat.sendMessage(-1,"",[],()=>{st.woocommerce.conversationPanelUpdate(i,"removed")},{event:"woocommerce-update-cart",action:"cart-remove",id:i}),e(this).sbLoading(!0),t.preventDefault(),!1}),e(A).on("click",".sb-panel-ump > i",function(){st.ump.conversationPanel()}),e(A).on("click",".sb-panel-armember > i",function(){st.armember.conversationPanel()}),e(A).on("click",".sb-panel-opencart > i",function(){st.opencart.conversationPanel()}),e(A).on("click",".sb-opencart-orders > a",function(){st.opencart.openOrder(e(this).attr("data-id"))}),e(H).on("click","#opencart-sync a",function(t){t.preventDefault(),a(this)||SBF.ajax({function:"opencart-sync"},t=>{e(this).sbLoading(!1),i(!0===t?"Users successfully imported.":t)})}),e(H).on("click","#perfex-sync a, #whmcs-sync a, #perfex-articles-sync a, #whmcs-articles-sync a, #aecommerce-sync a, #aecommerce-sync-admins a, #aecommerce-sync-sellers a, #martfury-sync a, #martfury-sync-sellers a",function(t){if(a(this))return;let s=e(this).closest("[id]").attr("id"),n=s.indexOf("article")>0;SBF.ajax({function:s},t=>{!0===t?(n||rt.update(),i(n?"Articles successfully imported.":"Users successfully imported.")):i("Error. Response: "+JSON.stringify(t)),e(this).sbLoading(!1)}),t.preventDefault()}),e(A).on("click","#sb-zendesk-btn",function(t){a(this)||(SBF.ajax({function:"zendesk-create-ticket",conversation_id:SBChat.conversation.id},t=>{!0===t?st.zendesk.conversationPanel():i("Error. Response: "+JSON.stringify(t)),e(this).sbLoading(!1)}),t.preventDefault())}),e(A).on("click","#sb-zendesk-update-ticket",function(t){if(!a(this))return SBF.ajax({function:"zendesk-update-ticket",conversation_id:SBChat.conversation.id,zendesk_ticket_id:e(this).closest("[data-id]").attr("data-id")},()=>{e(this).sbLoading(!1)}),t.preventDefault(),!1}),e(k).on("click",".sb-enlarger",function(){e(this).sbActive(!0)}),e(k).on("mouseenter","[data-sb-tooltip-init]",function(){e(this).parent().sbInitTooltips(),e(this).removeAttr("data-sb-tooltip-init"),e(this).trigger("mouseenter")}),e(k).on("click",".sb-language-switcher > i",function(){let t=e(this).parent(),i=t.find("[data-language]").map(function(){return e(this).attr("data-language")}).get(),s="";i.push("en");for(var a in SB_LANGUAGE_CODES)i.includes(a)||(s+=`<div data-language="${a}"><img src="${SB_URL}/media/flags/${a}.png" />${o(SB_LANGUAGE_CODES[a])}</div>`);fe=t,dt.genericPanel("languages","Choose a language",s,[],' data-source="'+t.attr("data-source")+'"',!0)}),e(k).on("click",".sb-language-switcher img",function(){let t=e(this).parent(),s=t.sbActive(),a=!s&&t.attr("data-language");switch(t.parent().attr("data-source")){case"article-categories":nt.categories.show(nt.categories.activeID(),a);break;case"articles":let e=Q.find(".sb-language-switcher .sb-active");i("The changes will be lost.","alert",()=>{let e=t.attr("data-id");e||s?nt.show(e&&!s?e:nt.activeID(!0)):nt.clear()},!1,!1,!1,!Pe,()=>{t.sbActive(!1),e.sbActive(!0)});break;case"automations":at.automations.show(!1,a);break;case"settings":let n=t.parent().find("[data-language].sb-active");at.translations.save(t,s?t.attr("data-language"):!!n.length&&n.attr("data-language")),at.translations.activate(t,a)}t.siblings().sbActive(!1),t.sbActive(!s)}),e(k).on("click",".sb-language-switcher span > i",function(){let t=e(this).parent(),s=t.attr("data-language");i(o("The {T} translation will be deleted.").replace("{T}",o(SB_LANGUAGE_CODES[s])),"alert",()=>{switch(t.parent().attr("data-source")){case"article-categories":nt.categories.translations.delete(s);break;case"articles":nt.translations.delete(s);break;case"automations":at.automations.deleteTranslation(!1,s),at.automations.show();break;case"settings":at.translations.delete(t,s)}t.remove()})}),e(k).on("click",".sb-languages-box [data-language]",function(){let t=e(this).parents().eq(1),s=e(this).attr("data-language"),a=!0;switch(t.attr("data-source")){case"article-categories":nt.categories.translations.add(s);break;case"articles":i("The changes will be lost.","alert",()=>{nt.translations.add(s),k.sbHideLightbox()},!1,!1,!1,!Pe),a=!1;break;case"automations":at.automations.addTranslation(!1,!1,s),at.automations.show(!1,s);break;case"settings":at.translations.add(s)}a&&k.sbHideLightbox()}),e(k).on("click",".sb-lightbox .sb-top-bar .sb-close",function(){k.sbHideLightbox()}),e(k).on("click",".sb-lightbox .sb-info",function(){e(this).sbActive(!1)}),e(k).on("click",".sb-dialog-box a",function(){let t=e(this).closest(".sb-lightbox");e(this).hasClass("sb-confirm")&&be(),e(this).hasClass("sb-cancel")&&ve&&ve(),1==k.find(".sb-lightbox.sb-active").length&&we.sbActive(!1),dt.open_popup=!1,t.sbActive(!1)}),e(k).on("click",".sb-menu-wide li, .sb-nav li",function(){e(this).siblings().sbActive(!1),e(this).sbActive(!0)}),e(k).on("click",".sb-nav:not(.sb-nav-only) li:not(.sb-tab-nav-title)",function(){let t=e(this).closest(".sb-tab"),i=e(t).find(" > .sb-content > div").sbActive(!1).eq(e(this).parent().find("li:not(.sb-tab-nav-title)").index(this));i.sbActive(!0),i.find("textarea").each(function(){e(this).autoExpandTextarea(),e(this).manualExpandTextarea()}),t.find(".sb-scroll-area:not(.sb-nav)").scrollTop(0)}),e(k).sbInitTooltips(),e(k).on("click",'[data-button="toggle"]',function(){let t=k.find("."+e(this).data("show")),i=k.find("."+e(this).data("hide"));t.addClass("sb-show-animation").show(),i.hide(),dt.open_popup=!(!t.hasClass("sb-lightbox")&&!t.hasClass("sb-popup"))&&t}),e(k).on("click",".sb-info-card",function(){e(this).sbActive(!1)}),e(de).on("change",function(){pe?(pe(),pe=!1):(e(ue).sbLoading(e(this).prop("files").length),e(this).sbUploadFiles(t=>{if(e(ue).sbLoading(!1),"success"==(t=JSON.parse(t))[0]){"upload-image"==e(ue).closest("[data-type]").data("type")&&(e(ue).attr("data-value")&&SBF.ajax({function:"delete-file",path:e(ue).attr("data-value")}),e(ue).attr("data-value",t[1]).css("background-image",`url("${t[1]}")`)),he&&he(t[1])}else console.log(t[1])}))}),e(k).on("click",".sb-accordion > div > span",function(t){let i=e(this).parent(),s=e(i).sbActive();e(t.target).is("span")&&(i.siblings().sbActive(!1),i.sbActive(!s))}),e(k).on("mousedown",function(t){if(e(dt.open_popup).length){let i=e(dt.open_popup);i.is(t.target)||0!==i.has(t.target).length||["sb-btn-saved-replies","sb-btn-emoji","sb-btn-woocommerce","sb-btn-shopify","sb-btn-open-ai"].includes(e(t.target).attr("class"))||(i.hasClass("sb-popup")?i.sbTogglePopup():i.hasClass("sb-select")?i.find("ul").sbActive(!1):i.hasClass("sb-menu-mobile")?i.find("i").sbActive(!1):i.hasClass("sb-menu")?i.sbActive(!1):dt.open_popup&&["sb-embeddings-box"].includes(dt.open_popup.attr("id"))||k.sbHideLightbox(),dt.open_popup=!1)}})})}(jQuery),function(e,t){"object"==typeof exports?module.exports=t(e):"function"==typeof define&&define.amd?define("colors",[],function(){return t(e)}):e.Colors=t(e)}(this,function(e,t){function i(e,i,a,n,o){if("string"==typeof i){a=(i=m.txt2color(i)).type,p[a]=i[a],o=o!==t?o:i.alpha}else if(i)for(var c in i)e[a][c]=r(i[c]/l[a][c][1],0,1);return o!==t&&(e.alpha=r(+o,0,1)),s(a,n?e:t)}function s(e,t){var i,s,r,h=t||p,f=m,g=u.options,b=l,v=h.RND,_="",S="",w={hsl:"hsv",rgb:e},y=v.rgb;if("alpha"!==e){for(var k in b)if(!b[k][k]){e!==k&&(S=w[k]||"rgb",h[k]=f[S+"2"+k](h[S])),v[k]||(v[k]={}),i=h[k];for(_ in i)v[k][_]=d(i[_]*b[k][_][1])}y=v.rgb,h.HEX=f.RGB2HEX(y),h.equivalentGrey=g.grey.r*h.rgb.r+g.grey.g*h.rgb.g+g.grey.b*h.rgb.b,h.webSave=s=a(y,51),h.webSmart=r=a(y,17),h.saveColor=y.r===s.r&&y.g===s.g&&y.b===s.b?"web save":y.r===r.r&&y.g===r.g&&y.b===r.b?"web smart":"",h.hueRGB=m.hue2RGB(h.hsv.h),t&&(h.background=function(e,t,i){var s=u.options.grey,a={};return a.RGB={r:e.r,g:e.g,b:e.b},a.rgb={r:t.r,g:t.g,b:t.b},a.alpha=i,a.equivalentGrey=d(s.r*e.r+s.g*e.g+s.b*e.b),a.rgbaMixBlack=o(t,{r:0,g:0,b:0},i,1),a.rgbaMixWhite=o(t,{r:1,g:1,b:1},i,1),a.rgbaMixBlack.luminance=n(a.rgbaMixBlack,!0),a.rgbaMixWhite.luminance=n(a.rgbaMixWhite,!0),u.options.customBG&&(a.rgbaMixCustom=o(t,u.options.customBG,i,1),a.rgbaMixCustom.luminance=n(a.rgbaMixCustom,!0),u.options.customBG.luminance=n(u.options.customBG,!0)),a}(y,h.rgb,h.alpha))}var x,B,A,T=h.rgb,I=h.alpha,$="luminance",C=h.background;return x=o(T,{r:0,g:0,b:0},I,1),x[$]=n(x,!0),h.rgbaMixBlack=x,B=o(T,{r:1,g:1,b:1},I,1),B[$]=n(B,!0),h.rgbaMixWhite=B,g.customBG&&(A=o(T,C.rgbaMixCustom,I,1),A[$]=n(A,!0),A.WCAG2Ratio=function(e,t){var i=1;return i=e>=t?(e+.05)/(t+.05):(t+.05)/(e+.05),d(100*i)/100}(A[$],C.rgbaMixCustom[$]),h.rgbaMixBGMixCustom=A,A.luminanceDelta=c.abs(A[$]-C.rgbaMixCustom[$]),A.hueDelta=function(e,t,i){return(c.max(e.r-t.r,t.r-e.r)+c.max(e.g-t.g,t.g-e.g)+c.max(e.b-t.b,t.b-e.b))*(i?255:1)/765}(C.rgbaMixCustom,A,!0)),h.RGBLuminance=n(y),h.HUELuminance=n(h.hueRGB),g.convertCallback&&g.convertCallback(h,e),h}function a(e,t){var i={},s=0,a=t/2;for(var n in e)s=e[n]%t,i[n]=e[n]+(s>a?t-s:-s);return i}function n(e,t){for(var i=t?1:255,s=[e.r/i,e.g/i,e.b/i],a=u.options.luminance,n=s.length;n--;)s[n]=s[n]<=.03928?s[n]/12.92:c.pow((s[n]+.055)/1.055,2.4);return a.r*s[0]+a.g*s[1]+a.b*s[2]}function o(e,i,s,a){var n={},o=s!==t?s:1,r=a!==t?a:1,l=o+r*(1-o);for(var c in e)n[c]=(e[c]*o+i[c]*r*(1-o))/l;return n.a=l,n}function r(e,t,i){return e>i?i:t>e?t:e}var l={rgb:{r:[0,255],g:[0,255],b:[0,255]},hsv:{h:[0,360],s:[0,100],v:[0,100]},hsl:{h:[0,360],s:[0,100],l:[0,100]},alpha:{alpha:[0,1]},HEX:{HEX:[0,16777215]}},c=e.Math,d=c.round,u={},p={},h={r:.298954,g:.586434,b:.114612},f={r:.2126,g:.7152,b:.0722},g=function(e){this.colors={RND:{}},this.options={color:"rgba(0,0,0,0)",grey:h,luminance:f,valueRanges:l},b(this,e||{})},b=function(e,s){var a,n=e.options;v(e);for(var o in s)s[o]!==t&&(n[o]=s[o]);a=n.customBG,n.customBG="string"==typeof a?m.txt2color(a).rgb:a,p=i(e.colors,n.color,t,!0)},v=function(e){u!==e&&(u=e,p=e.colors)};g.prototype.setColor=function(e,a,n){return v(this),e?i(this.colors,e,a,t,n):(n!==t&&(this.colors.alpha=r(n,0,1)),s(a))},g.prototype.setCustomBackground=function(e){return v(this),this.options.customBG="string"==typeof e?m.txt2color(e).rgb:e,i(this.colors,t,"rgb")},g.prototype.saveAsBackground=function(){return v(this),i(this.colors,t,"rgb",!0)},g.prototype.toString=function(e,t){return m.color2text((e||"rgb").toLowerCase(),this.colors,t)};var m={txt2color:function(e){var t={},i=e.replace(/(?:#|\)|%)/g,"").split("("),s=(i[1]||"").split(/,\s*/),a=i[1]?i[0].substr(0,3):"rgb",n="";if(t.type=a,t[a]={},i[1])for(var o=3;o--;)n=a[o]||a.charAt(o),t[a][n]=+s[o]/l[a][n][1];else t.rgb=m.HEX2rgb(i[0]);return t.alpha=s[3]?+s[3]:1,t},color2text:function(e,t,i){var s=!1!==i&&d(100*t.alpha)/100,a="number"==typeof s&&!1!==i&&(i||1!==s),n=t.RND.rgb,o=t.RND.hsl,r="hex"===e&&a,l="hex"===e&&!r,c="rgb"===e||r?n.r+", "+n.g+", "+n.b:l?"#"+t.HEX:o.h+", "+o.s+"%, "+o.l+"%";return l?c:(r?"rgb":e)+(a?"a":"")+"("+c+(a?", "+s:"")+")"},RGB2HEX:function(e){return((e.r<16?"0":"")+e.r.toString(16)+(e.g<16?"0":"")+e.g.toString(16)+(e.b<16?"0":"")+e.b.toString(16)).toUpperCase()},HEX2rgb:function(e){return e=e.split(""),{r:+("0x"+e[0]+e[e[3]?1:0])/255,g:+("0x"+e[e[3]?2:1]+(e[3]||e[1]))/255,b:+("0x"+(e[4]||e[2])+(e[5]||e[2]))/255}},hue2RGB:function(e){var t=6*e,i=~~t%6,s=6===t?0:t-i;return{r:d(255*[1,1-s,0,0,s,1][i]),g:d(255*[s,1,1,1-s,0,0][i]),b:d(255*[0,0,s,1,1,1-s][i])}},rgb2hsv:function(e){var t,i,s,a=e.r,n=e.g,o=e.b,r=0;return o>n&&(n=o+(o=n,0),r=-1),i=o,n>a&&(a=n+(n=a,0),r=-2/6-r,i=c.min(n,o)),t=a-i,s=a?t/a:0,{h:1e-15>s?p&&p.hsl&&p.hsl.h||0:t?c.abs(r+(n-o)/(6*t)):0,s:a?t/a:p&&p.hsv&&p.hsv.s||0,v:a}},hsv2rgb:function(e){var t=6*e.h,i=e.s,s=e.v,a=~~t,n=t-a,o=s*(1-i),r=s*(1-n*i),l=s*(1-(1-n)*i),c=a%6;return{r:[s,r,o,o,l,s][c],g:[l,s,s,r,o,o][c],b:[o,o,l,s,s,r][c]}},hsv2hsl:function(e){var t=(2-e.s)*e.v,i=e.s*e.v;return i=e.s?1>t?t?i/t:0:i/(2-t):0,{h:e.h,s:e.v||i?i:p&&p.hsl&&p.hsl.s||0,l:t/2}},rgb2hsl:function(e,t){var i=m.rgb2hsv(e);return m.hsv2hsl(t?i:p.hsv=i)},hsl2rgb:function(e){var t=6*e.h,i=e.s,s=e.l,a=.5>s?s*(1+i):s+i-i*s,n=s+s-a,o=~~t,r=a*(a?(a-n)/a:0)*(t-o),l=n+r,c=a-r,d=o%6;return{r:[a,c,n,n,l,a][d],g:[l,a,a,c,n,n][d],b:[n,n,l,a,a,c][d]}}};return g}),function(e,t){"object"==typeof exports?module.exports=t(e,require("jquery"),require("colors")):"function"==typeof define&&define.amd?define(["jquery","colors"],function(i,s){return t(e,i,s)}):t(e,e.jQuery,e.Colors)}(this,function(e,t,i,s){function a(e){return e.value||e.getAttribute("value")||t(e).css("background-color")||"#FFF"}function n(e){return(e=e.originalEvent&&e.originalEvent.touches?e.originalEvent.touches[0]:e).originalEvent?e.originalEvent:e}function o(e){return t(e.find(f.doRender)[0]||e[0])}function r(i){var s=t(this),n=s.offset(),r=t(e),u=f.gap;i?(g=o(s),g._colorMode=g.data("colorMode"),p.$trigger=s,(b||l()).css(f.positionCallback.call(p,s)||{left:(b._left=n.left)-((b._left+=b._width-(r.scrollLeft()+r.width()))+u>0?b._left+u:0),top:(b._top=n.top+s.outerHeight())-((b._top+=b._height-(r.scrollTop()+r.height()))+u>0?b._top+u:0)}).show(f.animationSpeed,function(){!0!==i&&(w.toggle(!!f.opacity)._width=w.width(),m._width=m.width(),m._height=m.height(),v._height=v.height(),h.setColor(a(g[0])),d(!0))}).off(".tcp").on(A,".cp-xy-slider,.cp-z-slider,.cp-alpha",c)):p.$trigger&&t(b).hide(f.animationSpeed,function(){d(!1),p.$trigger=null}).off(".tcp")}function l(){return t("head")[f.cssPrepend?"prepend":"append"]('<style type="text/css" id="tinyColorPickerStyles">'+(f.css||F)+(f.cssAddon||"")+"</style>"),t(C).css({margin:f.margin}).appendTo("body").show(0,function(){p.$UI=b=t(this),I=f.GPU&&b.css("perspective")!==s,v=t(".cp-z-slider",this),m=t(".cp-xy-slider",this),_=t(".cp-xy-cursor",this),S=t(".cp-z-cursor",this),w=t(".cp-alpha",this),y=t(".cp-alpha-cursor",this),f.buildCallback.call(p,b),b.prepend("<div>").children().eq(0).css("width",b.children().eq(0).width()),b._width=this.offsetWidth,b._height=this.offsetHeight}).hide()}function c(e){var i=this.className.replace(/cp-(.*?)(?:\s*|$)/,"$1").replace("-","_");(e.button||e.which)>1||(e.preventDefault&&e.preventDefault(),e.returnValue=!1,g._offset=t(this).offset(),(i="xy_slider"===i?function(e){var t=n(e),i=t.pageX-g._offset.left,s=t.pageY-g._offset.top;h.setColor({s:i/m._width*100,v:100-s/m._height*100},"hsv")}:"z_slider"===i?function(e){var t=n(e).pageY-g._offset.top;h.setColor({h:360-t/v._height*360},"hsv")}:function(e){var t=(n(e).pageX-g._offset.left)/w._width;h.setColor({},"rgb",t)})(e),d(),k.on(T,function(){k.off(".tcp")}).on(B,function(e){i(e),d()}))}function d(e){var t=h.colors,i=t.hueRGB,a=(t.RND.rgb,t.RND.hsl,f.dark),n=f.light,o=h.toString(g._colorMode,f.forceAlpha),r=t.HUELuminance>.22?a:n,l=t.rgbaMixBlack.luminance>.22?a:n,c=(1-t.hsv.h)*v._height,d=t.hsv.s*m._width,p=(1-t.hsv.v)*m._height,b=t.alpha*w._width,k=I?"translate3d":"",x=g[0].value,B=g[0].hasAttribute("value")&&""===x&&e!==s;m._css={backgroundColor:"rgb("+i.r+","+i.g+","+i.b+")"},_._css={transform:k+"("+d+"px, "+p+"px, 0)",left:I?"":d,top:I?"":p,borderColor:t.RGBLuminance>.22?a:n},S._css={transform:k+"(0, "+c+"px, 0)",top:I?"":c,borderColor:"transparent "+r},w._css={backgroundColor:"#"+t.HEX},y._css={transform:k+"("+b+"px, 0, 0)",left:I?"":b,borderColor:l+" transparent"},g._css={backgroundColor:B?"":o,color:B?"":t.rgbaMixBGMixCustom.luminance>.22?a:n},g.text=B?"":x!==o?o:"",e!==s?u(e):$(u)}function u(e){m.css(m._css),_.css(_._css),S.css(S._css),w.css(w._css),y.css(y._css),f.doRender&&g.css(g._css),g.text&&g.val(g.text),f.renderCallback.call(p,g,"boolean"==typeof e?e:s)}var p,h,f,g,b,v,m,_,S,w,y,k=t(document),x=t(),B="touchmove.tcp mousemove.tcp pointermove.tcp",A="touchstart.tcp mousedown.tcp pointerdown.tcp",T="touchend.tcp mouseup.tcp pointerup.tcp",I=!1,$=e.requestAnimationFrame||e.webkitRequestAnimationFrame||function(e){e()},C='<div class="cp-color-picker"><div class="cp-z-slider"><div class="cp-z-cursor"></div></div><div class="cp-xy-slider"><div class="cp-white"></div><div class="cp-xy-cursor"></div></div><div class="cp-alpha"><div class="cp-alpha-cursor"></div></div></div>',F=".cp-color-picker{position:absolute;overflow:hidden;padding:6px 6px 0;background-color:#444;color:#bbb;font-family:Arial,Helvetica,sans-serif;font-size:12px;font-weight:400;cursor:default;border-radius:5px}.cp-color-picker>div{position:relative;overflow:hidden}.cp-xy-slider{float:left;height:128px;width:128px;margin-bottom:6px;background:linear-gradient(to right,#FFF,rgba(255,255,255,0))}.cp-white{height:100%;width:100%;background:linear-gradient(rgba(0,0,0,0),#000)}.cp-xy-cursor{position:absolute;top:0;width:10px;height:10px;margin:-5px;border:1px solid #fff;border-radius:100%;box-sizing:border-box}.cp-z-slider{float:right;margin-left:6px;height:128px;width:20px;background:linear-gradient(red 0,#f0f 17%,#00f 33%,#0ff 50%,#0f0 67%,#ff0 83%,red 100%)}.cp-z-cursor{position:absolute;margin-top:-4px;width:100%;border:4px solid #fff;border-color:transparent #fff;box-sizing:border-box}.cp-alpha{clear:both;width:100%;height:16px;margin:6px 0;background:linear-gradient(to right,#444,rgba(0,0,0,0))}.cp-alpha-cursor{position:absolute;margin-left:-4px;height:100%;border:4px solid #fff;border-color:#fff transparent;box-sizing:border-box}",N=function(e){h=this.color=new i(e),f=h.options,p=this};N.prototype={render:d,toggle:r},t.fn.colorPicker=function(i){var s=this,n=function(){};return i=t.extend({animationSpeed:150,GPU:!0,doRender:!0,customBG:"#FFF",opacity:!0,renderCallback:n,buildCallback:n,positionCallback:n,body:document.body,scrollResize:!0,gap:4,dark:"#222",light:"#DDD"},i),!p&&i.scrollResize&&t(e).on("resize.tcp scroll.tcp",function(){p.$trigger&&p.toggle.call(p.$trigger[0],!0)}),x=x.add(this),this.colorPicker=p||new N(i),this.options=i,t(i.body).off(".tcp").on(A,function(e){-1===x.add(b).add(t(b).find(e.target)).index(e.target)&&r()}),this.on("focusin.tcp click.tcp",function(e){p.color.options=t.extend(p.color.options,f=s.options),r.call(this,e)}).on("change.tcp",function(){h.setColor(this.value||"#FFF"),s.colorPicker.render(!0)}).each(function(){var e=a(this),s=e.split("("),n=o(t(this));n.data("colorMode",s[1]?s[0].substr(0,3):"HEX").attr("readonly",f.preventFocus),i.doRender&&n.css({"background-color":e,color:function(){return h.setColor(e).rgbaMixBGMixCustom.luminance>.22?i.dark:i.light}})})},t.fn.colorPicker.destroy=function(){t("*").off(".tcp"),p.toggle(!1),x=t()}});