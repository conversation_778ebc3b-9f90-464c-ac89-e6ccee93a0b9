{"{product_name} has no {product_attribute_name} variants.": "{product_name} には {product_attribute_name} のバリエーションがありません。", "360dialog settings": "360dialog設定", "360dialog template": "360dialogテンプレート", "Abandoned cart notification": "放棄されたカート通知", "Abandoned cart notification - Admin email": "放棄されたカート通知 - 管理者メール", "Abandoned cart notification - First email": "放棄されたカート通知 - 最初のメール", "Abandoned cart notification - Second email": "放棄されたカート通知 - 2番目のメール", "Accept button text": "同意ボタンのテキスト", "Account SID": "アカウントSID", "Activate the Slack integration.": "Slack連携を有効にします。", "Activate the Zendesk integration": "Zendesk連携を有効にする", "Activate this option if you don't want to translate the settings area.": "設定エリアを翻訳したくない場合は、このオプションを有効にしてください。", "Active": "有効", "Active - admin": "有効 - 管理者", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce CMSのURL。例: https://shop.com/", "Active eCommerce URL": "Active eCommerce URL", "Active for agents": "エージェントに対して有効", "Active for users": "ユーザーに対して有効", "Active webhooks": "有効なWebhook", "Add a delay (ms) to the bot's responses. Default is 2000.": "ボットの応答に遅延（ミリ秒）を追加します。デフォルトは2000です。", "Add and manage additional support departments.": "追加のサポート部署を追加および管理します。", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "エージェントがチャットエディタで使用できる定型返信を追加および管理します。定型返信は、# の後に返信名とスペースを入力することで挿入できます。\\n を使用して改行します。", "Add and manage tags.": "タグを追加および管理します。", "Add comma separated WordPress user roles. The Support Board administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "WordPressユーザーロールをカンマ区切りで追加します。Support Board管理エリアは、デフォルトのロール（editor, administrator, author）に加えて、新しいロールでも利用可能になります。", "Add custom fields to the new ticket form.": "新しいチケットフォームにカスタムフィールドを追加します。", "Add custom fields to the user profile details.": "ユーザープロフィールの詳細にカスタムフィールドを追加します。", "Add Intents": "インテントを追加", "Add Intents to saved replies": "インテントを定型返信に追加", "Add WhatsApp phone number details here.": "ここにWhatsApp電話番号の詳細を追加します。", "Adjust the chat button position. Values are in px.": "チャットボタンの位置を調整します。値はピクセル（px）単位です。", "Admin icon": "管理者アイコン", "Admin IDs": "管理者ID", "Admin login logo": "管理者ログインロゴ", "Admin login message": "管理者ログインメッセージ", "Admin notifications": "管理者通知", "Admin title": "管理者タイトル", "Agent area": "エージェントエリア", "Agent details": "エージェント詳細", "Agent email notifications": "エージェントのメール通知", "Agent ID": "エージェントID", "Agent linking": "エージェント連携", "Agent message template": "エージェントメッセージテンプレート", "Agent notification email": "エージェント通知メール", "Agent privileges": "エージェント権限", "Agents": "エージェント", "Agents and admins tab": "エージェントと管理者タブ", "Agents menu": "エージェントメニュー", "Agents only": "エージェントのみ", "All": "すべて", "All channels": "すべてのチャネル", "All messages": "すべてのメッセージ", "All questions": "すべての質問", "Allow only extended licenses": "拡張ライセンスのみ許可", "Allow only one conversation": "会話は1つのみ許可", "Allow only one conversation per user.": "ユーザーごとに1つの会話のみ許可します。", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "回答が既知でメールパイピングが有効な場合、チャットボットがユーザーのメールに返信できるようにします。", "Allow the chatbot to reply to the user's text messages if the answer is known.": "回答が既知の場合、チャットボットがユーザーのテキストメッセージに返信できるようにします。", "Allow the user to archive a conversation and hide archived conversations.": "ユーザーが会話をアーカイブし、アーカイブされた会話を非表示にできるようにします。", "Allow users to contact you via their favorite messaging apps.": "ユーザーがお気に入りのメッセージングアプリ経由で連絡できるようにします。", "Allow users to select a product on ticket creation.": "チケット作成時にユーザーが製品を選択できるようにします。", "Always all messages": "常にすべてのメッセージ", "Always incoming messages only": "常に受信メッセージのみ", "Always sort conversations by date in the admin area.": "管理エリアで常に会話を日付順に並べ替えます。", "API key": "APIキー", "Append the registration user details to the success message.": "登録ユーザーの詳細を成功メッセージに追加します。", "Apply a custom background image for the header area.": "ヘッダーエリアにカスタム背景画像を適用します。", "Apply changes": "変更を適用", "Apply to": "に適用", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "Slackアプリ内のすべてのユーザーチャネルをアーカイブします。この操作には時間がかかる場合があります。重要：すべてのSlackチャネルがアーカイブされます。", "Archive automatically the conversations marked as read every 24h.": "既読としてマークされた会話を24時間ごとに自動的にアーカイブします。", "Archive channels": "チャネルをアーカイブ", "Archive channels now": "今すぐチャネルをアーカイブ", "Articles": "記事", "Articles area": "記事エリア", "Articles button link": "記事ボタンリンク", "Articles page URL": "記事ページURL", "Artificial Intelligence": "人工知能", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "Google Business Messagesから開始されたすべての会話に部署を割り当てます。部署IDを入力してください。", "Assign a department to all conversations started from Twitter. Enter the department ID.": "Twitterから開始されたすべての会話に部署を割り当てます。部署IDを入力してください。", "Assign a department to all conversations started from Viber. Enter the department ID.": "Viberから開始されたすべての会話に部署を割り当てます。部署IDを入力してください。", "Assign a department to all conversations started from WeChat. Enter the department ID.": "WeChatから開始されたすべての会話に部署を割り当てます。部署IDを入力してください。", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "異なるGoogle Business Messagesの場所から開始された会話に異なる部署を割り当てます。この設定はデフォルトの部署を上書きします。", "Assistant": "アシスタント", "Assistant ID": "アシスタントID", "Attachments list": "添付ファイルリスト", "Audio file URL - admin": "音声ファイルURL - 管理者", "Automatic": "自動", "Automatic human takeover": "自動的な人間による介入", "Automatic translation": "自動翻訳", "Automatic updates": "自動更新", "Automatically archive conversations": "会話を自動的にアーカイブ", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "ユーザーのアクティブなプランに基づいて部署を自動的に割り当てます。プランがないユーザーにはプランIDとして -1 を挿入します。", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "新しい更新を自動的にチェックしてインストールします。有効なEnvato購入コードと有効なアプリのライセンスキーが必要です。", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "管理エリアの会話詳細パネルやその他のパネルを自動的に折りたたみます。", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "各ウェブサイト用に部署を自動的に作成し、各ウェブサイトの会話を適切な部署にルーティングします。この設定にはWordPressマルチサイトインストールが必要です。", "Automatically hide the conversation details panel.": "会話詳細パネルを自動的に非表示にします。", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "カートに商品を入れたままの顧客にカートリマインダーを自動的に送信します。次のマージフィールドなどを使用できます: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}。", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "Zendeskの顧客を {R} と自動的に同期し、Zendeskチケットを表示したり、{R} を離れることなく新しいチケットを作成したりします。", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "製品、カテゴリ、タグなどをDialogflowと自動的に同期し、ボットがショップ関連の質問に自律的に回答できるようにします。", "Automatically translate admin area": "管理エリアを自動翻訳", "Automatically translate the admin area to match the agent profile language or browser language.": "エージェントのプロフィール言語またはブラウザ言語に合わせて管理エリアを自動的に翻訳します。", "Avatar image": "アバター画像", "Away mode": "離席モード", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "チャットを開始する前に、ユーザーはアクセスするためにプライバシーメッセージに同意する必要があります。", "Birthday": "誕生日", "Body variables": "本文変数", "Bot name": "ボット名", "Bot profile image": "ボットプロフィール画像", "Bot response delay": "ボット応答遅延", "Bottom": "下", "Brand": "ブランド", "Built-in chat button icons": "組み込みチャットボタンアイコン", "Business Account ID": "ビジネスアカウントID", "Button action": "ボタンアクション", "Button name": "ボタン名", "Button text": "ボタンテキスト", "Button variables": "ボタン変数", "Cancel button text": "キャンセルボタンのテキスト", "Cart": "カート", "Cart follow up message": "カートフォローアップメッセージ", "Catalogue details": "カタログ詳細", "Catalogue ID": "カタログID", "Change the chat button image with a custom one.": "チャットボタン画像をカスタム画像に変更します。", "Change the default field names.": "デフォルトのフィールド名を変更します。", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "チャットウィジェットのヘッダーエリアのメッセージテキストを変更します。このテキストは、最初の返信が送信されるとエージェントの見出しに置き換えられます。", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "チャットウィジェットのヘッダーエリアのタイトルテキストを変更します。このテキストは、最初の返信が送信されるとエージェントの名前に置き換えられます。", "Channel ID": "チャネルID", "Channels": "チャネル", "Channels filter": "チャネルフィルター", "Chat": "チャット", "Chat and admin": "チャットと管理", "Chat background": "チャット背景", "Chat button icon": "チャットボタンアイコン", "Chat button offset": "チャットボタンオフセット", "Chat message": "チャットメッセージ", "Chat only": "チャットのみ", "Chat position": "チャット位置", "Chatbot": "チャットボット", "Chatbot mode": "チャットボットモード", "Check Requirements": "要件を確認", "Check the server configurations and make sure it has all the requirements.": "サーバー構成を確認し、すべての要件を満たしていることを確認します。", "Checkout": "チェックアウト", "Choose a background texture for the chat header and conversation area.": "チャットヘッダーと会話エリアの背景テクスチャを選択します。", "Choose where to display the chat. Enter the values separated by commas.": "チャットを表示する場所を選択します。値をカンマ区切りで入力します。", "Choose which fields to disable from the tickets area.": "チケットエリアから無効にするフィールドを選択します。", "Choose which fields to include in the new ticket form.": "新しいチケットフォームに含めるフィールドを選択します。", "Choose which fields to include in the registration form. The name field is included by default.": "登録フォームに含めるフィールドを選択します。名前フィールドはデフォルトで含まれます。", "Choose which user system the front-end chat will use to register and log in users.": "フロントエンドチャットがユーザーの登録とログインに使用するユーザーシステムを選択します。", "City": "市区町村", "Clear flows": "フローをクリア", "Click the button to start the Dialogflow synchronization.": "ボタンをクリックしてDialogflow同期を開始します。", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "ボタンをクリックしてSlack同期を開始します。Localhostはメッセージを受信できません。テストを実行するには、別のアカウントまたは訪問者としてログインしてください。", "Client email": "クライアントメール", "Client ID": "クライアントID", "Client token": "クライアントトークン", "Close chat": "チャットを閉じる", "Close message": "クローズメッセージ", "Cloud API numbers": "Cloud API番号", "Cloud API settings": "Cloud API設定", "Cloud API template fallback": "Cloud APIテンプレートフォールバック", "Code": "コード", "Collapse panels": "パネルを折りたたむ", "Color": "色", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "Slackから直接ユーザーとコミュニケーションします。メッセージや添付ファイルの送受信、絵文字の使用などが可能です。", "Company": "会社", "Concurrent chats": "同時チャット数", "Configuration URL": "構成URL", "Confirm button text": "確認ボタンのテキスト", "Confirmation message": "確認メッセージ", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "スマートチャットボットを接続し、世界で最も高度な人工知能の1つを使用して会話を自動化します。", "Connect stores to agents.": "ストアをエージェントに接続します。", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "Telegramボットを {R} に接続して、Telegramボットに送信されたすべてのメッセージを {R} で直接読み取り、返信します。", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "Viberボットを {R} に接続して、Viberボットに送信されたすべてのメッセージを {R} で直接読み取り、返信します。", "Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.": "Zalo公式アカウントを {R} に接続して、Zalo公式アカウントに送信されたすべてのメッセージを {R} で直接読み取り、返信します。", "Content": "コンテンツ", "Content template SID": "コンテンツテンプレートSID", "Conversation profile": "会話プロフィール", "Conversations data": "会話データ", "Convert all emails": "すべてのメールを変換", "Cookie domain": "クッキードメイン", "Country": "国", "Coupon discount (%)": "クーポン割引（%）", "Coupon expiration (days)": "クーポン有効期限（日数）", "Coupon expiration (seconds)": "クーポン有効期限（秒）", "Create a WordPress user upon registration.": "登録時にWordPressユーザーを作成します。", "Create Intents now": "今すぐインテントを作成", "Currency symbol": "通貨記号", "Custom CSS": "カスタムCSS", "Custom fields": "カスタムフィールド", "Custom JS": "カスタムJS", "Custom model ID": "カスタムモデルID", "Custom parameters": "カスタムパラメータ", "Customize the link for the 'All articles' button.": "「すべての記事」ボタンのリンクをカスタマイズします。", "Dashboard display": "ダッシュボード表示", "Dashboard title": "ダッシュボードタイトル", "Database details": "データベース詳細", "Database host": "データベースホスト", "Database name": "データベース名", "Database password": "データベースパスワード", "Database prefix": "データベースプレフィックス", "Database user": "データベースユーザー", "Decline button text": "拒否ボタンのテキスト", "Declined message": "拒否されたメッセージ", "Default": "デフォルト", "Default body text": "デフォルトの本文テキスト", "Default conversation name": "デフォルトの会話名", "Default department": "デフォルト部署", "Default department ID": "デフォルト部署ID", "Default form": "デフォルトフォーム", "Default header text": "デフォルトのヘッダーテキスト", "Delay (ms)": "遅延（ミリ秒）", "Delete all leads and all messages and conversations linked to them.": "すべてのリードとそれらにリンクされたすべてのメッセージおよび会話を削除します。", "Delete conversation": "会話を削除", "Delete leads": "リードを削除", "Delete message": "メッセージを削除", "Delete the built-in flows.": "組み込みフローを削除します。", "Delimiter": "区切り文字", "Department": "部署", "Department ID": "部署ID", "Departments": "部署（複数）", "Departments settings": "部署設定", "Desktop notifications": "デスクトップ通知", "Dialogflow - Department linking": "Dialogflow - 部署連携", "Dialogflow chatbot": "Dialogflowチャットボット", "Dialogflow edition": "Dialogflowエディション", "Dialogflow Intent detection confidence": "Dialogflowインテント検出信頼度", "Dialogflow location": "Dialogflowロケーション", "Dialogflow spelling correction": "Dialogflowスペル修正", "Dialogflow welcome Intent": "Dialogflowウェルカムインテント", "Disable agents check": "エージェントチェックを無効化", "Disable and hide the chat widget if all agents are offline.": "すべてのエージェントがオフラインの場合、チャットウィジェットを無効にして非表示にします。", "Disable and hide the chat widget outside of scheduled office hours.": "営業時間外にチャットウィジェットを無効にして非表示にします。", "Disable any features that you don't need.": "不要な機能を無効にします。", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "チャットウィジェットの自動初期化を無効にします。この設定が有効な場合、自分で作成したカスタムJavaScript APIコードでチャットウィジェットを初期化する必要があります。チャットが表示されず、この設定が有効になっている場合は、無効にしてください。", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "チケットエリアの自動初期化を無効にします。この設定が有効な場合、自分で作成したカスタムJavaScript APIコードでチケットエリアを初期化する必要があります。チケットエリアが表示されず、この設定が有効になっている場合は、無効にしてください。", "Disable chatbot": "チャットボットを無効化", "Disable cron job": "cronジョブを無効化", "Disable dashboard": "ダッシュボードを無効化", "Disable during office hours": "営業時間中に無効化", "Disable features": "機能を無効化", "Disable features you don't use and improve the chat performance.": "使用しない機能を無効にして、チャットのパフォーマンスを向上させます。", "Disable file uploading capabilities within the chat.": "チャット内でのファイルアップロード機能を無効にします。", "Disable for messaging channels": "メッセージングチャネルに対して無効化", "Disable for the tickets area": "チケットエリアに対して無効化", "Disable invitation": "招待を無効化", "Disable online status check": "オンラインステータスチェックを無効化", "Disable outside of office hours": "営業時間外に無効化", "Disable password": "パスワードを無効化", "Disable registration during office hours": "営業時間中に登録を無効化", "Disable registration if agents online": "エージェントがオンラインの場合に登録を無効化", "Disable the automatic invitation of agents to the channels.": "チャネルへのエージェントの自動招待を無効にします。", "Disable the channels filter.": "チャネルフィルターを無効にします。", "Disable the chatbot for the tickets area.": "チケットエリアのチャットボットを無効にします。", "Disable the chatbot for this channel only.": "このチャネルのみチャットボットを無効にします。", "Disable the dashboard, and allow only one conversation per user.": "ダッシュボードを無効にし、ユーザーごとに1つの会話のみ許可します。", "Disable the login and remove the password field from the registration form.": "ログインを無効にし、登録フォームからパスワードフィールドを削除します。", "Disable uploads": "アップロードを無効化", "Disable voice message capabilities within the chat.": "チャット内での音声メッセージ機能を無効にします。", "Disable voice messages": "音声メッセージを無効化", "Disabled": "無効", "Display a brand image in the header area. This only applies for the 'brand' header type.": "ヘッダーエリアにブランド画像を表示します。これは「ブランド」ヘッダータイプにのみ適用されます。", "Display categories": "カテゴリを表示", "Display images": "画像を表示", "Display in conversation list": "会話リストに表示", "Display in dashboard": "ダッシュボードに表示", "Display online agents only": "オンラインエージェントのみ表示", "Display the articles section in the right area.": "右側のエリアに記事セクションを表示します。", "Display the dashboard instead of the chat area on initialization.": "初期化時にチャットエリアの代わりにダッシュボードを表示します。", "Display the feedback form to rate the conversation when it is archived.": "アーカイブ時に会話を評価するためのフィードバックフォームを表示します。", "Display the user full name in the left panel instead of the conversation title.": "会話タイトルの代わりに左パネルにユーザーのフルネームを表示します。", "Display the user's profile image within the chat.": "チャット内にユーザーのプロフィール画像を表示します。", "Display user name in header": "ヘッダーにユーザー名を表示", "Display user's profile image": "ユーザーのプロフィール画像を表示", "Displays additional columns in the user table. Enter the name of the fields to add.": "ユーザーテーブルに追加の列を表示します。追加するフィールドの名前を入力してください。", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "エージェント間で会話を均等に分散し、訪問者にキュー内の位置を通知します。応答時間は分単位です。メッセージ内で次のマージフィールドを使用できます: {position}, {minutes}。これらはリアルタイムで実際の値に置き換えられます。", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "エージェント間で会話を均等に分散し、エージェントが他のエージェントの会話を表示できないようにします。", "Do not send email notifications to admins": "管理者にメール通知を送信しない", "Do not show tickets in chat": "チャットにチケットを表示しない", "Do not translate settings area": "設定エリアを翻訳しない", "Download": "ダウンロード", "Edit profile": "プロフィールを編集", "Edit user": "ユーザーを編集", "Email address": "メールアドレス", "Email and ticket": "メールとチケット", "Email header": "メールヘッダー", "Email notification delay (hours)": "メール通知遅延（時間）", "Email notifications via cron job": "cronジョブによるメール通知", "Email only": "メールのみ", "Email piping": "メールパイピング", "Email piping server information and more settings.": "メールパイピングサーバー情報およびその他の設定。", "Email request message": "メールリクエストメッセージ", "Email signature": "メール署名", "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "エージェントが返信したときにユーザーに送信されるメールのテンプレート。テキスト、HTML、および次のマージフィールドを使用できます: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}。", "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "ユーザーが新しいメッセージを送信したときにエージェントに送信されるメールのテンプレート。テキスト、HTML、および次のマージフィールドを使用できます: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}。", "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "フォローアップメッセージフォームを通じてメールアドレスを送信した後にユーザーに送信されるメールのテンプレート。テキスト、HTML、および次のマージフィールドを使用できます: {user_name}, {user_email}。", "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.": "メールアドレスを確認するためにユーザーに送信されるメールのテンプレート。コンテンツ内に {code} マージフィールドを含めてください。これはワンタイムコードに置き換えられます。", "Email verification": "メール認証", "Email verification content": "メール認証コンテンツ", "Enable email verification with OTP.": "OTPによるメール認証を有効にします。", "Enable logging of agent activity": "エージェントアクティビティのログ記録を有効にする", "Enable the chatbot outside of scheduled office hours only.": "営業時間外のみチャットボットを有効にします。", "Enable the registration only if all agents are offline.": "すべてのエージェントがオフラインの場合のみ登録を有効にします。", "Enable the registration outside of scheduled office hours only.": "営業時間外のみ登録を有効にします。", "Enable this option if email notifications are sent via cron job.": "メール通知がcronジョブ経由で送信される場合は、このオプションを有効にしてください。", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "購読者のみにチケットとチャットサポートを有効にし、管理エリアでメンバープロフィール詳細と購読詳細を表示します。", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "ボットトークンを入力し、ボタンをクリックしてTelegramボットを同期します。Localhostはメッセージを受信できません。", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "ボットトークンを入力し、ボタンをクリックしてViberボットを同期します。Localhostはメッセージを受信できません。", "Enter the database details of the Active eCommerce CMS database.": "Active eCommerce CMSデータベースのデータベース詳細を入力します。", "Enter the database details of the Martfury database.": "Martfuryデータベースのデータベース詳細を入力します。", "Enter the database details of the Perfex database.": "Perfexデータベースのデータベース詳細を入力します。", "Enter the database details of the WHMCS database.": "WHMCSデータベースのデータベース詳細を入力します。", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "ユーザーの質問に動的な回答が必要な場合にチャットボットが使用するデフォルトメッセージを入力します。", "Enter the details of your Google Business Messages.": "Google Business Messagesの詳細を入力します。", "Enter the details of your Twitter app.": "Twitterアプリの詳細を入力します。", "Enter the LINE details to start using it. Localhost cannot receive messages.": "LINEの使用を開始するためのLINE詳細を入力します。Localhostはメッセージを受信できません。", "Enter the URL of a .css file, to load it automatically in the admin area.": "管理エリアで自動的に読み込む.cssファイルのURLを入力します。", "Enter the URL of a .js file, to load it automatically in the admin area.": "管理エリアで自動的に読み込む.jsファイルのURLを入力します。", "Enter the URL of the articles page.": "記事ページのURLを入力します。", "Enter the URLs of your shop": "ショップのURLを入力してください", "Enter the WeChat official account token. See the docs for more details.": "WeChat公式アカウントトークンを入力します。詳細についてはドキュメントを参照してください。", "Enter the Zalo details to start using it. Localhost cannot receive messages.": "Zaloの使用を開始するためのZalo詳細を入力します。Localhostはメッセージを受信できません。", "Enter your 360dialog account settings information.": "360dialogアカウント設定情報を入力します。", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "自動更新を有効にし、すべての機能のロックを解除するために、Envato購入コードを入力してください。", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "T<PERSON><PERSON>アカウント詳細を入力します。テキストと次のマージフィールドを使用できます: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}。", "Enter your Twilio account settings information.": "<PERSON><PERSON><PERSON>アカウント設定情報を入力します。", "Enter your WeChat Official Account information.": "WeChat公式アカウント情報を入力します。", "Enter your Zendesk information.": "Zendesk情報を入力します。", "Entities": "エンティティ", "Envato Purchase Code": "Envato購入コード", "Envato purchase code validation": "Envato購入コード検証", "Exclude products": "製品を除外", "Export all settings.": "すべての設定をエクスポートします。", "Export settings": "設定をエクスポート", "Facebook pages": "Facebookページ", "Fallback message": "フォールバックメッセージ", "Filters": "フィルター", "First chat message": "最初のチャットメッセージ", "First reminder delay (hours)": "最初のリマインダー遅延（時間）", "First ticket form": "最初のチケットフォーム", "Flash notifications": "フラッシュ通知", "Follow up - Email": "フォローアップ - Eメール", "Follow up email": "フォローアップメール", "Follow up message": "フォローアップメッセージ", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "人間のエージェントとエンドユーザー間の会話をフォローし、リアルタイムで人間のエージェントに応答候補を提供します。", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "フォローアップメールテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます: {coupon}, {product_names}, {user_name}。", "Force language": "言語を強制", "Force log out": "強制ログアウト", "Force the chat to ignore the language preferences, and to use always the same language.": "チャットが言語設定を無視し、常に同じ言語を使用するように強制します。", "Force the loggout of Support Board agents if they are not logged in WordPress.": "Support BoardエージェントがWordPressにログインしていない場合、強制的にログアウトさせます。", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "ユーザーが各ストアごとに異なる会話を使用し、ストア管理者から他のストアの会話を非表示にするように強制します。", "Force users to use only one phone country code.": "ユーザーが1つの電話国コードのみを使用するように強制します。", "Form message": "フォームメッセージ", "Form title": "フォームタイトル", "Frequency penalty": "頻度ペナルティ", "Full visitor details": "完全な訪問者詳細", "Function name": "関数名", "Generate conversations data": "会話データを生成", "Generate user questions": "ユーザーの質問を生成", "Get configuration URL": "構成URLを取得", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "Active eCommerceのルートディレクトリにあるファイル .env のAPP_KEY値から取得します。", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "Martfuryのルートディレクトリにあるファイル .env のAPP_KEY値から取得します。", "Get Path": "パスを取得", "Get Service Worker path": "Service Workerパスを取得", "Get URL": "URLを取得", "Google and Dialogflow settings.": "GoogleおよびDialogflow設定。", "Google search": "Google検索", "Header": "ヘッダー", "Header background image": "ヘッダー背景画像", "Header brand image": "ヘッダーブランド画像", "Header message": "ヘッダーメッセージ", "Header title": "ヘッダータイトル", "Header type": "ヘッダータイプ", "Header variables": "ヘッダー変数", "Hide": "非表示", "Hide agent's profile image": "エージェントのプロフィール画像を非表示", "Hide archived tickets": "アーカイブ済みチケットを非表示", "Hide archived tickets from users.": "ユーザーからアーカイブ済みチケットを非表示にします。", "Hide chat if no agents online": "オンラインのエージェントがいない場合にチャットを非表示", "Hide chat outside of office hours": "営業時間外にチャットを非表示", "Hide conversation details panel": "会話詳細パネルを非表示", "Hide conversations of other agents": "他のエージェントの会話を非表示", "Hide on mobile": "モバイルで非表示", "Hide the agent's profile image within the chat.": "チャット内でエージェントのプロフィール画像を非表示にします。", "Hide tickets from the chat widget and chats from the ticket area.": "チャットウィジェットからチケットを、チケットエリアからチャットを非表示にします。", "Hide timetable": "タイムテーブルを非表示", "Host": "ホスト", "Human takeover": "人間による介入", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "指定された時間内にエージェントが応答しない場合、メールアドレスなどのユーザーの詳細を要求するメッセージが送信されます。", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "チャットボットがユーザーの質問を理解できない場合、会話をエージェントに転送します。", "Image": "画像", "Import admins": "管理者をインポート", "Import all settings.": "すべての設定をインポートします。", "Import articles": "記事をインポート", "Import contacts": "連絡先をインポート", "Import customers": "顧客をインポート", "Import customers into Support Board. Only new customers will be imported.": "顧客をSupport Boardにインポートします。新規顧客のみがインポートされます。", "Import settings": "設定をインポート", "Import users": "ユーザーをインポート", "Import users from a CSV file.": "CSVファイルからユーザーをインポートします。", "Import vendors": "ベンダーをインポート", "Import vendors into Support Board as agents. Only new vendors will be imported.": "ベンダーをエージェントとしてSupport Boardにインポートします。新規ベンダーのみがインポートされます。", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "PusherとWebSocketでチャットのパフォーマンスを向上させます。この設定は、サーバーを遅くするすべてのAJAX/HTTPリアルタイムリクエストを停止し、代わりにWebSocketを使用します。", "Include custom fields": "カスタムフィールドを含める", "Include custom fields in the registration form.": "登録フォームにカスタムフィールドを含めます。", "Include the password field in the registration form.": "登録フォームにパスワードフィールドを含めます。", "Incoming conversations and messages": "受信会話とメッセージ", "Incoming conversations only": "受信会話のみ", "Incoming messages only": "受信メッセージのみ", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Support Board.": "Active eCommerceをSupport Boardと統合して、売上を増やし、販売者と顧客をリアルタイムで結び付けます。", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Support Board.": "WooCommerceをSupport Boardと統合して、売上を増やし、より良いサポートと迅速なソリューションを提供します。", "Info message": "情報メッセージ", "Initialize and display the chat widget and tickets only for members.": "メンバーのみにチャットウィジェットとチケットを初期化して表示します。", "Initialize and display the chat widget only when the user is logged in.": "ユーザーがログインしている場合にのみチャットウィジェットを初期化して表示します。", "Instance ID": "インスタンスID", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "OpenCartを {R} と統合して、顧客のリアルタイム同期、注文履歴へのアクセス、顧客カートの可視性を実現します。", "Interval (sec)": "間隔（秒）", "IP banning": "IP禁止", "Label": "ラベル", "Language": "言語", "Language detection": "言語検出", "Language detection message": "言語検出メッセージ", "Last name": "姓", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "この設定が何であるかわからない場合は、空白のままにしてください！不正な値を入力するとチャットが壊れます。メインドメインとサブドメイン間のログインと会話共有を有効にするために、チャットが使用されるメインドメインを設定します。", "Left": "左", "Left panel": "左パネル", "Left profile image": "左プロフィール画像", "Let the bot to search on Google to find answers to user questions.": "ボットがGoogleで検索してユーザーの質問への回答を見つけられるようにします。", "Let the chatbot search on Google to find answers to user questions.": "チャットボットがGoogleで検索してユーザーの質問への回答を見つけられるようにします。", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "ユーザーがTwitter経由で連絡できるようにします。Twitterアカウントに送信されたメッセージを {R} で直接読み取り、返信します。", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "ユーザーがWeChat経由で連絡できるようにします。WeChat公式アカウントに送信されたすべてのメッセージを {R} で直接読み取り、返信します。", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "ユーザーがWhatsApp経由で連絡できるようにします。WhatsApp Businessアカウントに送信されたすべてのメッセージを {R} で直接読み取り、返信します。", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "各エージェントを対応するSlackユーザーとリンクさせ、エージェントがSlack経由で返信すると、割り当てられたエージェントとして表示されるようにします。", "Link name": "リンク名", "Login form": "ログインフォーム", "Login initialization": "ログイン初期化", "Login verification URL": "ログイン認証URL", "Logit bias": "Logitバイアス (※AI関連用語、文脈により調整)", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "まずDialogflowエージェントのバックアップを作成してください。この操作には数分かかる場合があります。", "Make the registration phone field mandatory.": "登録電話フィールドを必須にします。", "Manage": "管理", "Manage here the departments settings.": "ここで部署設定を管理します。", "Manage the tags settings.": "タグ設定を管理します。", "Manifest file URL": "マニフェストファイルURL", "Manual": "手動", "Manual initialization": "手動初期化", "Martfury root directory path, e.g. /var/www/": "Mart<PERSON>ryルートディレクトリパス、例: /var/www/", "Martfury shop URL, e.g. https://shop.com": "MartfuryショップURL、例: https://shop.com", "Max message limit": "最大メッセージ制限", "Max tokens": "最大トークン数 (※AI関連用語)", "Members only": "メンバーのみ", "Members with an active paid plan only": "アクティブな有料プランを持つメンバーのみ", "Message": "メッセージ", "Message area": "メッセージエリア", "Message rewrite button": "メッセージ書き換えボタン", "Message template": "メッセージテンプレート", "Message type": "メッセージタイプ", "Messaging channels": "メッセージングチャネル", "Messenger and Instagram settings": "MessengerおよびInstagram設定", "Minify JS": "JSを最小化", "Minimal": "最小限", "Model": "モデル (※AI関連用語)", "Multilingual": "多言語", "Multilingual plugin": "多言語プラグイン", "Multilingual via translation": "翻訳による多言語", "Multlilingual training sources": "多言語トレーニングソース", "Name": "名前", "Namespace": "名前空間", "New conversation email": "新しい会話メール", "New conversation notification": "新しい会話通知", "New ticket button": "新しいチケットボタン", "Newsletter": "ニュースレター", "No delay": "遅延なし", "No results found.": "結果が見つかりません。", "No, we don't ship in": "いいえ、 には発送しません", "None": "なし", "Note data scraping": "メモデータスクレイピング", "Notes": "メモ", "Notifications icon": "通知アイコン", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "営業時間外またはすべてのエージェントがオフラインのときにメッセージが送信された場合、ユーザーに通知します。", "OA secret key": "OAシークレットキー", "Offline message": "オフラインメッセージ", "Offset": "オフセット", "On chat open": "チャットを開いたとき", "On page load": "ページ読み込み時", "One conversation per agent": "エージェントごとに1つの会話", "One conversation per department": "部署ごとに1つの会話", "Online users notification": "オンラインユーザー通知", "Only desktop": "デスクトップのみ", "Only general questions": "一般的な質問のみ", "Only mobile devices": "モバイルデバイスのみ", "Only questions related to your sources": "あなたのソースに関連する質問のみ", "Open automatically": "自動的に開く", "Open chat": "チャットを開く", "Open the chat window automatically when a new message is received.": "新しいメッセージを受信したときにチャットウィンドウを自動的に開きます。", "OpenAI Assistants - Department linking": "OpenAI Assistants - 部署連携", "OpenAI settings.": "OpenAI設定。", "Optional link": "オプションのリンク", "Order webhook": "注文Webhook", "Other": "その他", "Outgoing SMTP server information.": "送信SMTPサーバー情報。", "Page ID": "ページID", "Page IDs": "ページID（複数）", "Page name": "ページ名", "Page token": "ページトークン", "Panel height": "パネルの高さ", "Panel name": "パネル名", "Panel title": "パネルタイトル", "Panels arrows": "パネル矢印", "Password": "パスワード", "Perfex URL": "Perfex URL", "Performance optimization": "パフォーマンス最適化", "Phone": "電話番号", "Phone number ID": "電話番号ID", "Phone required": "電話番号必須", "Place ID": "場所ID", "Placeholder text": "プレースホルダーテキスト", "Play a sound for new messages and conversations.": "新しいメッセージや会話に対してサウンドを再生します。", "Popup message": "ポップアップメッセージ", "Port": "ポート", "Post Type slugs": "投稿タイプスラッグ", "Presence penalty": "存在ペナルティ (※AI関連用語)", "Prevent admins from receiving email notifications.": "管理者がメール通知を受信しないようにします。", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "エージェントが他のエージェントに割り当てられた会話を表示できないようにします。この設定は、ルーティングまたはキューがアクティブな場合に自動的に有効になります。", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "1つのデバイスからチャットボットに送信されるメッセージ数を制限することで、ユーザーによる乱用を防ぎます。", "Primary color": "プライマリカラー", "Priority": "優先度", "Privacy link": "プライバシーリンク", "Privacy message": "プライバシーメッセージ", "Private chat": "プライベートチャット", "Private chat linking": "プライベートチャット連携", "Private key": "秘密鍵", "Product IDs": "製品ID", "Product removed notification": "製品削除通知", "Product removed notification - Email": "製品削除通知 - Eメール", "Profile image": "プロフィール画像", "Project ID": "プロジェクトID", "Project ID or Agent Name": "プロジェクトIDまたはエージェント名", "Prompt": "プロンプト (※AI関連用語)", "Prompt - Message rewriting": "プロンプト - メッセージ書き換え", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "Google reCAPTCHAでチケットエリアをスパムや乱用から保護します。", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "チケットエリア（すべてのチャット機能を含む）を任意のWebページに数秒で含めることで、顧客にヘルプデスクサポートを提供します。", "Provider": "プロバイダー", "Purchase button text": "購入ボタンのテキスト", "Push notifications": "プッシュ通知", "Push notifications settings.": "プッシュ通知設定。", "Queue": "キュー", "Rating": "評価", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "Google検索、マップ、ブランド所有チャネルから送信されたメッセージを {R} で直接読み取り、返信します。", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "FacebookページやInstagramアカウントに送信されたすべてのメッセージを {R} で直接読み取り、管理し、返信します。", "Reconnect": "再接続", "Redirect the user to the registration link instead of showing the registration form.": "登録フォームを表示する代わりに、ユーザーを登録リンクにリダイレクトします。", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "登録が必要でユーザーがログインしていない場合、指定されたURLにユーザーをリダイレクトします。デフォルトの登録フォームを使用する場合は空白のままにします。", "Refresh token": "リフレッシュトークン", "Register all visitors": "すべての訪問者を登録", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "すべての訪問者を自動的に登録します。このオプションがアクティブでない場合、チャットを開始した訪問者のみが登録されます。", "Registration / Login": "登録 / ログイン", "Registration and login form": "登録およびログインフォーム", "Registration fields": "登録フィールド", "Registration form": "登録フォーム", "Registration link": "登録リンク", "Registration redirect": "登録リダイレクト", "Rename the chat bot. Default is 'Bot'.": "チャットボットの名前を変更します。デフォルトは「Bo<PERSON>」です。", "Rename the visitor name prefix. Default is 'User'.": "訪問者名のプレフィックスを変更します。デフォルトは「User」です。", "Repeat": "繰り返す", "Repeat - admin": "繰り返す - 管理者", "Replace the admin login page message.": "管理者ログインページのメッセージを置き換えます。", "Replace the brand logo on the admin login page.": "管理者ログインページのブランドロゴを置き換えます。", "Replace the header title with the user's first name and last name when available.": "利用可能な場合、ヘッダータイトルをユーザーの名と姓に置き換えます。", "Replace the top-left brand icon on the admin area and the browser favicon.": "管理エリアの左上のブランドアイコンとブラウザのファビコンを置き換えます。", "Reply to user emails": "ユーザーのメールに返信する", "Reply to user text messages": "ユーザーのテキストメッセージに返信する", "Reports": "レポート", "Reports area": "レポートエリア", "Request a valid Envato purchase code for registration.": "登録に有効なEnvato購入コードを要求します。", "Request the user to provide their email address and then send a confirmation email to the user.": "ユーザーにメールアドレスの提供を要求し、その後確認メールをユーザーに送信します。", "Require phone": "電話番号を要求", "Require registration": "登録を要求", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "チャットを開始する前にユーザー登録またはログインを要求します。ログインエリアを有効にするには、パスワードフィールドを含める必要があります。", "Require the user registration or login in order to use the tickets area.": "チケットエリアを使用するためにユーザー登録またはログインを要求します。", "Required": "必須", "Response time": "応答時間", "Restrict chat access by blocking IPs. List IPs with commas.": "IPをブロックしてチャットアクセスを制限します。IPをカンマでリストします。", "Returning visitor message": "リピート訪問者メッセージ", "Rich messages": "リッチメッセージ", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "リッチメッセージは、チャットメッセージ内で利用できるコードスニペットです。HTMLコードを含めることができ、チャット内で自動的にレンダリングされます。リッチメッセージは次の構文で使用できます: [rich-message-name]。組み込みのリッチメッセージがたくさんあります。", "Right": "右", "Right panel": "右パネル", "Routing": "ルーティング", "Routing if offline": "オフライン時のルーティング", "Save useful information like user country and language also for visitors.": "訪問者に対しても、ユーザーの国や言語などの有用な情報を保存します。", "Saved replies": "定型返信", "Scheduled office hours": "営業時間", "Search engine ID": "検索エンジンID", "Second chat message": "2番目のチャットメッセージ", "Second reminder delay (hours)": "2番目のリマインダー遅延（時間）", "Secondary color": "セカンダリカラー", "Secret key": "秘密鍵", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "現在在庫切れだが興味のある製品を購入できるようになったときに顧客に通知できるようにメッセージを送信します。次のマージフィールドを使用できます: {user_name}, {product_name}。", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "最初のチケットを作成したときに新規ユーザーにメッセージを送信します。テキストフォーマットとマージフィールドがサポートされています。", "Send a message to new users when they visit the website for the first time.": "初めてウェブサイトを訪れた新規ユーザーにメッセージを送信します。", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "カートから商品が削除された後、顧客にメッセージを送信します。次のマージフィールドなどを使用できます: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}。", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "購入を完了した顧客に、購入したばかりの製品を共有するように依頼するメッセージを送信します。次のマージフィールドなどを使用できます: {product_name}, {user_name}。", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "購入を完了した顧客にメッセージを送信します。次のマージフィールドなどを使用できます: {coupon}, {product_names}, {user_name}。", "Send a message to the user when the agent archive the conversation.": "エージェントが会話をアーカイブしたときにユーザーにメッセージを送信します。", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "少なくとも24時間後に再びウェブサイトを訪れたユーザーにメッセージを送信します。次のマージフィールドなどを使用できます: {coupon}, {user_name}。詳細についてはドキュメントを参照してください。", "Send a test agent notification email to verify email settings.": "メール設定を確認するためにテストエージェント通知メールを送信します。", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "Slackチャネルにテストメッセージを送信します。これは送信メッセージの送信機能のみをテストします。", "Send a test user notification email to verify email settings.": "メール設定を確認するためにテストユーザー通知メールを送信します。", "Send a text message to the provided phone number.": "提供された電話番号にテキストメッセージを送信します。", "Send a user email notification": "ユーザーメール通知を送信", "Send a user text message notifcation": "ユーザーテキストメッセージ通知を送信", "Send a user text message notification": "ユーザーテキストメッセージ通知を送信 (※重複?)", "Send an agent email notification": "エージェントメール通知を送信", "Send an agent text message notification": "エージェントテキストメッセージ通知を送信", "Send an agent user text notification": "エージェントユーザーテキスト通知を送信", "Send an email notification to the provided email address.": "提供されたメールアドレスにメール通知を送信します。", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "ユーザーが返信し、エージェントがオフラインの場合、エージェントにメールを送信します。新しい会話については、すべてのエージェントに自動的にメールが送信されます。", "Send an email to the user when a new conversation is created.": "新しい会話が作成されたときにユーザーにメールを送信します。", "Send an email to the user when a new conversation or ticket is created": "新しい会話またはチケットが作成されたときにユーザーにメールを送信します", "Send an email to the user when an agent replies and the user is offline.": "エージェントが返信し、ユーザーがオフラインの場合、ユーザーにメールを送信します。", "Send email": "メールを送信", "Send login details to the specified URL and allow access only if the response is positive.": "指定されたURLにログイン詳細を送信し、応答が肯定的な場合にのみアクセスを許可します。", "Send message": "メッセージを送信", "Send message to Slack": "Slackにメッセージを送信", "Send message via enter button": "Enterボタンでメッセージを送信", "Send text message": "テキストメッセージを送信", "Send the message template to a WhatsApp number.": "メッセージテンプレートをWhatsApp番号に送信します。", "Send the message via the ENTER keyboard button.": "キーボードのENTERボタン経由でメッセージを送信します。", "Send the user details of the registration form and email rich messages to Dialogflow.": "登録フォームとメールリッチメッセージのユーザー詳細をDialogflowに送信します。", "Send the WhatsApp order details to the URL provided.": "提供されたURLにWhatsApp注文詳細を送信します。", "Send to user's email": "ユーザーのメールに送信", "Send transcript to user's email": "トランスクリプトをユーザーのメールに送信", "Send user details": "ユーザー詳細を送信", "Sender": "送信者", "Sender email": "送信者メール", "Sender name": "送信者名", "Sender number": "送信者番号", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "WhatsAppメッセージの送信に失敗した場合にテキストメッセージを送信します。テキストと次のマージフィールドを使用できます: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}。", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "WhatsAppメッセージの送信に失敗した場合にWhatsAppテンプレート通知を送信します。テキストと次のマージフィールドを使用できます: {conversation_url_parameter}, {recipient_name}, {recipient_email}。", "Service": "サービス", "Service Worker path": "Service Workerパス", "Service Worker URL": "Service Worker URL", "Set a dedicated Dialogflow agent for each department.": "各部署に専用のDialogflowエージェントを設定します。", "Set a dedicated OpenAI Assistants for each department.": "各部署に専用のOpenAI Assistantsを設定します。", "Set a dedicated Slack channel for each department.": "各部署に専用のSlackチャネルを設定します。", "Set a profile image for the chat bot.": "チャットボットのプロフィール画像を設定します。", "Set the articles panel title. Default is 'Help Center'.": "記事パネルのタイトルを設定します。デフォルトは「ヘルプセンター」です。", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "メッセージの隣に表示されるアバター画像を設定します。1024x1024pxのJPG画像で、最大サイズは50KBである必要があります。", "Set the chat language or translate it automatically to match the user language. Default is English.": "チャット言語を設定するか、ユーザー言語に合わせて自動的に翻訳します。デフォルトは英語です。", "Set the currency symbol of the membership prices.": "メンバーシップ価格の通貨記号を設定します。", "Set the currency symbol used by your system.": "システムで使用される通貨記号を設定します。", "Set the default departments for all tickets. Enter the department ID.": "すべてのチケットのデフォルト部署を設定します。部署IDを入力してください。", "Set the default email header that will be prepended to automated emails and direct emails.": "自動メールとダイレクトメールの先頭に追加されるデフォルトのメールヘッダーを設定します。", "Set the default email signature that will be appended to automated emails and direct emails.": "自動メールとダイレクトメールの末尾に追加されるデフォルトのメール署名を設定します。", "Set the default form to display if the registraion is required.": "登録が必要な場合に表示するデフォルトフォームを設定します。", "Set the default name to use for conversations without a name.": "名前のない会話に使用するデフォルト名を設定します。", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "デフォルトの通知アイコンを設定します。ユーザーがプロフィール画像を持っていない場合、アイコンがプロフィール画像として使用されます。", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "エージェントが利用可能として表示されるデフォルトの営業時間を設定します。これらの設定は、営業時間に依存する他のすべての設定にも使用されます。", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "ユーザーが名前を持っていない場合にボットメッセージとメールで使用するデフォルトのユーザー名を設定します。", "Set the header appearance.": "ヘッダーの外観を設定します。", "Set the maximum height of the tickets panel.": "チケットパネルの最大高さを設定します。", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "使用している多言語プラグインを設定するか、サイトが1つの言語のみを使用する場合は無効のままにします。", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "エージェントまたは管理者が管理エリアで少なくとも10分間非アクティブな場合、オフラインステータスを自動的に設定します。", "Set the position of the chat widget.": "チャットウィジェットの位置を設定します。", "Set the primary color of the admin area.": "管理エリアのプライマリカラーを設定します。", "Set the primary color of the chat widget.": "チャットウィジェットのプライマリカラーを設定します。", "Set the secondary color of the admin area.": "管理エリアのセカンダリカラーを設定します。", "Set the secondary color of the chat widget.": "チャットウィジェットのセカンダリカラーを設定します。", "Set the tertiary color of the chat widget.": "チャットウィジェットのターシャリカラーを設定します。", "Set the title of the administration area.": "管理エリアのタイトルを設定します。", "Set the title of the conversations panel.": "会話パネルのタイトルを設定します。", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "営業時間タイムテーブルのUTCオフセットを設定します。正しい値は負の場合があり、この入力フィールドが空の場合にクリックすると自動的に生成されます。", "Set which actions to allow agents.": "エージェントに許可するアクションを設定します。", "Set which actions to allow supervisors.": "スーパーバイザーに許可するアクションを設定します。", "Set which user details to send to the main channel. Add comma separated values.": "メインチャネルに送信するユーザー詳細を設定します。カンマ区切りの値を追加します。", "Settings area": "設定エリア", "settings information": "設定情報", "Shop": "ショップ", "Show": "表示", "Show a browser tab notification when a new message is received.": "新しいメッセージを受信したときにブラウザタブ通知を表示します。", "Show a desktop notification when a new message is received.": "新しいメッセージを受信したときにデスクトップ通知を表示します。", "Show a notification and play a sound when a new user is online.": "新しいユーザーがオンラインになったときに通知を表示し、サウンドを再生します。", "Show a pop-up notification to all users.": "すべてのユーザーにポップアップ通知を表示します。", "Show profile images": "プロフィール画像を表示", "Show sender's name": "送信者名を表示", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "ダッシュボードにエージェントメニューを表示し、ユーザーに会話を開始するエージェントを選択させます。", "Show the articles panel on the chat dashboard.": "チャットダッシュボードに記事パネルを表示します。", "Show the categories instead of the articles list.": "記事リストの代わりにカテゴリを表示します。", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "訪問者がカートにアイテムを追加したときにフォローアップメッセージを表示します。メッセージは、ユーザーがまだメールアドレスを提供していない場合にのみ送信されます。", "Show the list of all Slack channels.": "すべてのSlackチャネルのリストを表示します。", "Show the profile image of agents and users within the conversation.": "会話内にエージェントとユーザーのプロフィール画像を表示します。", "Show the sender's name in every message.": "すべてのメッセージに送信者の名前を表示します。", "Single label": "単一ラベル", "Single phone country code": "単一電話国コード", "Site key": "サイトキー", "Slug": "スラッグ", "Smart Reply": "スマートリプライ", "Social share message": "ソーシャルシェアメッセージ", "Sort conversations by date": "会話を日付順に並べ替え", "Sound": "サウンド", "Sound settings": "サウンド設定", "Sounds": "サウンド（複数）", "Sounds - admin": "サウンド - 管理者", "Source links": "ソースリンク", "Speech recognition": "音声認識", "Spelling correction": "スペル修正", "Starred tag": "スター付きタグ", "Start importing": "インポート開始", "Store name": "ストア名", "Subject": "件名", "Subscribe": "購読する", "Subscribe users to your preferred newsletter service when they provide an email.": "メールを提供したときに、ユーザーを希望のニュースレターサービスに購読させます。", "Subtract the offset value from the height value.": "高さの値からオフセット値を引きます。", "Success message": "成功メッセージ", "Supervisors": "スーパーバイザー", "Support Board path": "Support Boardパス", "Sync admin and staff accounts with Support Board. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "管理者アカウントとスタッフアカウントをSupport Boardと同期します。スタッフユーザーはエージェントとして、管理者は管理者として登録されます。新規ユーザーのみがインポートされます。", "Sync all contacts of all clients with Support Board. Only new contacts will be imported.": "すべてのクライアントのすべての連絡先をSupport Boardと同期します。新規連絡先のみがインポートされます。", "Sync all users with Support Board. Only new users will be imported.": "すべてのユーザーをSupport Boardと同期します。新規ユーザーのみがインポートされます。", "Sync all WordPress users with Support Board. Only new users will be imported.": "すべてのWordPressユーザーをSupport Boardと同期します。新規ユーザーのみがインポートされます。", "Sync knowledge base articles with Support Board. Only new articles will be imported.": "ナレッジベース記事をSupport Boardと同期します。新規記事のみがインポートされます。", "Sync mode": "同期モード", "Synchronization": "同期", "Synchronize": "同期する", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "顧客を同期し、購読者のみにチケットとチャットサポートを有効にし、管理エリアで購読プランを表示します。", "Synchronize emails": "メールを同期", "Synchronize Entities": "エンティティを同期", "Synchronize Entities now": "今すぐエンティティを同期", "Synchronize now": "今すぐ同期", "Synchronize users": "ユーザーを同期", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "顧客をリアルタイムで同期し、チャットしてエンゲージメントを高めたり、より良く迅速なサポートを提供したりします。", "Synchronize your Messenger and Instagram accounts.": "MessengerおよびInstagramアカウントを同期します。", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "Perfexの顧客をリアルタイムで同期し、チャットで連絡できるようにします！プロフィール詳細の表示、積極的なエンゲージメントなど。", "Synchronize your WhatsApp Cloud API account.": "WhatsApp Cloud APIアカウントを同期します。", "System requirements": "システム要件", "Tags": "タグ", "Tags settings": "タグ設定", "Template default language": "テンプレートのデフォルト言語", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "エージェントが返信したときにユーザーに送信されるメールのテンプレート。テキスト、HTML、および次のマージフィールドを使用できます: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}。", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "新しい会話が作成されたときにユーザーに送信されるメールのテンプレート。テキスト、HTML、および次のマージフィールドを使用できます: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}。", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "新しい会話またはチケットが作成されたときにユーザーに送信されるメールのテンプレート。テキスト、HTML、および次のマージフィールドを使用できます: {conversation_url_parameter}, {user_name}, {message}, {attachments}。", "Template languages": "テンプレート言語", "Template name": "テンプレート名", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "管理者通知メールのテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます: {carts}。通知を送信したいメールアドレスをメールアドレスフィールドに入力します。", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "カートから商品が削除された後に顧客に送信されるメールのテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}。", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "最初の通知メールのテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}。", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "2番目の通知メールのテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}。", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "待機リスト通知メールのテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}。", "Terms link": "利用規約リンク", "Tertiary color": "ターシャリカラー", "Test Slack": "Slackをテスト", "Test template": "テストテンプレート", "Text": "テキスト", "Text message fallback": "テキストメッセージフォールバック", "Text message notifications": "テキストメッセージ通知", "Text messages": "テキストメッセージ", "The product is not in the cart.": "製品はカートにありません。", "The workspace name you are using to synchronize Slack.": "Slack同期に使用しているワークスペース名。", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "これはメインのSlackチャネルIDで、通常は#generalチャネルです。Slack同期を完了するとこのコードを取得できます。", "This returns the Support Board path of your server.": "これはサーバーのSupport Boardパスを返します。", "This returns your Support Board URL.": "これはあなたのSupport Board URLを返します。", "Ticket custom fields": "チケットカスタムフィールド", "Ticket email": "チケットメール", "Ticket field names": "チケットフィールド名", "Ticket fields": "チケットフィールド", "Ticket only": "チケットのみ", "Ticket products selector": "チケット製品セレクター", "Title": "タイトル", "Top": "上", "Top bar": "トップバー", "Training via cron job": "cronジョブによるトレーニング", "Transcript": "トランスクリプト", "Transcript settings.": "トランスクリプト設定。", "Trigger": "トリガー", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "ウェルカムメッセージがアクティブな場合、新規訪問者に対してDialogflowウェルカムインテントをトリガーします。", "Troubleshoot": "トラブルシュート", "Troubleshoot problems": "問題をトラブルシュート", "Twilio settings": "<PERSON><PERSON><PERSON>設定", "Twilio template": "Twilioテンプレート", "Unsubscribe": "購読解除", "Upload attachments to Amazon S3.": "添付ファイルをAmazon S3にアップロードします。", "Usage Limit": "使用制限", "Use this option to change the PWA icon. See the docs for more details.": "このオプションを使用してPWAアイコンを変更します。詳細についてはドキュメントを参照してください。", "User details": "ユーザー詳細", "User details in success message": "成功メッセージ内のユーザー詳細", "User email notifications": "ユーザーメール通知", "User login form information.": "ユーザーログインフォーム情報。", "User message template": "ユーザーメッセージテンプレート", "User name as title": "タイトルとしてのユーザー名", "User notification email": "ユーザー通知メール", "User registration form information.": "ユーザー登録フォーム情報。", "User roles": "ユーザーロール", "User system": "ユーザーシステム", "Username": "ユーザー名", "Users and agents": "ユーザーとエージェント", "Users area": "ユーザーエリア", "Users only": "ユーザーのみ", "Users table additional columns": "ユーザーテーブル追加列", "UTC offset": "UTCオフセット", "Variables": "変数", "View channels": "チャネルを表示", "View unassigned conversations": "未割り当ての会話を表示", "Visibility": "可視性", "Visitor default name": "訪問者のデフォルト名", "Visitor name prefix": "訪問者名プレフィックス", "Volume": "音量", "Volume - admin": "音量 - 管理者", "Waiting list": "待機リスト", "Waiting list - Email": "待機リスト - Eメール", "Webhook URL": "Webhook URL", "Webhooks": "Webhook", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "Webhookは、何かが起こったときに定義した一意のURLにバックグラウンドで送信される情報です。", "Website": "ウェブサイト", "WeChat settings": "WeChat設定", "Welcome message": "ウェルカムメッセージ", "Whmcs admin URL": "WHMCS管理者URL", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "WHMCS管理者URL。例: https://example.com/whmcs/admin/", "WordPress registration": "WordPress登録", "Yes, we ship in": "はい、 に発送します", "You haven't placed an order yet.": "まだ注文していません。", "You will get this code by completing the Dialogflow synchronization.": "Dialogflow同期を完了するとこのコードを取得できます。", "You will get this code by completing the Slack synchronization.": "Slack同期を完了するとこのコードを取得できます。", "You will get this information by completing the synchronization.": "同期を完了するとこの情報を取得できます。", "Your cart is empty.": "あなたのカートは空です。", "Your turn message": "あなたの番ですメッセージ", "Your username": "あなたのユーザー名", "Your WhatsApp catalogue details.": "あなたのWhatsAppカタログ詳細。", "Zendesk settings": "Zendesk設定", "Image recognition": "画像認識", "Logs": "ログ"}