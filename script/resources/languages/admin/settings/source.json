{"{product_name} has no {product_attribute_name} variants.": "", "360dialog settings": "", "360dialog template": "", "Abandoned cart notification": "", "Abandoned cart notification - Admin email": "", "Abandoned cart notification - First email": "", "Abandoned cart notification - Second email": "", "Accept button text": "", "Account SID": "", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "", "Activate the Right-To-Left (RTL) reading layout.": "", "Activate the Slack integration.": "", "Activate the Zendesk integration": "", "Activate this option if you don't want to translate the settings area.": "", "Active": "", "Active - admin": "", "Active eCommerce CMS URL. Ex. https://shop.com/": "", "Active eCommerce URL": "", "Active for agents": "", "Active for users": "", "Active webhooks": "", "Add a delay (ms) to the bot's responses. Default is 2000.": "", "Add and manage additional support departments.": "", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "", "Add and manage tags.": "", "Add comma separated WordPress user roles. The Support Board administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "", "Add custom fields to the new ticket form.": "", "Add custom fields to the user profile details.": "", "Add Intents": "", "Add Intents to saved replies": "", "Add WhatsApp phone number details here.": "", "Adjust the chat button position. Values are in px.": "", "Admin icon": "", "Admin IDs": "", "Admin login logo": "", "Admin login message": "", "Admin notifications": "", "Admin title": "", "Agent area": "", "Agent details": "", "Agent email notifications": "", "Agent ID": "", "Agent linking": "", "Agent message template": "", "Agent notification email": "", "Agent privileges": "", "Agents": "", "Agents and admins tab": "", "Agents menu": "", "Agents only": "", "All": "", "All channels": "", "All messages": "", "All questions": "", "Allow only extended licenses": "", "Allow only one conversation": "", "Allow only one conversation per user.": "", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "", "Allow the chatbot to reply to the user's text messages if the answer is known.": "", "Allow the user to archive a conversation and hide archived conversations.": "", "Allow users to contact you via their favorite messaging apps.": "", "Allow users to select a product on ticket creation.": "", "Always all messages": "", "Always incoming messages only": "", "Always sort conversations by date in the admin area.": "", "API key": "", "Append the registration user details to the success message.": "", "Apply a custom background image for the header area.": "", "Apply changes": "", "Apply to": "", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "", "Archive automatically the conversations marked as read every 24h.": "", "Archive channels": "", "Archive channels now": "", "Articles": "", "Articles area": "", "Articles button link": "", "Articles page URL": "", "Artificial Intelligence": "", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "", "Assign a department to all conversations started from Twitter. Enter the department ID.": "", "Assign a department to all conversations started from Viber. Enter the department ID.": "", "Assign a department to all conversations started from WeChat. Enter the department ID.": "", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "", "Assistant": "", "Assistant ID": "", "Attachments list": "", "Audio file URL - admin": "", "Automatic": "", "Automatic human takeover": "", "Automatic translation": "", "Automatic updates": "", "Automatically archive conversations": "", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "", "Automatically hide the conversation details panel.": "", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "", "Automatically translate admin area": "", "Automatically translate the admin area to match the agent profile language or browser language.": "", "Avatar image": "", "Away mode": "", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "", "Birthday": "", "Body variables": "", "Bot name": "", "Bot profile image": "", "Bot response delay": "", "Bottom": "", "Brand": "", "Built-in chat button icons": "", "Business Account ID": "", "Button action": "", "Button name": "", "Button text": "", "Button variables": "", "Cancel button text": "", "Cart": "", "Cart follow up message": "", "Catalogue details": "", "Catalogue ID": "", "Change the chat button image with a custom one.": "", "Change the default field names.": "", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "", "Channel ID": "", "Channels": "", "Channels filter": "", "Chat": "", "Chat and admin": "", "Chat background": "", "Chat button icon": "", "Chat button offset": "", "Chat message": "", "Chat only": "", "Chat position": "", "Chatbot": "", "Chatbot mode": "", "Check Requirements": "", "Check the server configurations and make sure it has all the requirements.": "", "Checkout": "", "Choose a background texture for the chat header and conversation area.": "", "Choose where to display the chat. Enter the values separated by commas.": "", "Choose which fields to disable from the tickets area.": "", "Choose which fields to include in the new ticket form.": "", "Choose which fields to include in the registration form. The name field is included by default.": "", "Choose which user system the front-end chat will use to register and log in users.": "", "City": "", "Clear flows": "", "Click the button to start the Dialogflow synchronization.": "", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "", "Client email": "", "Client ID": "", "Client token": "", "Close chat": "", "Close message": "", "Cloud API numbers": "", "Cloud API settings": "", "Cloud API template fallback": "", "Code": "", "Collapse panels": "", "Color": "", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "", "Company": "", "Concurrent chats": "", "Configuration URL": "", "Confirm button text": "", "Confirmation message": "", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "", "Connect stores to agents.": "", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "", "Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.": "", "Content": "", "Content template SID": "", "Conversation profile": "", "Conversations data": "", "Convert all emails": "", "Cookie domain": "", "Country": "", "Coupon discount (%)": "", "Coupon expiration (days)": "", "Coupon expiration (seconds)": "", "Create a WordPress user upon registration.": "", "Create Intents now": "", "Currency symbol": "", "Custom CSS": "", "Custom fields": "", "Custom JS": "", "Custom model ID": "", "Custom parameters": "", "Customize the link for the 'All articles' button.": "", "Dashboard display": "", "Dashboard title": "", "Database details": "", "Database host": "", "Database name": "", "Database password": "", "Database prefix": "", "Database user": "", "Decline button text": "", "Declined message": "", "Default": "", "Default body text": "", "Default conversation name": "", "Default department": "", "Default department ID": "", "Default form": "", "Default header text": "", "Delay (ms)": "", "Delete all leads and all messages and conversations linked to them.": "", "Delete conversation": "", "Delete leads": "", "Delete message": "", "Delete the built-in flows.": "", "Delimiter": "", "Department": "", "Department ID": "", "Departments": "", "Departments settings": "", "Desktop notifications": "", "Dialogflow - Department linking": "", "Dialogflow chatbot": "", "Dialogflow edition": "", "Dialogflow Intent detection confidence": "", "Dialogflow location": "", "Dialogflow spelling correction": "", "Dialogflow welcome Intent": "", "Disable agents check": "", "Disable and hide the chat widget if all agents are offline.": "", "Disable and hide the chat widget outside of scheduled office hours.": "", "Disable any features that you don't need.": "", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "", "Disable chatbot": "", "Disable cron job": "", "Disable dashboard": "", "Disable during office hours": "", "Disable features": "", "Disable features you don't use and improve the chat performance.": "", "Disable file uploading capabilities within the chat.": "", "Disable for messaging channels": "", "Disable for the tickets area": "", "Disable invitation": "", "Disable online status check": "", "Disable outside of office hours": "", "Disable password": "", "Disable registration during office hours": "", "Disable registration if agents online": "", "Disable the automatic invitation of agents to the channels.": "", "Disable the channels filter.": "", "Disable the chatbot for the tickets area.": "", "Disable the chatbot for this channel only.": "", "Disable the dashboard, and allow only one conversation per user.": "", "Disable the login and remove the password field from the registration form.": "", "Disable uploads": "", "Disable voice message capabilities within the chat.": "", "Disable voice messages": "", "Disabled": "", "Display a brand image in the header area. This only applies for the 'brand' header type.": "", "Display categories": "", "Display images": "", "Display in conversation list": "", "Display in dashboard": "", "Display online agents only": "", "Display the articles section in the right area.": "", "Display the dashboard instead of the chat area on initialization.": "", "Display the feedback form to rate the conversation when it is archived.": "", "Display the user full name in the left panel instead of the conversation title.": "", "Display the user's profile image within the chat.": "", "Display user name in header": "", "Display user's profile image": "", "Displays additional columns in the user table. Enter the name of the fields to add.": "", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "", "Do not send email notifications to admins": "", "Do not show tickets in chat": "", "Do not translate settings area": "", "Download": "", "Edit profile": "", "Edit user": "", "Email address": "", "Email and ticket": "", "Email header": "", "Email notification delay (hours)": "", "Email notifications via cron job": "", "Email only": "", "Email piping": "", "Email piping server information and more settings.": "", "Email request message": "", "Email signature": "", "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "", "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "", "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "", "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.": "", "Email verification": "", "Email verification content": "", "Enable email verification with OTP.": "", "Enable logging of agent activity": "", "Enable logs": "", "Enable the chatbot outside of scheduled office hours only.": "", "Enable the registration only if all agents are offline.": "", "Enable the registration outside of scheduled office hours only.": "", "Enable this option if email notifications are sent via cron job.": "", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "", "Enter the database details of the Active eCommerce CMS database.": "", "Enter the database details of the Martfury database.": "", "Enter the database details of the Perfex database.": "", "Enter the database details of the WHMCS database.": "", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "", "Enter the details of your Google Business Messages.": "", "Enter the details of your Twitter app.": "", "Enter the LINE details to start using it. Localhost cannot receive messages.": "", "Enter the URL of a .css file, to load it automatically in the admin area.": "", "Enter the URL of a .js file, to load it automatically in the admin area.": "", "Enter the URL of the articles page.": "", "Enter the URLs of your shop": "", "Enter the WeChat official account token. See the docs for more details.": "", "Enter the Zalo details to start using it. Localhost cannot receive messages.": "", "Enter your 360dialog account settings information.": "", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "", "Enter your Twilio account settings information.": "", "Enter your WeChat Official Account information.": "", "Enter your Zendesk information.": "", "Entities": "", "Envato Purchase Code": "", "Envato purchase code validation": "", "Exclude products": "", "Export all settings.": "", "Export settings": "", "Facebook pages": "", "Fallback message": "", "Filters": "", "First chat message": "", "First reminder delay (hours)": "", "First ticket form": "", "Flash notifications": "", "Follow up - Email": "", "Follow up email": "", "Follow up message": "", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "", "Force language": "", "Force log out": "", "Force the chat to ignore the language preferences, and to use always the same language.": "", "Force the loggout of Support Board agents if they are not logged in WordPress.": "", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "", "Force users to use only one phone country code.": "", "Form message": "", "Form title": "", "Frequency penalty": "", "Full visitor details": "", "Function name": "", "Generate conversations data": "", "Generate user questions": "", "Get configuration URL": "", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "", "Get Path": "", "Get Service Worker path": "", "Get URL": "", "Google and Dialogflow settings.": "", "Google search": "", "Header": "", "Header background image": "", "Header brand image": "", "Header message": "", "Header title": "", "Header type": "", "Header variables": "", "Hide": "", "Hide agent's profile image": "", "Hide archived tickets": "", "Hide archived tickets from users.": "", "Hide chat if no agents online": "", "Hide chat outside of office hours": "", "Hide conversation details panel": "", "Hide conversations of other agents": "", "Hide on mobile": "", "Hide the agent's profile image within the chat.": "", "Hide tickets from the chat widget and chats from the ticket area.": "", "Hide timetable": "", "Host": "", "Human takeover": "", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "", "Image": "", "Import admins": "", "Import all settings.": "", "Import articles": "", "Import contacts": "", "Import customers": "", "Import customers into Support Board. Only new customers will be imported.": "", "Import settings": "", "Import users": "", "Import users from a CSV file.": "", "Import vendors": "", "Import vendors into Support Board as agents. Only new vendors will be imported.": "", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "", "Include custom fields": "", "Include custom fields in the registration form.": "", "Include the password field in the registration form.": "", "Incoming conversations and messages": "", "Incoming conversations only": "", "Incoming messages only": "", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Support Board.": "", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Support Board.": "", "Info message": "", "Initialize and display the chat widget and tickets only for members.": "", "Initialize and display the chat widget only when the user is logged in.": "", "Instance ID": "", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "", "Interval (sec)": "", "IP banning": "", "Label": "", "Language": "", "Language detection": "", "Language detection message": "", "Last name": "", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "", "Left": "", "Left panel": "", "Left profile image": "", "Let the bot to search on Google to find answers to user questions.": "", "Let the chatbot search on Google to find answers to user questions.": "", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "", "Link name": "", "Login form": "", "Login initialization": "", "Login verification URL": "", "Logit bias": "", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "", "Make the registration phone field mandatory.": "", "Manage": "", "Manage here the departments settings.": "", "Manage the tags settings.": "", "Manifest file URL": "", "Manual": "", "Manual initialization": "", "Martfury root directory path, e.g. /var/www/": "", "Martfury shop URL, e.g. https://shop.com": "", "Max message limit": "", "Max tokens": "", "Members only": "", "Members with an active paid plan only": "", "Message": "", "Message area": "", "Message rewrite button": "", "Message template": "", "Message type": "", "Messaging channels": "", "Messenger and Instagram settings": "", "Minify JS": "", "Minimal": "", "Model": "", "Multilingual": "", "Multilingual plugin": "", "Multilingual via translation": "", "Multlilingual training sources": "", "Name": "", "Namespace": "", "New conversation email": "", "New conversation notification": "", "New ticket button": "", "Newsletter": "", "No delay": "", "No results found.": "", "No, we don't ship in": "", "None": "", "Note data scraping": "", "Notes": "", "Notifications icon": "", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "", "OA secret key": "", "Offline message": "", "Offset": "", "On chat open": "", "On page load": "", "One conversation per agent": "", "One conversation per department": "", "Online users notification": "", "Only desktop": "", "Only general questions": "", "Only mobile devices": "", "Only questions related to your sources": "", "Open automatically": "", "Open chat": "", "Open the chat window automatically when a new message is received.": "", "OpenAI Assistants - Department linking": "", "OpenAI settings.": "", "Optional link": "", "Order webhook": "", "Other": "", "Outgoing SMTP server information.": "", "Page ID": "", "Page IDs": "", "Page name": "", "Page token": "", "Panel height": "", "Panel name": "", "Panel title": "", "Panels arrows": "", "Password": "", "Perfex URL": "", "Performance optimization": "", "Phone": "", "Phone number ID": "", "Phone required": "", "Place ID": "", "Placeholder text": "", "Play a sound for new messages and conversations.": "", "Popup message": "", "Port": "", "Post Type slugs": "", "Presence penalty": "", "Prevent admins from receiving email notifications.": "", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "", "Primary color": "", "Priority": "", "Privacy link": "", "Privacy message": "", "Private chat": "", "Private chat linking": "", "Private key": "", "Product IDs": "", "Product removed notification": "", "Product removed notification - Email": "", "Profile image": "", "Project ID": "", "Project ID or Agent Name": "", "Prompt": "", "Prompt - Message rewriting": "", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "", "Provider": "", "Purchase button text": "", "Push notifications": "", "Push notifications settings.": "", "Queue": "", "Rating": "", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "", "Reconnect": "", "Redirect the user to the registration link instead of showing the registration form.": "", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "", "Refresh token": "", "Register all visitors": "", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "", "Registration / Login": "", "Registration and login form": "", "Registration fields": "", "Registration form": "", "Registration link": "", "Registration redirect": "", "Rename the chat bot. Default is 'Bot'.": "", "Rename the visitor name prefix. Default is 'User'.": "", "Repeat": "", "Repeat - admin": "", "Replace the admin login page message.": "", "Replace the brand logo on the admin login page.": "", "Replace the header title with the user's first name and last name when available.": "", "Replace the top-left brand icon on the admin area and the browser favicon.": "", "Reply to user emails": "", "Reply to user text messages": "", "Reports": "", "Reports area": "", "Request a valid Envato purchase code for registration.": "", "Request the user to provide their email address and then send a confirmation email to the user.": "", "Require phone": "", "Require registration": "", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "", "Require the user registration or login in order to use the tickets area.": "", "Required": "", "Response time": "", "Restrict chat access by blocking IPs. List IPs with commas.": "", "Returning visitor message": "", "Rich messages": "", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "", "Right": "", "Right panel": "", "Routing": "", "Routing if offline": "", "RTL": "", "Save useful information like user country and language also for visitors.": "", "Saved replies": "", "Scheduled office hours": "", "Search engine ID": "", "Second chat message": "", "Second reminder delay (hours)": "", "Secondary color": "", "Secret key": "", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "", "Send a message to new users when they visit the website for the first time.": "", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "", "Send a message to the user when the agent archive the conversation.": "", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "", "Send a test agent notification email to verify email settings.": "", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "", "Send a test user notification email to verify email settings.": "", "Send a text message to the provided phone number.": "", "Send a user email notification": "", "Send a user text message notifcation": "", "Send a user text message notification": "", "Send an agent email notification": "", "Send an agent text message notification": "", "Send an agent user text notification": "", "Send an email notification to the provided email address.": "", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "", "Send an email to the user when a new conversation is created.": "", "Send an email to the user when a new conversation or ticket is created": "", "Send an email to the user when an agent replies and the user is offline.": "", "Send email": "", "Send login details to the specified URL and allow access only if the response is positive.": "", "Send message": "", "Send message to Slack": "", "Send message via enter button": "", "Send text message": "", "Send the message template to a WhatsApp number.": "", "Send the message via the ENTER keyboard button.": "", "Send the user details of the registration form and email rich messages to Dialogflow.": "", "Send the WhatsApp order details to the URL provided.": "", "Send to user's email": "", "Send transcript to user's email": "", "Send user details": "", "Sender": "", "Sender email": "", "Sender name": "", "Sender number": "", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "", "Service": "", "Service Worker path": "", "Service Worker URL": "", "Set a dedicated Dialogflow agent for each department.": "", "Set a dedicated OpenAI Assistants for each department.": "", "Set a dedicated Slack channel for each department.": "", "Set a profile image for the chat bot.": "", "Set the articles panel title. Default is 'Help Center'.": "", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": null, "Set the chat language or translate it automatically to match the user language. Default is English.": "", "Set the currency symbol of the membership prices.": "", "Set the currency symbol used by your system.": "", "Set the default departments for all tickets. Enter the department ID.": "", "Set the default email header that will be prepended to automated emails and direct emails.": "", "Set the default email signature that will be appended to automated emails and direct emails.": "", "Set the default form to display if the registraion is required.": "", "Set the default name to use for conversations without a name.": "", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "", "Set the header appearance.": "", "Set the maximum height of the tickets panel.": "", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "", "Set the position of the chat widget.": "", "Set the primary color of the admin area.": "", "Set the primary color of the chat widget.": "", "Set the secondary color of the admin area.": "", "Set the secondary color of the chat widget.": "", "Set the tertiary color of the chat widget.": "", "Set the title of the administration area.": "", "Set the title of the conversations panel.": "", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "", "Set which actions to allow agents.": "", "Set which actions to allow supervisors.": "", "Set which user details to send to the main channel. Add comma separated values.": "", "Settings area": "", "settings information": "", "Shop": "", "Show": "", "Show a browser tab notification when a new message is received.": "", "Show a desktop notification when a new message is received.": "", "Show a notification and play a sound when a new user is online.": "", "Show a pop-up notification to all users.": "", "Show profile images": "", "Show sender's name": "", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "", "Show the articles panel on the chat dashboard.": "", "Show the categories instead of the articles list.": "", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "", "Show the list of all Slack channels.": "", "Show the profile image of agents and users within the conversation.": "", "Show the sender's name in every message.": "", "Single label": "", "Single phone country code": "", "Site key": "", "Slug": "", "Smart Reply": "", "Social share message": "", "Sort conversations by date": "", "Sound": "", "Sound settings": "", "Sounds": "", "Sounds - admin": "", "Source links": "", "Speech recognition": "", "Spelling correction": "", "Starred tag": "", "Start importing": "", "Store name": "", "Subject": "", "Subscribe": "", "Subscribe users to your preferred newsletter service when they provide an email.": "", "Subtract the offset value from the height value.": "", "Success message": "", "Supervisors": "", "Support Board path": "", "Sync admin and staff accounts with Support Board. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "", "Sync all contacts of all clients with Support Board. Only new contacts will be imported.": "", "Sync all users with Support Board. Only new users will be imported.": "", "Sync all WordPress users with Support Board. Only new users will be imported.": "", "Sync knowledge base articles with Support Board. Only new articles will be imported.": "", "Sync mode": "", "Synchronization": "", "Synchronize": "", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "", "Synchronize emails": "", "Synchronize Entities": "", "Synchronize Entities now": "", "Synchronize now": "", "Synchronize users": "", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "", "Synchronize your Messenger and Instagram accounts.": "", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "", "Synchronize your WhatsApp Cloud API account.": "", "System requirements": "", "Tags": "", "Tags settings": "", "Template default language": "", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "", "Template languages": "", "Template name": "", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "", "Terms link": "", "Tertiary color": "", "Test Slack": "", "Test template": "", "Text": "", "Text message fallback": "", "Text message notifications": "", "Text messages": "", "The product is not in the cart.": "", "The workspace name you are using to synchronize Slack.": "", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "", "This returns the Support Board path of your server.": "", "This returns your Support Board URL.": "", "Ticket custom fields": "", "Ticket email": "", "Ticket field names": "", "Ticket fields": "", "Ticket only": "", "Ticket products selector": "", "Title": "", "Top": "", "Top bar": "", "Training via cron job": "", "Transcript": "", "Transcript settings.": "", "Trigger": "", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "", "Troubleshoot": "", "Troubleshoot problems": "", "Twilio settings": "", "Twilio template": "", "Unsubscribe": "", "Upload attachments to Amazon S3.": "", "Usage Limit": "", "Use this option to change the PWA icon. See the docs for more details.": "", "User details": "", "User details in success message": "", "User email notifications": "", "User login form information.": "", "User message template": "", "User name as title": "", "User notification email": "", "User registration form information.": "", "User roles": "", "User system": "", "Username": "", "Users and agents": "", "Users area": "", "Users only": "", "Users table additional columns": "", "UTC offset": "", "Variables": "", "View channels": "", "View unassigned conversations": "", "Visibility": "", "Visitor default name": "", "Visitor name prefix": "", "Volume": "", "Volume - admin": "", "Waiting list": "", "Waiting list - Email": "", "Webhook URL": "", "Webhooks": "", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "", "Website": "", "WeChat settings": "", "Welcome message": "", "Whmcs admin URL": "", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "", "WordPress registration": "", "Yes, we ship in": "", "You haven't placed an order yet.": "", "You will get this code by completing the Dialogflow synchronization.": "", "You will get this code by completing the Slack synchronization.": "", "You will get this information by completing the synchronization.": "", "Your cart is empty.": "", "Your turn message": "", "Your username": "", "Your WhatsApp catalogue details.": "", "Zendesk settings": ""}