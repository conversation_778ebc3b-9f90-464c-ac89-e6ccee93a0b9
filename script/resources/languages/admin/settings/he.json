{"{product_name} has no {product_attribute_name} variants.": "ל-{product_name} אין גרסאות של {product_attribute_name}.", "360dialog settings": "הגדרות דיאלוג 360", "360dialog template": "תבנית דיאלוג 360", "Abandoned cart notification": "הודעה על עגלה נטושה", "Abandoned cart notification - Admin email": "הודעה על עגלה נטושה - דוא&quot;ל מנהל", "Abandoned cart notification - First email": "הודעה על עגלה נטושה - אימייל ראשון", "Abandoned cart notification - Second email": "הודעה על עגלה נטושה - אימייל שני", "Accept button text": "קבל את טקסט הלחצן", "Account SID": "SID חשבון", "Activate the Slack integration.": "הפעל את שילוב <PERSON>.", "Activate the Zendesk integration": "הפעל את שילוב Zendesk", "Activate this option if you don't want to translate the settings area.": "הפעל אפשרות זו אם אינך רוצה לתרגם את אזור ההגדרות.", "Active": "פָּעִיל", "Active - admin": "פעיל - <PERSON><PERSON><PERSON><PERSON>ן", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce כתובת URL של CMS. לְשֶׁעָבַר. https://shop.com/", "Active eCommerce URL": "Active eCommerce כתובת אתר", "Active for agents": "פעיל עבור סוכנים", "Active for users": "פעיל עבור משתמשים", "Active webhooks": "Webhooks פעילים", "Add a delay (ms) to the bot's responses. Default is 2000.": "הוסף עיכוב (ms) לתגובות הבוט. ברירת המחדל היא 2000.", "Add and manage additional support departments.": "הוסף ונהל מחלקות תמיכה נוספות.", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "הוסף ונהל תשובות שמורות שיכולות לשמש סוכנים בעורך הצ&#39;אט. ניתן להדפיס תשובות שמורות על ידי הקלדת # ואחריו שם התשובה בתוספת רווח. השתמש ב-\\n כדי לבצע מעבר שורה.", "Add and manage tags.": "הוסף ונהל תגים.", "Add comma separated WordPress user roles. The Support Board administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "הוסף תפקידי משתמש וורדפרס מופרדים בפסיקים. אזור הניהול של מועצת התמיכה יהיה זמין לתפקידים חדשים, בנוסף לתפקיד ברירת המחדל: עורך, מנהל, מחבר.", "Add custom fields to the new ticket form.": "הוסף שדות מותאמים אישית לטופס הכרטיס החדש.", "Add custom fields to the user profile details.": "הוסף שדות מותאמים אישית לפרטי פרופיל המשתמש.", "Add Intents": "הוסף Intents", "Add Intents to saved replies": "הוסף Intents לתשובות שמורות", "Add WhatsApp phone number details here.": "הוסף כאן פרטי מספר טלפון של WhatsApp.", "Adjust the chat button position. Values are in px.": "התאם את מיקום כפתור הצ&#39;אט. הערכים הם בפיקסלים.", "Admin icon": "סמל מנהל מערכת", "Admin IDs": "מזהי מנהל", "Admin login logo": "לוגו התחברות של מנהל מערכת", "Admin login message": "הודעת התחברות למנהל מערכת", "Admin notifications": "הודעות מנהל", "Admin title": "כותרת מנהל", "Agent area": "אזור סוכנים", "Agent details": "פרטי הסוכן", "Agent email notifications": "הודעות דוא&quot;ל של סוכן", "Agent ID": "מזהה סוכן", "Agent linking": "<PERSON><PERSON><PERSON><PERSON>", "Agent message template": "תבנית הודעת סוכן", "Agent notification email": "אימייל התראה לסוכן", "Agent privileges": "הרש<PERSON><PERSON>ת סוכן", "Agents": "סוכנים", "Agents and admins tab": "כרטיסיית סוכנים ומנהלים", "Agents menu": "תפריט סוכנים", "Agents only": "סוכנים בלבד", "All": "את כל", "All channels": "כל הערוצים", "All messages": "כל ההודעות", "All questions": "כל השאלות", "Allow only extended licenses": "אפשר רק רישיונות מורחבים", "Allow only one conversation": "אפשר רק שיחה אחת", "Allow only one conversation per user.": "אפשר רק שיחה אחת לכל משתמש.", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "א<PERSON><PERSON><PERSON> לצ&#39;אט<PERSON><PERSON><PERSON> להשיב לאימיילים של המשתמש אם התשובה ידועה וצנרת האימייל פעילה.", "Allow the chatbot to reply to the user's text messages if the answer is known.": "א<PERSON><PERSON><PERSON> לצ&#39;אט<PERSON><PERSON><PERSON> להשיב להודעות הטקסט של המשתמש אם התשובה ידועה.", "Allow the user to archive a conversation and hide archived conversations.": "אפשר למשת<PERSON>ש להעביר שיחה לארכיון ולהסתיר שיחות בארכיון.", "Allow users to contact you via their favorite messaging apps.": "אפשר למשתמשים ליצור איתך קשר באמצעות אפליקציות ההודעות המועדפות עליהם.", "Allow users to select a product on ticket creation.": "אפשר למשתמשים לבחור מוצר ביצירת כרטיסים.", "Always all messages": "תמיד כל ההודעות", "Always incoming messages only": "הודעות נכנסות תמיד בלבד", "Always sort conversations by date in the admin area.": "תמיד מיין שיחות לפי תאריך באזור הניהול.", "API key": "מפתח API", "Append the registration user details to the success message.": "הוסף את פרטי המשתמש בהרשמה להודעת ההצלחה.", "Apply a custom background image for the header area.": "החל תמונת רקע מותאמת אישית עבור אזור הכותרת.", "Apply changes": "החל שינויים", "Apply to": "הגשת בקשה ל", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "שמור את כל ערוצי המשתמש בארכיון באפליקציית Slack. פעולה זו עשויה להימשך זמן רב. חשוב: כל הערוצים הרפויים שלך יאוחסנו בארכיון.", "Archive automatically the conversations marked as read every 24h.": "אר<PERSON><PERSON><PERSON><PERSON> אוטומטית את השיחות שסומנו כנקראו כל 24 שעות.", "Archive channels": "אר<PERSON><PERSON><PERSON>ן ערוצי ארכיון", "Archive channels now": "ארכיון ערוצים עכשיו", "Articles": "מאמרים", "Articles area": "אזור מאמרים", "Articles button link": "קישור לחצן מאמרים", "Articles page URL": "כתובת האתר של דף המאמרים", "Artificial Intelligence": "בינה מלאכותית", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "הקצה מחלקה לכל השיחות שהתחילו מ-Google Business Messages. הזן את מזהה המחלקה.", "Assign a department to all conversations started from Twitter. Enter the department ID.": "הקצה מחלקה לכל השיחות שהתחילו מטוויטר. הזן את מזהה המחלקה.", "Assign a department to all conversations started from Viber. Enter the department ID.": "הקצה מחלקה לכל השיחות שהתחילו מ-Viber. הזן את מזהה המחלקה.", "Assign a department to all conversations started from WeChat. Enter the department ID.": "הקצה מחלקה לכל השיחות שהתחילו מ-WeChat. הזן את מזהה המחלקה.", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "הקצה מחלקות שונות לשיחות שהתחילו ממיקומים שונים של Google Business Messages. הגדרה זו עוקפת את מחלקת ברירת המחדל.", "Assistant": "עוֹזֵר", "Assistant ID": "מזהה עוזר", "Attachments list": "רשימת קבצים מצורפים", "Audio file URL - admin": "כתובת אתר של קובץ שמע - אדמין", "Automatic": "אוֹטוֹמָטִי", "Automatic human takeover": "השתלטות אנושית אוטומטית", "Automatic translation": "תרגום אוטומטי", "Automatic updates": "עדכונים אוטומטיים", "Automatically archive conversations": "העבר שיחות לארכיון באופן אוטומטי", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "מקצה אוטומטית מחלקה על סמך התוכניות הפעילות של המשתמש. הוסף -1 כמזהה תוכנית עבור משתמשים ללא כל תוכנית.", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "בד<PERSON><PERSON> והת<PERSON>ן עדכונים חדשים באו<PERSON>ן אוטומטי. נדרשים קוד רכישה חוקי של Envato ומפתחות רישיון תקפים של אפליקציות.", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "כיווץ אוטומטי של חלונית פרטי השיחה ופאנלים אחרים של אזור הניהול.", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "צור אוטומטית מחלקה לכל אתר ונתב את השיחות של כל אתר למחלקה הנכונה. הגדרה זו דורשת התקנת וורדפרס מרובי אתרים.", "Automatically hide the conversation details panel.": "הסתר אוטומטית את חלונית פרטי השיחה.", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "שלח באו<PERSON>ן אוטומטי תזכורות לעגלה ללקוחות עם מוצרים בעגלותיהם. אתה יכול להשתמש בשדות המיזוג הבאים ועוד: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "סנכרן אוטומטית לקוחות Zendesk עם {R}, הצג כרטיסים של Zendesk או צור כרטיסים חדשים מבלי לעזוב את {R}.", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "סנכרן אוטומטית מוצרים, קטג<PERSON>ריות, תגים ועוד עם Dialogflow, ואפשר לבוט לענות באופן אוטונומי על שאלות הקשורות לחנות שלך.", "Automatically translate admin area": "תרגם אוטומטית את אזור הניהול", "Automatically translate the admin area to match the agent profile language or browser language.": "תרגם אוטומטית את אזור הניהול כך שיתאים לשפת פרופיל הסוכן או לשפת הדפדפן.", "Avatar image": "תמונת אווטאר", "Away mode": "מצ<PERSON> רחוק", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "לפני תחילת הצ&#39;אט, על המשתמש לקבל הודעת פרטיות על מנת לקבל גישה.", "Birthday": "יום הולדת", "Body variables": "משתני גוף", "Bot name": "שם הבוט", "Bot profile image": "תמונת פרופיל בוט", "Bot response delay": "עיכוב בתגובת הבוט", "Bottom": "תַחתִית", "Brand": "מותג", "Built-in chat button icons": "סמלי כפתור צ&#39;אט מובנים", "Business Account ID": "מזהה חשבון עסקי", "Button action": "פעולת כפתור", "Button name": "שם הכפתור", "Button text": "טקסט לחצן", "Button variables": "משת<PERSON>י כפתור", "Cancel button text": "טקסט לחצן ביטול", "Cart": "עֲגָלָה", "Cart follow up message": "הודעת מעקב עגלה", "Catalogue details": "פרטי קטלוג", "Catalogue ID": "מזהה קטלוג", "Change the chat button image with a custom one.": "שנה את תמונת כפתור הצ&#39;אט עם תמונה מותאמת אישית.", "Change the default field names.": "שנה את שמות השדות המוגדרים כברירת מחדל.", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "שנה את טקסט ההודעה באזור הכותרת של ווידג&#39;ט הצ&#39;אט. טקסט זה יוחלף בכותרת הסוכן לאחר שליחת התשובה הראשונה.", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "שנה את טקסט הכותרת באזור הכותרת של ווידג&#39;ט הצ&#39;אט. טקסט זה יוחלף בשם הסוכן לאחר שליחת התשובה הראשונה.", "Channel ID": "מזהה ערוץ", "Channels": "ערוצים", "Channels filter": "מסנן ערוצים", "Chat": "לְשׂוֹחֵחַ", "Chat and admin": "צ&#39;אט ומנהל", "Chat background": "רקע צ&#39;אט", "Chat button icon": "סמל לחצן צ&#39;אט", "Chat button offset": "היסט לחצן צ&#39;אט", "Chat message": "הודעת צ&#39;אט", "Chat only": "צ&#39;אט בלבד", "Chat position": "עמדת צ&#39;אט", "Chatbot": "צ&#39;אטבוט", "Chatbot mode": "מצב צ&#39;אטבוט", "Check Requirements": "ב<PERSON><PERSON><PERSON> דרישות", "Check the server configurations and make sure it has all the requirements.": "בדוק את תצורות השרת וודא שיש לו את כל הדרישות.", "Checkout": "לבדוק", "Choose a background texture for the chat header and conversation area.": "בחר מרקם רקע עבור כותרת הצ&#39;אט ואזור השיחה.", "Choose where to display the chat. Enter the values separated by commas.": "בחר <PERSON>י<PERSON><PERSON> להציג את הצ&#39;אט. הזן את הערכים מופרדים בפסיקים.", "Choose which fields to disable from the tickets area.": "בחר אילו שדות להשבית מאזור הכרטיסים.", "Choose which fields to include in the new ticket form.": "בחר אילו שדות לכלול בטופס הכרטיס החדש.", "Choose which fields to include in the registration form. The name field is included by default.": "בחר אילו שדות לכלול בטופס ההרשמה. שדה השם נכלל כברירת מחדל.", "Choose which user system the front-end chat will use to register and log in users.": "בחר באיזו מערכת משתמש הצ&#39;אט הקדמי ישתמש כדי להירשם ולהתחבר למשתמשים.", "City": "עִיר", "Clear flows": "זרימות ברורות", "Click the button to start the Dialogflow synchronization.": "לחץ על הלחצן כדי להתחיל את הסנכרון של Dialogflow.", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "לחץ על הכפתור כדי להתחיל את סנכרון Slack. Localhost לא יכול ולא מקבל הודעות. התחבר עם חשבון אחר או כמבקר כדי לבצע את הבדיקות שלך.", "Client email": "מייל לקוח", "Client ID": "מזהה לקוח", "Client token": "אסימון לקוח", "Close chat": "סגור צ&#39;אט", "Close message": "סגור הודעה", "Cloud API numbers": "מספרי Cloud API", "Cloud API settings": "הגדרות Cloud API", "Cloud API template fallback": "תבנית חזרה של Cloud API", "Code": "קוד", "Collapse panels": "כווץ לוחות", "Color": "צֶבַע", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "תקשר עם המשתמשים שלך ישירות מ-Slack. שלח וקבל הודעות וקבצים מצורפים, השתמש באימוג&#39;י ועוד הרבה יותר.", "Company": "חֶברָה", "Concurrent chats": "חתולים מתחרים", "Configuration URL": "כתובת אתר לתצורה", "Confirm button text": "אשר את טק<PERSON>ט הלחצן", "Confirmation message": "הודעת אישור", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "חבר צ&#39;אטבוטים חכמים והפוך שיחות לאוטומטיות על ידי שימוש באחת הצורות המתקדמות ביותר של בינה מלאכותית בעולם.", "Connect stores to agents.": "<PERSON><PERSON><PERSON> חנויות לסוכנים.", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "חבר את בוט הטלגרם שלך ל-{R} כדי לקרוא ולהשיב לכל ההודעות שנשלחות לבוט הטלגרם שלך ישירות ב-{R}.", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "חבר את בוט ה-Viber שלך אל {R} כדי לקרוא ולהשיב לכל ההודעות שנשלחות לבוט ה-Viber שלך ישירות ב-{R}.", "Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.": "חבר את החשבון הרשמי של Zalo אל {R} כדי לקרוא ולהשיב לכל ההודעות שנשלחו לחשבון הרשמי של Zalo ישירות ב-{R}.", "Content": "תוֹכֶן", "Content template SID": "SID של תבנית תוכן", "Conversation profile": "פרופיל שיחה", "Conversations data": "נתוני שיחות", "Convert all emails": "המר את כל המיילים", "Cookie domain": "תחום קובצי <PERSON>", "Country": "מדינה", "Coupon discount (%)": "הנ<PERSON><PERSON> קופון (%)", "Coupon expiration (days)": "תוקף הקופון (ימים)", "Coupon expiration (seconds)": "תפוגת קופון (שניות)", "Create a WordPress user upon registration.": "צור משתמש וורדפרס עם ההרשמה.", "Create Intents now": "צור Intents עכשיו", "Currency symbol": "סמל המטבע", "Custom CSS": "CSS מותאם אישית", "Custom fields": "שדות מותאמים אישית", "Custom JS": "JS מותאם אישית", "Custom model ID": "מזהה דגם מותאם אישית", "Custom parameters": "פרמטרים מותאמים אישית", "Customize the link for the 'All articles' button.": "התאם אישית את הקישור ללחצן &#39;כל המאמרים&#39;.", "Dashboard display": "תצוגת לוח מחוונים", "Dashboard title": "כותרת לוח המחוונים", "Database details": "פרטי מאגר מידע", "Database host": "מארח מסד נתונים", "Database name": "שם בסיס הנתונים", "Database password": "סיסמת מסד נתונים", "Database prefix": "קידומת מסד נתונים", "Database user": "משת<PERSON><PERSON> במסד נתונים", "Decline button text": "<PERSON><PERSON><PERSON> טקסט של לחצן", "Declined message": "הודעה נדחתה", "Default": "בְּרִירַת מֶחדָל", "Default body text": "טקסט גוף ברירת מחדל", "Default conversation name": "שם ברירת מחדל לשיחה", "Default department": "מחלקת ברירת מחדל", "Default department ID": "מזהה מחלקה ברירת מחדל", "Default form": "טופס ברירת מחדל", "Default header text": "טקסט כותרת ברירת מחדל", "Delay (ms)": "השהיה (ms)", "Delete all leads and all messages and conversations linked to them.": "מחק את כל הלידים ואת כל ההודעות והשיחות המקושרות אליהם.", "Delete conversation": "<PERSON>ח<PERSON> שיחה", "Delete leads": "<PERSON><PERSON><PERSON> לידים", "Delete message": "למחו<PERSON> הודעה", "Delete the built-in flows.": "מחק את הזרימות המובנות.", "Delimiter": "תוחם", "Department": "מַחלָקָה", "Department ID": "מזהה מחלקה", "Departments": "מחלקות", "Departments settings": "הגדרות מחלקות", "Desktop notifications": "הודעות על שולחן העבודה", "Dialogflow - Department linking": "Dialogflow - קישור מחלקות", "Dialogflow chatbot": "צ&#39;אטבוט של Dialogflow", "Dialogflow edition": "מהדורת Dialogflow", "Dialogflow Intent detection confidence": "ביטחון זיהוי כוונות Dialogflow", "Dialogflow location": "מיק<PERSON><PERSON> דיאלוג", "Dialogflow spelling correction": "תיקון איות של Dialogflow", "Dialogflow welcome Intent": "כוונות ברוך הבא של Dialogflow", "Disable agents check": "השבת בדיקת סוכנים", "Disable and hide the chat widget if all agents are offline.": "השבת והסתיר את ווידג&#39;ט הצ&#39;אט אם כל הסוכנים אינם מקוונים.", "Disable and hide the chat widget outside of scheduled office hours.": "השבת והסתיר את ווידג&#39;ט הצ&#39;אט מחוץ לשעות העבודה המתוכננות.", "Disable any features that you don't need.": "השבת את כל התכונות שאינך צריך.", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "השבת אתחול האוטומטי של ווידג&#39;ט הצ&#39;אט. כאשר הגדרה זו פעילה, עליך לאתחל את ווידג&#39;ט הצ&#39;אט עם קוד JavaScript API מותאם אישית שנכתב על ידך. אם הצ&#39;אט לא מופיע וההגדרה הזו מופעלת, השבת אותה.", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "השבת אתחול אוטומטי של אזור הכרטיסים. כאשר הגדרה זו פעילה, עליך לאתחל את אזור הכרטיסים עם קוד JavaScript API מותאם אישית שנכתב על ידך. אם אזור הכרטיסים לא מופיע וההגדרה הזו מופעלת, השבת אותה.", "Disable chatbot": "השבת צ&#39;אטבוט", "Disable cron job": "השבת את עבודת הקרון", "Disable dashboard": "השבת את לוח המחוונים", "Disable during office hours": "השבתה במהלך שעות העבודה", "Disable features": "השבת תכונות", "Disable features you don't use and improve the chat performance.": "השבת תכונות שאינך משתמש בהן ושפר את ביצועי הצ&#39;אט.", "Disable file uploading capabilities within the chat.": "השבת את יכולות העלאת הקבצים בתוך הצ&#39;אט.", "Disable for messaging channels": "השבת עבור ערוצי הודעות", "Disable for the tickets area": "השבת עבור אזור הכרטיסים", "Disable invitation": "השבת את ההזמנה", "Disable online status check": "השבת את בדיקת המצב המקוון", "Disable outside of office hours": "השבת מחוץ לשעות העבודה", "Disable password": "השבת את הסיסמה", "Disable registration during office hours": "השבת את הרישום בשעות המשרד", "Disable registration if agents online": "השבת את הרישום אם סוכנים מקוונים", "Disable the automatic invitation of agents to the channels.": "השבת את ההזמנה האוטומטית של סוכנים לערוצים.", "Disable the channels filter.": "השבת את מסנן הערוצים.", "Disable the chatbot for the tickets area.": "השבת את הצ&#39;אטבוט עבור אזור הכרטיסים.", "Disable the chatbot for this channel only.": "השבת את הצ&#39;אט בוט עבור ערוץ זה בלבד.", "Disable the dashboard, and allow only one conversation per user.": "השבת את לוח המחוונים ואפשר שיחה אחת בלבד לכל משתמש.", "Disable the login and remove the password field from the registration form.": "השבת את הכניסה והסר את שדה הסיסמה מטופס ההרשמה.", "Disable uploads": "השבת העלאות", "Disable voice message capabilities within the chat.": "השבת את יכולות ההודעות הקוליות בצ&#39;אט.", "Disable voice messages": "השבת הודעות קוליות", "Disabled": "נָכֶה", "Display a brand image in the header area. This only applies for the 'brand' header type.": "הצג תמונת מותג באזור הכותרת. זה חל רק על סוג הכותרת &#39;מותג&#39;.", "Display categories": "הצג קטגוריות", "Display images": "הצג תמונות", "Display in conversation list": "הצג ברשימת השיחות", "Display in dashboard": "תצוגה בלוח המחוונים", "Display online agents only": "הצג סוכנים מקוונים בלבד", "Display the articles section in the right area.": "הצג את מדור המאמרים באזור הימני.", "Display the dashboard instead of the chat area on initialization.": "הצג את לוח המחוונים במקום את אזור הצ&#39;אט בעת האתחול.", "Display the feedback form to rate the conversation when it is archived.": "הצג את טופס המשוב כדי לדרג את השיחה כשהיא מאוחסנת בארכיון.", "Display the user full name in the left panel instead of the conversation title.": "הצג את השם המלא של המשתמש בחלונית השמאלית במקום את כותרת השיחה.", "Display the user's profile image within the chat.": "הצג את תמונת הפרופיל של המשתמש בצ&#39;אט.", "Display user name in header": "הצג שם משתמש בכותרת", "Display user's profile image": "הצג את תמונת הפרופיל של המשתמש", "Displays additional columns in the user table. Enter the name of the fields to add.": "מציג עמודות נוספות בטבלת המשתמש. הזן את שם השדות להוספה.", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "הפזר שיחות באופן פרופורציונלי בין סוכנים והודיע למבקרים על מיקומם בתור. זמן התגובה הוא בדקות. אתה יכול להשתמש בשדות המיזוג הבאים בהודעה: {עמדה}, {דקות}. הם יוחלפו בערכים האמיתיים בזמן אמת.", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "הפזר שיחות באו<PERSON>ן פרופורציונלי בין סוכנים, וחסום סוכן לצפות בשיחות של הסוכנים האחרים.", "Do not send email notifications to admins": "אל תשלח הודעות אימייל למנהלים", "Do not show tickets in chat": "אל תציג כרטיסים בצ&#39;אט", "Do not translate settings area": "אל תתרגם אזור הגדרות", "Download": "הורד", "Edit profile": "ערוך פרופיל", "Edit user": "ערוך משתמש", "Email address": "כתובת דוא&quot;ל", "Email and ticket": "מייל וכרטיס", "Email header": "כותרת אימייל", "Email notification delay (hours)": "עיכוב בהודעת אימייל (שעות)", "Email notifications via cron job": "הודעות אימייל באמצעות cron job", "Email only": "מייל בלבד", "Email piping": "צנרת אימייל", "Email piping server information and more settings.": "מידע על שרת צנרת דוא&quot;ל והגדרות נוספות.", "Email request message": "הודעת בקשה למייל", "Email signature": "ח<PERSON>י<PERSON><PERSON> במייל", "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "תבנית דוא&quot;ל עבור האימייל שנשלח למשתמש כאשר סוכן משיב. אתה יכול להשתמש בטקסט, ב-HTML ובשדות המיזוג הבאים: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "תבנית דוא&quot;ל עבור האימייל שנשלח לסוכן כאשר משתמש שולח הודעה חדשה. אתה יכול להשתמש בטקסט, HTML ובשדות המיזוג הבאים: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "תבנית דוא&quot;ל עבור האימייל שנשלח למשתמש לאחר שליחת הדוא&quot;ל שלו באמצעות טופס הודעת ההמשך. אתה יכול להשתמש בטקסט, ב-HTML ובשדות המיזוג הבאים: {user_name}, {user_email}.", "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.": "תבנית דוא&quot;ל עבור האימייל שנשלח למשתמש כדי לאמת את כתובת הדוא&quot;ל שלו. כלול את שדה המיזוג {code} בתוך התוכן שלך, הוא יוחלף בקוד החד-פעמי.", "Email verification": "אימות דוא&quot;ל", "Email verification content": "תוכן אימות בדוא&quot;ל", "Enable email verification with OTP.": "אפשר אימות דוא&quot;ל באמצעות OTP.", "Enable logging of agent activity": "אפשר רישום של פעילות סוכן", "Enable the chatbot outside of scheduled office hours only.": "הפעל את הצ&#39;אט בוט מחוץ לשעות העבודה המתוכננות בלבד.", "Enable the registration only if all agents are offline.": "אפשר את הרישום רק אם כל הסוכנים אינם מקוונים.", "Enable the registration outside of scheduled office hours only.": "אפשר את ההרשמה מחוץ לשעות העבודה המתוכננות בלבד.", "Enable this option if email notifications are sent via cron job.": "הפעל אפשרות זו אם הודעות דוא&quot;ל נשלחות באמצעות cron job.", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "אפשר תמיכה בכרטיסים וצ&#39;אט למנויים בלבד, הצג את פרטי פרופיל החברים ופרטי המנוי באזור הניהול.", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "הזן את אסימון הבוט ולחץ על הכפתור כדי לסנכרן את בוט הטלגרם. Localhost לא יכול לקבל הודעות.", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "הזן את אסימון הבוט ולחץ על הכפתור כדי לסנכרן את הבוט Viber. Localhost לא יכול לקבל הודעות.", "Enter the database details of the Active eCommerce CMS database.": "הזן את פרטי מסד הנתונים של מסד הנתונים של Active eCommerce CMS.", "Enter the database details of the Martfury database.": "הזן את פרטי מסד הנתונים של מסד הנתונים של Martfury.", "Enter the database details of the Perfex database.": "הזן את פרטי מסד הנתונים של מסד הנתונים של פרפקס.", "Enter the database details of the WHMCS database.": "הזן את פרטי מסד הנתונים של מסד הנתונים של WHMCS.", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "הזן את הודעות ברירת המחדל המשמשות את הצ&#39;אט בוט כאשר שאלת המשתמש דורשת תשובה דינמית.", "Enter the details of your Google Business Messages.": "הזן את הפרטים של הודעות Google העסקיות שלך.", "Enter the details of your Twitter app.": "הזן את הפרטים של אפליקציית הטוויטר שלך.", "Enter the LINE details to start using it. Localhost cannot receive messages.": "הזן את הפרטים LINE כדי להתחיל להשתמש בו. Localhost לא יכול לקבל הודעות.", "Enter the URL of a .css file, to load it automatically in the admin area.": "הזן את כתובת האתר של קובץ .css, כדי לטעון אותו אוטומטית באזור הניהול.", "Enter the URL of a .js file, to load it automatically in the admin area.": "הזן את כתובת האתר של קובץ ‎.js, כדי לטעון אותו אוטומטית באזור הניהול.", "Enter the URL of the articles page.": "הזן את כתובת האתר של דף המאמרים.", "Enter the URLs of your shop": "הזן את כתובות האתרים של החנות שלך", "Enter the WeChat official account token. See the docs for more details.": "הזן את אסימון החשבון הרשמי של WeChat. עיין במסמכים לפרטים נוספים.", "Enter the Zalo details to start using it. Localhost cannot receive messages.": "הזן את פרטי Zalo כדי להתחיל להשתמש בו. Localhost לא יכול לקבל הודעות.", "Enter your 360dialog account settings information.": "הזן את פרטי הגדרות חשבון 360dialog שלך.", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "הזן את קוד הרכישה שלך ב-<PERSON><PERSON><PERSON> כדי להפעיל עדכונים אוטומטיים ולבטל את הנעילה של כל התכונות.", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "הזן את פרטי חשבון Twilio שלך. אתה יכול להשתמש בטקסט ובשדות המיזוג הבאים: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.", "Enter your Twilio account settings information.": "הזן את פרטי הגדרות חשבון Twilio שלך.", "Enter your WeChat Official Account information.": "הזן את פרטי החשבון הרשמי שלך ב-WeChat.", "Enter your Zendesk information.": "הזן את פרטי ה-Zendesk שלך.", "Entities": "Entities", "Envato Purchase Code": "קוד רכישה של Envato", "Envato purchase code validation": "אימות קוד רכישה של Envato", "Exclude products": "אל תכלול מוצרים", "Export all settings.": "ייצא את כל ההגדרות.", "Export settings": "ייצוא הגדרות", "Facebook pages": "<PERSON><PERSON><PERSON> פייס<PERSON><PERSON>ק", "Fallback message": "הודעת סתירה", "Filters": "מסננים", "First chat message": "הודעת צ&#39;אט ראשונה", "First reminder delay (hours)": "עיכוב תזכורת ראשונה (שעות)", "First ticket form": "טופס כרטיס ראשון", "Flash notifications": "התראות פלאש", "Follow up - Email": "מעקב - אימייל", "Follow up email": "דוא&quot;ל מעקב", "Follow up message": "הודעת המשך", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "עוקב אחר שיחה בין סוכן אנושי למשתמש קצה ומספק הצעות תגובה לסוכן האנושי בזמן אמת.", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "תבנית דוא&quot;ל מעקב. אתה יכול להשתמש בטקסט, ב-HTML ובשדות המיזוג הבאים ועוד: {coupon}, {product_names}, {user_name}.", "Force language": "לכפות שפה", "Force log out": "כ<PERSON>ה יציאה", "Force the chat to ignore the language preferences, and to use always the same language.": "הכריח את הצ&#39;אט להתעלם מהעדפות השפה, ולהשתמש תמיד באותה שפה.", "Force the loggout of Support Board agents if they are not logged in WordPress.": "כפה את ההתנתקות של סוכנים Support Board אם הם לא מחוברים ב-WordPress.", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "לאלץ משתמשים להשתמש בשיחה שונה עבור כל חנות ולהסתיר שיחות מחנויות אחרות ממנהלי חנות.", "Force users to use only one phone country code.": "כפה על משתמשים להשתמש רק בקוד מדינה אחד של הטלפון.", "Form message": "הודעת טופס", "Form title": "כותרת הטופס", "Frequency penalty": "עונש תדירות", "Full visitor details": "פרטי המבקרים המלאים", "Function name": "שם הפונקציה", "Generate conversations data": "הפקת נתוני שיחות", "Generate user questions": "צור שאלות משתמש", "Get configuration URL": "קבל כתובת URL לתצורה", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "קבל אותו מהערך APP_KEY של הקובץ .env שנמצא בספריית הבסיס של Active eCommerce.", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "קבל אותו מהערך APP_KEY של הקובץ .env שנמצא בספריית השורש של Martfury.", "Get Path": "קבל את נתיב", "Get Service Worker path": "ק<PERSON><PERSON> נתיב Service Worker", "Get URL": "קבל כתובת URL", "Google and Dialogflow settings.": "הגדרות Google ו-Dialogflow.", "Google search": "<PERSON><PERSON><PERSON><PERSON><PERSON> בגוגל", "Header": "כּוֹתֶרֶת", "Header background image": "תמונת רקע של כותרת", "Header brand image": "תמונת מותג כותרת", "Header message": "הודעת כותרת", "Header title": "כותרת הכותרת", "Header type": "סוג כותרת", "Header variables": "משת<PERSON>י כותרת", "Hide": "להתחבא", "Hide agent's profile image": "הסתר את תמונת הפרופיל של הסוכן", "Hide archived tickets": "הסתר כרטיסים בארכיון", "Hide archived tickets from users.": "הסתר כרטיסים בארכיון ממשתמשים.", "Hide chat if no agents online": "הסתר צ&#39;אט אם אין סוכנים מקוונים", "Hide chat outside of office hours": "הסתר צ&#39;אט מחוץ לשעות המשרד", "Hide conversation details panel": "הסתר את חלונית פרטי השיחה", "Hide conversations of other agents": "הסתר שיחות של סוכנים אחרים", "Hide on mobile": "הסתר בנייד", "Hide the agent's profile image within the chat.": "הסתר את תמונת הפרופיל של הסוכן בצ&#39;אט.", "Hide tickets from the chat widget and chats from the ticket area.": "הסתר כרטיסים מווידג&#39;ט הצ&#39;אט וצ&#39;אטים מאזור הכרטיסים.", "Hide timetable": "הסתר את לוח הזמנים", "Host": "מנחה", "Human takeover": "השתלטות אנושית", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "אם אף סוכנים לא יגיבו בתוך מרווח הזמן שצוין, תישלח הודעה כדי לבקש את פרטי המשתמש, כגון האימייל שלו.", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "אם הצ&#39;אט<PERSON><PERSON><PERSON> לא מבין שאלה של משתמש, מעביר את השיחה לסוכן.", "Image": "תמונה", "Import admins": "ייבוא מנהלים", "Import all settings.": "ייבא את כל ההגדרות.", "Import articles": "ייבוא מאמרים", "Import contacts": "ייבוא אנשי קשר", "Import customers": "ייבוא לקוחות", "Import customers into Support Board. Only new customers will be imported.": "ייבא לקוחות אל Support Board. רק לקוחות חדשים ייובאו.", "Import settings": "ייבא הגדרות", "Import users": "ייבוא משתמשים", "Import users from a CSV file.": "ייבא משתמשים מקובץ CSV.", "Import vendors": "ספ<PERSON>י ייבוא", "Import vendors into Support Board as agents. Only new vendors will be imported.": "ייבא ספקים אל Support Board כסוכנים. רק ספקים חדשים ייובאו.", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "שפר את ביצועי הצ&#39;אט עם <PERSON>er ו-WebSockets. הגדרה זו עוצרת את כל בקשות AJAX/HTTP בזמן אמת שמאטות את השרת שלך ומשתמשות במקום זאת ב-WebSockets.", "Include custom fields": "כלול שדות מותאמים אישית", "Include custom fields in the registration form.": "כלול שדות מותאמים אישית בטופס ההרשמה.", "Include the password field in the registration form.": "כלול את שדה הסיסמה בטופס ההרשמה.", "Incoming conversations and messages": "שיחות והודעות נכנסות", "Incoming conversations only": "שיחות נכנסות בלבד", "Incoming messages only": "הודעות נכנסות בלבד", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Support Board.": "הגדל את המכירות וחבר אותך למוכרים עם לקוחות בזמן אמת על ידי שילוב Active eCommerce עם לוח התמיכה.", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Support Board.": "הגדל את המכירות, ספק תמיכה טובה יותר ופתרונות מהירים יותר, על ידי שילוב WooCommerce עם לוח התמיכה.", "Info message": "הודעת מידע", "Initialize and display the chat widget and tickets only for members.": "אתחול והצג את ווידג&#39;ט הצ&#39;אט והכרטיסים רק לחברים.", "Initialize and display the chat widget only when the user is logged in.": "אתחול והצג את ווידג&#39;ט הצ&#39;אט רק כאשר המשתמש מחובר.", "Instance ID": "מזהה מופע", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "שלב OpenCart עם {R} לסנכרון בזמן אמת של לקוחות, גישה להיסטוריית הזמנות וניראות עגלת הלקוחות.", "Interval (sec)": "מרווח (שניות)", "IP banning": "איסור IP", "Label": "תווית", "Language": "שפה", "Language detection": "זי<PERSON><PERSON><PERSON> שפה", "Language detection message": "הודעת זיהוי שפה", "Last name": "שם משפחה", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "השאר את זה ריק אם אתה לא יודע מהי ההגדרה הזו! הזנת ערך שגוי תפר את הצ&#39;אט. מגדיר את הדומיין הראשי שבו נעשה שימוש בצ&#39;אט כדי לאפשר התחברות ושיתוף שיחות בין הדומיין הראשי ותת הדומיינים.", "Left": "שמאלה", "Left panel": "לו<PERSON> שמאל", "Left profile image": "תמונת פרופיל שמאל", "Let the bot to search on Google to find answers to user questions.": "תן לבוט לחפש בגוגל כדי למצוא תשובות לשאלות משתמשים.", "Let the chatbot search on Google to find answers to user questions.": "תן לצ&#39;אטבוט לחפש בגוגל כדי למצוא תשובות לשאלות משתמשים.", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "מאפשר למשתמשים שלך להגיע אליך באמצעות טוויטר. קרא והשב להודעות שנשלחו לחשבון הטוויטר שלך ישירות מ-{R}.", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "מאפשר למשתמשים שלך להגיע אליך דרך WeChat. קרא והשב לכל ההודעות שנשלחו לחשבון הרשמי שלך ב-WeChat ישירות מ-{R}.", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "מאפשר למשתמשים שלך להגיע אליך באמצעות WhatsApp. קרא והשב לכל ההודעות שנשלחו לחשבון WhatsApp Business שלך ישירות מ-{R}.", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "קשר כל סוכן עם משתמש Slack המתאים, כך שכאשר סוכן משיב דרך Slack הוא יוצג כסוכן שהוקצה.", "Link name": "שם קישור", "Login form": "טופס התחברות", "Login initialization": "אתחול הכניסה", "Login verification URL": "כתובת אתר לאימות כניסה", "Logit bias": "הטיית Logit", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "תחילה בצע גיבוי לסוכן Dialogflow שלך. פעולה זו יכולה להימשך מספר דקות.", "Make the registration phone field mandatory.": "הפוך את שדה טלפון הרישום לחובה.", "Manage": "לנהל", "Manage here the departments settings.": "נהל כאן את הגדרות המחלקות.", "Manage the tags settings.": "נהל את הגדרות התגים.", "Manifest file URL": "כתובת האתר של הקובץ Manifest", "Manual": "מדריך ל", "Manual initialization": "אתחול ידני", "Martfury root directory path, e.g. /var/www/": "נתיב ספריית השורש של Martfury, למשל /var/www/", "Martfury shop URL, e.g. https://shop.com": "כתובת האתר של חנות Mart<PERSON>, למשל https://shop.com", "Max message limit": "מגבלת הודעות מקסימלית", "Max tokens": "מקסימום אסימונים", "Members only": "לחברים בלבד", "Members with an active paid plan only": "חברים עם תוכנית בתשלום פעילה בלבד", "Message": "הוֹדָעָה", "Message area": "אזור הודעות", "Message rewrite button": "כפתור כתיבה מחדש של הודעה", "Message template": "תבנית הודעה", "Message type": "סוג הודעה", "Messaging channels": "ערוצי העברת הודעות", "Messenger and Instagram settings": "Messenger והגדרות Instagram", "Minify JS": "הקטנת JS", "Minimal": "מִינִימָלִי", "Model": "דֶגֶם", "Multilingual": "רב לשוני", "Multilingual plugin": "תוסף רב לשוני", "Multilingual via translation": "רב לשוני באמצעות תרגום", "Multlilingual training sources": "מקורות הדרכה רב-לשוניים", "Name": "שֵׁם", "Namespace": "מר<PERSON><PERSON> שמות", "New conversation email": "דוא&quot;ל שיחה חדש", "New conversation notification": "התראה על שיחה חדשה", "New ticket button": "כפתור כרטיס חדש", "Newsletter": "ניוזלטר", "No delay": "לל<PERSON> עיכוב", "No results found.": "לא נמצאו תוצאות.", "No, we don't ship in": "לא, אנחנו לא שולחים פנימה", "None": "אף אחד", "Note data scraping": "שימו לב לגרידת נתונים", "Notes": "הערות", "Notifications icon": "סמל התראות", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "הודע למשתמש כאשר ההודעה שלו נשלחת מחוץ לשעות המשרד המתוכננות או שכל הסוכנים נמצאים במצב לא מקוון.", "OA secret key": "מפתח סודי OA", "Offline message": "הודעה לא מקוונת", "Offset": "לְקַזֵז", "On chat open": "בצ&#39;אט פתוח", "On page load": "בטעינת עמודים", "One conversation per agent": "שיחה אחת לכל סוכן", "One conversation per department": "שיחה אחת לכל מחלקה", "Online users notification": "התראה למשתמשים מקוונים", "Only desktop": "רק שולחן עבודה", "Only general questions": "רק שאלות כלליות", "Only mobile devices": "רק מכשירים ניידים", "Only questions related to your sources": "רק שאלות הקשורות למקורות שלך", "Open automatically": "נפתח אוטומטית", "Open chat": "פתח צ&#39;אט", "Open the chat window automatically when a new message is received.": "פתח את חלון הצ&#39;אט באופן אוטומטי כאשר מתקבלת הודעה חדשה.", "OpenAI Assistants - Department linking": "עוזרי OpenAI - קישור מחלקה", "OpenAI settings.": "הגדרות OpenAI.", "Optional link": "קישור אופציונלי", "Order webhook": "הזמנת חיבור לאינטרנט", "Other": "אַח<PERSON>ר", "Outgoing SMTP server information.": "מידע שרת SMTP יוצא.", "Page ID": "מזהה עמוד", "Page IDs": "מזהי דפים", "Page name": "שם עמוד", "Page token": "אסימון עמוד", "Panel height": "גו<PERSON>ה הפאנל", "Panel name": "שם לוח", "Panel title": "כותרת הפאנל", "Panels arrows": "חיצים של לוחות", "Password": "סיסמה", "Perfex URL": "כתובת אתר פרפקס", "Performance optimization": "אופטימיזציה של ביצועים", "Phone": "<PERSON><PERSON><PERSON><PERSON><PERSON> טלפון", "Phone number ID": "מזהה מספר טלפון", "Phone required": "נדרש טלפון", "Place ID": "מזהה מקום", "Placeholder text": "טקסט מציין מיקום", "Play a sound for new messages and conversations.": "השמע צליל עבור הודעות ושיחות חדשות.", "Popup message": "הודעה קופצת", "Port": "נמל", "Post Type slugs": "Post Type שבלולים", "Presence penalty": "עונש נוכחות", "Prevent admins from receiving email notifications.": "מנע ממנהלי מערכת לקבל הודעות אימייל.", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "מנע מסוכנים לצפות בשיחות שהוקצו לסוכנים אחרים. הגדרה זו מופעלת באופן אוטומטי אם ניתוב או תור פעילים.", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "מנע כל שימוש לרעה ממשתמשים על ידי הגבלת מספר ההודעות הנשלחות לצ&#39;אטבוט ממכשיר אחד.", "Primary color": "צבע יסוד", "Priority": "עדיפות", "Privacy link": "קישור פרטיות", "Privacy message": "הודעת פרטיות", "Private chat": "שיחה פרטית", "Private chat linking": "קישור צ&#39;אט פרטי", "Private key": "מפתח פרטי", "Product IDs": "מזהי מוצר", "Product removed notification": "התראה על הסרת מוצר", "Product removed notification - Email": "הודעה על הסרת מוצר - אימייל", "Profile image": "תמונת פרופיל", "Project ID": "מזהה פרויקט", "Project ID or Agent Name": "מזהה פרויקט או שם סוכן", "Prompt": "מיי<PERSON>י", "Prompt - Message rewriting": "הנחיה - ש<PERSON><PERSON><PERSON><PERSON> הודעה", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "הגן על אזור הכרטיסים מפני ספאם וניצול לרעה עם Google reCAPTCHA.", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "ספק תמיכה בדלפק העזרה ללקוחות שלך על ידי הכללת אזור כרטיסים, עם כל תכונות הצ&#39;אט כלולות, בכל דף אינטרנט תוך שניות.", "Provider": "ספק", "Purchase button text": "טקסט לחצן רכישה", "Push notifications": "הודעות דחיפה", "Push notifications settings.": "הגדרות הודעות דחיפה.", "Queue": "תוֹר", "Rating": "דֵרוּג", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "קרא והשב להודעות שנשלחו מחיפוש Google, מפות ומערוצים בבעלות המותג ישירות ב-{R}.", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "קרא, נהל והשב לכל ההודעות שנשלחות לדפי הפייס<PERSON>וק ולחשבונות האינסטגרם שלך ישירות מ-{R}.", "Reconnect": "התח<PERSON>ר מחדש", "Redirect the user to the registration link instead of showing the registration form.": "הפנה את המשתמש לקישור ההרשמה במקום להציג את טופס ההרשמה.", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "הפנה את המשתמש לכתובת ה-U<PERSON> שצוינה אם ההרשמה נדרשת והמשתמש אינו מחובר. השאר ריק כדי להשתמש בטופס הרישום המוגדר כברירת מחדל.", "Refresh token": "רענן אסימון", "Register all visitors": "רשום את כל המבקרים", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "רשום את כל המבקרים באופן אוטומטי. כאשר אפשרות זו אינה פעילה, רק המבקרים שמתחילים צ&#39;אט יירשמו.", "Registration / Login": "הרשמה / כניסה", "Registration and login form": "טופס הרשמה והתחברות", "Registration fields": "שדות רישום", "Registration form": "טופס הרשמה", "Registration link": "קישור להרשמה", "Registration redirect": "הפניית רישום מחדש", "Rename the chat bot. Default is 'Bot'.": "שנה את שם הצ&#39;אט בוט. ברירת המחדל היא &#39;בוט&#39;.", "Rename the visitor name prefix. Default is 'User'.": "שנה את שם הקידומת של שם המבקר. ברירת המחדל היא &#39;משתמש&#39;.", "Repeat": "חז<PERSON>ר", "Repeat - admin": "חזור - א<PERSON><PERSON><PERSON>ן", "Replace the admin login page message.": "החלף את הודעת דף ההתחברות של מנהל המערכת.", "Replace the brand logo on the admin login page.": "החלף את לוגו המותג בדף הכניסה למנהל המערכת.", "Replace the header title with the user's first name and last name when available.": "החלף את כותרת הכותרת בשם הפרטי ושם המשפחה של המשתמש כאשר הם זמינים.", "Replace the top-left brand icon on the admin area and the browser favicon.": "החלף את סמל המותג השמאלי העליון באזור הניהול ואת סמל הדפדפן.", "Reply to user emails": "השב למיילים של משתמשים", "Reply to user text messages": "השב להודעות טקסט של משתמשים", "Reports": "דיווחים", "Reports area": "אזור דוחות", "Request a valid Envato purchase code for registration.": "<PERSON><PERSON><PERSON> קוד רכי<PERSON>ה חוקי של Envato לרישום.", "Request the user to provide their email address and then send a confirmation email to the user.": "בקש מהמשתמש לספק את כתובת הדוא&quot;ל שלו ולאחר מכן שלח דוא&quot;ל אישור למשתמש.", "Require phone": "ד<PERSON><PERSON><PERSON> טלפון", "Require registration": "דורש רישום", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "דרוש רישום משתמש או התחברות לפני תחילת צ&#39;אט. כדי להפעיל את אזור הכניסה יש לכלול את שדה הסיסמה.", "Require the user registration or login in order to use the tickets area.": "דרוש רישום משתמש או התחברות כדי להשתמש באזור הכרטיסים.", "Required": "נדרש", "Response time": "<PERSON><PERSON><PERSON> תגובה", "Restrict chat access by blocking IPs. List IPs with commas.": "הגבל את הגישה לצ&#39;אט על ידי חסימת כתובות IP. רשום כתובות IP עם פסיקים.", "Returning visitor message": "הודעת מבקר חוזר", "Rich messages": "הודעות עשירות", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "הודעות עשירות הן קטעי קוד שניתן להשתמש בהם בתוך הודעת צ&#39;אט. הם יכולים להכיל קוד HTML ומעובדים אוטומטית בצ&#39;אט. ניתן להשתמש בהודעות עשירות עם התחביר הבא: [rich-message-name]. יש המון הודעות עשירות מובנות לבחירה.", "Right": "<PERSON><PERSON><PERSON><PERSON>", "Right panel": "לו<PERSON> ימין", "Routing": "ניתוב", "Routing if offline": "ניתוב אם לא מקוון", "Save useful information like user country and language also for visitors.": "שמור מידע שימושי כמו ארץ ושפה של המשתמש גם עבור מבקרים.", "Saved replies": "תשובות שמורות", "Scheduled office hours": "שעות עבודה קבועות", "Search engine ID": "מזהה מנוע חיפוש", "Second chat message": "הודעת צ&#39;אט שנייה", "Second reminder delay (hours)": "עיכוב תזכורת שני (שעות)", "Secondary color": "צבע משני", "Secret key": "מפתח סודי", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "שלח הודעה כדי לאפשר ללקוחות לקבל הודעה כאשר הם יכולים לרכוש מוצר שהם מעוניינים בו, אך כרגע אזל מהמלאי. אתה יכול להשתמש בשדות המיזוג הבאים: {user_name}, {product_name}.", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "שלח הודעה למשתמשים חדשים כאשר הם יוצרים את הכרטיס הראשון. עיצוב טקסט ושדות מיזוג נתמכים.", "Send a message to new users when they visit the website for the first time.": "שלח הודעה למשתמשים חדשים כאשר הם מבקרים באתר בפעם הראשונה.", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "שלח הודעה ללקוח לאחר הסרת מוצר מהסל. אתה יכול להשתמש בשדות המיזוג הבאים ועוד: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "שלחו הודעה ללקוחות המשלימים רכישה בבקשה לשתף את המוצר שזה עתה קנו. אתה יכול להשתמש בשדות המיזוג הבאים ועוד: {product_name}, {user_name}.", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "שלחו הודעה ללקוחות המשלימים רכישה. אתה יכול להשתמש בשדות המיזוג הבאים ועוד: {coupon}, {product_names}, {user_name}.", "Send a message to the user when the agent archive the conversation.": "שלח הודעה למשתמש כאשר הסוכן מעביר את השיחה לארכיון.", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "שלח הודעה למשתמשים שיבקרו שוב באתר לאחר 24 שעות לפחות. אתה יכול להשתמש בשדות המיזוג הבאים ועוד: {coupon}, {user_name}. עיין במסמכים לפרטים נוספים.", "Send a test agent notification email to verify email settings.": "שלח הודעת דוא&quot;ל של סוכן בדיקה כדי לאמת את הגדרות הדוא&quot;ל.", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "שלח הודעת בדיקה לערוץ ה-Slack שלך. זה רק בודק את פונקציונליות השליחה של הודעות יוצאות.", "Send a test user notification email to verify email settings.": "שלח הודעת דוא&quot;ל למשתמש בדיקה כדי לאמת את הגדרות הדוא&quot;ל.", "Send a text message to the provided phone number.": "שלח הודעת טקסט למספר הטלפון שסופק.", "Send a user email notification": "שלח הודעת דוא&quot;ל למשתמש", "Send a user text message notifcation": "שלח הודעת טקסט למשתמש", "Send a user text message notification": "שלח הודעת טקסט למשתמש", "Send an agent email notification": "שלח הודעת דוא&quot;ל לסוכן", "Send an agent text message notification": "שלח הודעת טקסט לסוכן", "Send an agent user text notification": "שלח הודעת טקסט למשתמש של סוכן", "Send an email notification to the provided email address.": "שלח הודעת דוא&quot;ל לכתובת הדוא&quot;ל שסופקה.", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "שלח דוא&quot;ל לסוכן כאשר משתמש משיב והסוכן במצב לא מקוון. דוא&quot;ל נשלח אוטומטית לכל הסוכנים עבור שיחות חדשות.", "Send an email to the user when a new conversation is created.": "שלח דוא&quot;ל למשתמש כאשר נוצרת שיחה חדשה.", "Send an email to the user when a new conversation or ticket is created": "שלח אימייל למשתמש כאשר נוצר שיחה או כרטיס חדש", "Send an email to the user when an agent replies and the user is offline.": "שלח דוא&quot;ל למשתמש כאשר סוכן משיב והמשתמש במצב לא מקוון.", "Send email": "של<PERSON> אימייל", "Send login details to the specified URL and allow access only if the response is positive.": "שלח פרטי התחברות לכתובת האתר שצוינה ואפשר גישה רק אם התגובה חיובית.", "Send message": "לשלוח הודעה", "Send message to Slack": "שלח הודעה ל-Slack", "Send message via enter button": "שלח הודעה באמצעות כפתור אנטר", "Send text message": "שלח הודעת טקסט", "Send the message template to a WhatsApp number.": "שלח את תבנית ההודעה למספר WhatsApp.", "Send the message via the ENTER keyboard button.": "שלח את ההודעה באמצעות כפתור המקלדת ENTER.", "Send the user details of the registration form and email rich messages to Dialogflow.": "שלח את פרטי המשתמש של טופס ההרשמה והודעות עשירות בדוא&quot;ל אל Dialogflow.", "Send the WhatsApp order details to the URL provided.": "שלח את פרטי הזמנת WhatsApp לכתובת ה-URL שסופקה.", "Send to user's email": "שלח למייל של המשתמש", "Send transcript to user's email": "שלח תמליל למייל של המשתמש", "Send user details": "שלח פרטי משתמש", "Sender": "שׁוֹלֵחַ", "Sender email": "שולח מייל", "Sender name": "שם השולח", "Sender number": "מספר השולח", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "שולח הודעת טקסט אם שליחת הודעת WhatsApp נכשלת. אתה יכול להשתמש בטקסט ובשדות המיזוג הבאים: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "שולח התראה על תבנית WhatsApp אם שליחת הודעת WhatsApp נכשלת. אתה יכול להשתמש בטקסט ובשדות המיזוג הבאים: {conversation_url_parameter}, {recipient_name}, {recipient_email}.", "Service": "שֵׁרוּת", "Service Worker path": "נתיב Service Worker", "Service Worker URL": "Service Worker כתובת אתר", "Set a dedicated Dialogflow agent for each department.": "הגדר סוכן ייעודי של Dialogflow לכל מחלקה.", "Set a dedicated OpenAI Assistants for each department.": "הגדר עוזרי OpenAI ייעודיים לכל מחלקה.", "Set a dedicated Slack channel for each department.": "הגדר ערוץ S<PERSON>ck ייעודי לכל מחלקה.", "Set a profile image for the chat bot.": "הגדר תמונת פרופיל עבור הצ&#39;אט בוט.", "Set the articles panel title. Default is 'Help Center'.": "הגדר את כותרת חלונית המאמרים. ברירת המחדל היא &#39;מרכז העזרה&#39;.", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "הגדר את תמונת הדמות המוצגת לצד ההודעה. היא חייבת להיות תמונת JPG של 1024x1024px עם גודל מקסימלי של 50KB.", "Set the chat language or translate it automatically to match the user language. Default is English.": "הגדר את שפת הצ&#39;אט או תרגם אותה אוטומטית כך שתתאים לשפת המשתמש. ברירת המחדל היא אנגלית.", "Set the currency symbol of the membership prices.": "הגדר את סמל המטבע של מחירי החברות.", "Set the currency symbol used by your system.": "הגדר את סמל המטבע המשמש את המערכת שלך.", "Set the default departments for all tickets. Enter the department ID.": "הגדר את מחלקות ברירת המחדל עבור כל הכרטיסים. הזן את מזהה המחלקה.", "Set the default email header that will be prepended to automated emails and direct emails.": "הגדר את כותרת הדוא&quot;ל המוגדרת כברירת מחדל שתתווסף להודעות דוא&quot;ל אוטומטיות ולהודעות דוא&quot;ל ישירות.", "Set the default email signature that will be appended to automated emails and direct emails.": "הגדר את חתימת הדוא&quot;ל המוגדרת כברירת מחדל שתצורף לאימיילים אוטומטיים ולאימיילים ישירים.", "Set the default form to display if the registraion is required.": "הגדר את טופס ברירת המחדל שיוצג אם יש צורך בהרשמה.", "Set the default name to use for conversations without a name.": "הגדר את שם ברירת המחדל לשימוש עבור שיחות ללא שם.", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "הגדר את סמל ברירת המחדל של התראות. הסמל ישמש כתמונת פרופיל אם אין למשתמש.", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "הגדר את שעות המשרד המוגדרות כברירת מחדל עבור מתי הסוכנים מוצגים כזמינים. הגדרות אלו משמשות גם עבור כל שאר ההגדרות המסתמכות על שעות עבודה.", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "הגדר את שם המשתמש המוגדר כברירת מחדל לשימוש בהודעות בוט ובאימיילים כאשר למשתמש אין שם.", "Set the header appearance.": "הגדר את מראה הכותרת.", "Set the maximum height of the tickets panel.": "הגדר את הגובה המרבי של לוח הכרטיסים.", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "הגדר את הפלאגין הרב לשוני שבו אתה משתמש, או השאר אותו מושבת אם האתר שלך משתמש בשפה אחת בלבד.", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "הגדר את המצב הלא מקוון באו<PERSON>ן אוטומטי כאשר הסוכן או האדמין נשארים לא פעילים באזור הניהול למשך 10 דקות לפחות.", "Set the position of the chat widget.": "הגדר את המיקום של ווידג&#39;ט הצ&#39;אט.", "Set the primary color of the admin area.": "הגדר את הצבע הראשי של אזור הניהול.", "Set the primary color of the chat widget.": "הגדר את הצבע הראשי של ווידג&#39;ט הצ&#39;אט.", "Set the secondary color of the admin area.": "הגדר את הצבע המשני של אזור הניהול.", "Set the secondary color of the chat widget.": "הגדר את הצבע המשני של ווידג&#39;ט הצ&#39;אט.", "Set the tertiary color of the chat widget.": "הגדר את הצבע השלישוני של ווידג&#39;ט הצ&#39;אט.", "Set the title of the administration area.": "הגדר את הכותרת של אזור הניהול.", "Set the title of the conversations panel.": "הגדר את הכותרת של חלונית השיחות.", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "הגדר את היסט UTC של לוח שעות המשרד. הערך הנכון יכול להיות שלילי, והוא נוצר באופן אוטומטי ברגע שאתה לוחץ על שדה קלט זה, אם הוא ריק.", "Set which actions to allow agents.": "הגדר אילו פעולות לאפשר לסוכנים.", "Set which actions to allow supervisors.": "הגדר אילו פעולות לאפשר למפקחים.", "Set which user details to send to the main channel. Add comma separated values.": "הגדר אילו פרטי משתמש לשלוח לערוץ הראשי. הוסף ערכים מופרדים בפסיקים.", "Settings area": "אזור ההגדרות", "settings information": "מידע על הגדרות", "Shop": "לִקְנוֹת", "Show": "הופעה", "Show a browser tab notification when a new message is received.": "הצג התראה על כרטיסיית דפד<PERSON>ן כאשר מתקבלת הודעה חדשה.", "Show a desktop notification when a new message is received.": "הצג התראה על שולחן העבודה כאשר מתקבלת הודעה חדשה.", "Show a notification and play a sound when a new user is online.": "הצג התראה והשמע צליל כאשר משתמש חדש מחובר.", "Show a pop-up notification to all users.": "הצג הודעה קופצת לכל המשתמשים.", "Show profile images": "הצג תמונות פרופיל", "Show sender's name": "הצג את שם השולח", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "הצג את תפריט הסוכנים בלוח המחוונים ואילץ את המשתמש לבחור סוכן כדי להתחיל שיחה.", "Show the articles panel on the chat dashboard.": "הצג את חלונית המאמרים בלוח המחוונים של הצ&#39;אט.", "Show the categories instead of the articles list.": "הצג את הקטגוריות במקום את רשימת המאמרים.", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "הצג את הודעת המעקב כאשר מבקר מוסיף פריט לעגלת הקניות. ההודעה נשלחת רק אם המשתמש עדיין לא סיפק אימייל.", "Show the list of all Slack channels.": "הצג את רשימת כל ערוצי <PERSON>ck.", "Show the profile image of agents and users within the conversation.": "הצג את תמונת הפרופיל של סוכנים ומשתמשים בתוך השיחה.", "Show the sender's name in every message.": "הצג את שם השולח בכל הודעה.", "Single label": "תווית בודדת", "Single phone country code": "קוד מדינה לטלפון בודד", "Site key": "מפתח אתר", "Slug": "שבלול", "Smart Reply": "תשובה חכמה", "Social share message": "הודעת שיתוף חברתית", "Sort conversations by date": "מיין שיחות לפי תאריך", "Sound": "נשמע", "Sound settings": "הגדרות שמע", "Sounds": "צלילים", "Sounds - admin": "צלילים - <PERSON><PERSON><PERSON><PERSON><PERSON>", "Source links": "קישורי מקור", "Speech recognition": "<PERSON><PERSON><PERSON><PERSON><PERSON> דיבור", "Spelling correction": "תיקון איות", "Starred tag": "תג מסומן בכו<PERSON>ב", "Start importing": "התחל לייבא", "Store name": "שם חנות", "Subject": "נושא", "Subscribe": "הירשם", "Subscribe users to your preferred newsletter service when they provide an email.": "הירשם למשתמשים לשירות הניוזלטר המועדף עליך כאשר הם מספקים דוא&quot;ל.", "Subtract the offset value from the height value.": "הפחת את ערך ההיסט מערך הגובה.", "Success message": "הודעת הצלחה", "Supervisors": "מפקחים", "Support Board path": "נתיב לוח תמיכה", "Sync admin and staff accounts with Support Board. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "סנכרן את חשבונות המנהל והצוות עם לוח התמיכה. משתמשי צוות יירשמו כסוכנים, בעוד שמנהלים כמנהלים. רק משתמשים חדשים ייובאו.", "Sync all contacts of all clients with Support Board. Only new contacts will be imported.": "סנכרן את כל אנשי הקשר של כל הלקוחות עם לוח התמיכה. רק אנשי קשר חדשים ייובאו.", "Sync all users with Support Board. Only new users will be imported.": "סנכרן את כל המשתמשים עם לוח התמיכה. רק משתמשים חדשים ייובאו.", "Sync all WordPress users with Support Board. Only new users will be imported.": "סנכרן את כל משתמשי וורדפרס עם לוח תמיכה. רק משתמשים חדשים ייובאו.", "Sync knowledge base articles with Support Board. Only new articles will be imported.": "סנכרן מאמרי בסיס ידע עם לוח התמיכה. רק מאמרים חדשים ייובאו.", "Sync mode": "מצ<PERSON>ון", "Synchronization": "סִנכְּרוּן", "Synchronize": "לְסַנכ<PERSON>רֵן", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "סנכרן לקוחות, א<PERSON><PERSON>ר תמיכה בכרטיסים וצ&#39;אט למנויים בלבד, הצג תוכניות מנויים באזור הניהול.", "Synchronize emails": "סנכרון מיילים", "Synchronize Entities": "סנכרן Entities", "Synchronize Entities now": "סנכרן Entities עכשיו", "Synchronize now": "<PERSON><PERSON><PERSON><PERSON><PERSON> עכשיו", "Synchronize users": "סנכרון משתמשים", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "סנכרן את הלקוחות שלך בזמן אמת, צ&#39;אט איתם והגבר את המעורבות שלהם, או ספק תמיכה טובה ומהירה יותר.", "Synchronize your Messenger and Instagram accounts.": "סנכרן את חשבונות ה-Messenger והאינסטגרם שלך.", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "סנכרן את לקוחות Perfex שלך בזמן אמת ואפשר להם ליצור איתך קשר באמצעות צ&#39;אט! הצג את פרטי הפרופיל, התקשר אליהם באופן יזום ועוד.", "Synchronize your WhatsApp Cloud API account.": "סנכרן את חשבון WhatsApp Cloud API שלך.", "System requirements": "דרישות מערכת", "Tags": "תגים", "Tags settings": "הגדרות תגים", "Template default language": "שפת ברירת המחדל של התבנית", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "תבנית לאימייל שנשלח למשתמש כאשר סוכן משיב. אתה יכול להשתמש בטקסט, ב-HTML ובשדות המיזוג הבאים: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "תבנית המייל שנשלח למשתמש בעת יצירת שיחה חדשה. אתה יכול להשתמש בטקסט, ב-HTML ובשדות המיזוג הבאים: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "תבנית למייל שנשלח למשתמש בעת יצירת שיחה או כרטיס חדש. אתה יכול להשתמש בטקסט, ב-HTML ובשדות המיזוג הבאים: {conversation_url_parameter}, {user_name}, {message}, {attachments}.", "Template languages": "שפות תבנית", "Template name": "שם התבנית", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "תבנית של הודעת דוא&quot;ל למנהל המערכת. אתה יכול להשתמש בטקסט, ב-HTML ובשדה המיזוג הבא ועוד: {carts}. הזן את האימייל שאליו ברצונך לשלוח הודעות בשדה כתובת הדוא&quot;ל.", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "תבנית המייל שנשלח ללקוח לאחר הסרת מוצר מהסל. אתה יכול להשתמש בטקסט, ב-HTML ובשדות המיזוג הבאים ועוד: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "תבנית של דוא&quot;ל ההתראה הראשון. אתה יכול להשתמש בטקסט, ב-HTML ובשדות המיזוג הבאים ועוד: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "תבנית של דוא&quot;ל ההתראה השני. אתה יכול להשתמש בטקסט, ב-HTML ובשדות המיזוג הבאים ועוד: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "תבנית של הודעת דוא&quot;ל ברשימת ההמתנה. אתה יכול להשתמש בטקסט, ב-HTML ובשדה המיזוג הבא ועוד: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.", "Terms link": "קישור לתנאים", "Tertiary color": "צ<PERSON>ע שלישוני", "Test Slack": "<PERSON><PERSON><PERSON><PERSON>", "Test template": "תבנית בדיקה", "Text": "טֶקסט", "Text message fallback": "חזרה של הודעת טקסט", "Text message notifications": "הודעות טקסט", "Text messages": "הודעות טקסט", "The product is not in the cart.": "המוצר אינו בעגלה.", "The workspace name you are using to synchronize Slack.": "השם workspace שבו אתה משתמש כדי לסנכרן את <PERSON>ck.", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "זהו מזהה ערוץ Slack הראשי שלך, שהוא בדרך כלל הערוץ הכללי #. תקבל את הקוד הזה על ידי השלמת סנכרון Slack.", "This returns the Support Board path of your server.": "זה מחזיר את הנתיב Support Board של השרת שלך.", "This returns your Support Board URL.": "זה מחזיר את כתובת האתר שלך Support Board.", "Ticket custom fields": "שדות כרטיס מותאמים אישית", "Ticket email": "מייל כרטיס", "Ticket field names": "שמות שדות כרטיסים", "Ticket fields": "שדות כרטיסים", "Ticket only": "כרט<PERSON>ס בלבד", "Ticket products selector": "בורר מוצרי כרטיסים", "Title": "כותרת", "Top": "<PERSON><PERSON><PERSON> עליון", "Top bar": "בר עליון", "Training via cron job": "הדר<PERSON>ה באמצעות cron job", "Transcript": "תמלול", "Transcript settings.": "הגדרות תמלול.", "Trigger": "הדק", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "הפעל את Dialogflow Welcome Intent עבור מבקרים חדשים כאשר הודעת הפתיחה פעילה.", "Troubleshoot": "פתרון בעיות", "Troubleshoot problems": "לפתור בעיות", "Twilio settings": "הגדר<PERSON><PERSON>", "Twilio template": "תב<PERSON><PERSON><PERSON>", "Unsubscribe": "בטל את המנוי", "Upload attachments to Amazon S3.": "העלה קבצים מצורפים ל-Amazon S3.", "Usage Limit": "מגבלת שימוש", "Use this option to change the PWA icon. See the docs for more details.": "השת<PERSON>ש באפשרות זו כדי לשנות את סמל PWA. עיין במסמכים לפרטים נוספים.", "User details": "פרטי המשתמש", "User details in success message": "פרטי משתמש בהודעת הצלחה", "User email notifications": "הודעות דוא&quot;ל למשתמש", "User login form information.": "פרטי טופס התחברות למשתמש.", "User message template": "תבנית הודעת משתמש", "User name as title": "שם משתמש ככותרת", "User notification email": "אימייל התראה למשתמש", "User registration form information.": "מידע על טופס הרשמה למשתמש.", "User roles": "תפקידי משתמש", "User system": "מערכת משתמש", "Username": "שם משתמש", "Users and agents": "משתמשים וסוכנים", "Users area": "אזור משתמשים", "Users only": "משתמשים בלבד", "Users table additional columns": "משתמשים שולחים עמודות נוספות", "UTC offset": "קיזוז UTC", "Variables": "משתנים", "View channels": "צ<PERSON>ה בערוצים", "View unassigned conversations": "הצג שיחות שלא הוקצו", "Visibility": "רְאוּת", "Visitor default name": "שם ברירת המחדל של המבקר", "Visitor name prefix": "קידומת שם המבקר", "Volume": "כרך", "Volume - admin": "נפח - א<PERSON><PERSON><PERSON>ן", "Waiting list": "רשימת המתנה", "Waiting list - Email": "רשימת המתנה - מייל", "Webhook URL": "URL של Webhook", "Webhooks": "Webhooks", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "Webhooks הם מידע שנשלח ברקע לכתובת URL ייחודית שהוגדרה על ידך כאשר משהו קורה.", "Website": "אתר אינטרנט", "WeChat settings": "הגדרות WeChat", "Welcome message": "הודעת ברוכים הבאים", "Whmcs admin URL": "כתובת האתר של ניהול Whmcs", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "כתובת האתר של ניהול Whmcs. לְשֶׁעָבַר. https://example.com/whmcs/admin/", "WordPress registration": "רישום וורדפרס", "Yes, we ship in": "כן, אנחנו שולחים פנימה", "You haven't placed an order yet.": "עדיין לא ביצעת הזמנה.", "You will get this code by completing the Dialogflow synchronization.": "תקבל את הקוד הזה על ידי השלמת סנכרון Dialogflow.", "You will get this code by completing the Slack synchronization.": "תקבל את הקוד הזה על ידי השלמת סנכרו<PERSON> Slack.", "You will get this information by completing the synchronization.": "תקבל מידע זה על ידי השלמת הסנכרון.", "Your cart is empty.": "העגלה שלך ריקה.", "Your turn message": "הודעת התור שלך", "Your username": "שם המשתמש שלך", "Your WhatsApp catalogue details.": "פרטי קטלוג הוואטסאפ שלך.", "Zendesk settings": "הגדרות Zendesk", "Image recognition": "זיה<PERSON><PERSON> תמונה", "Logs": "יומני רישום"}