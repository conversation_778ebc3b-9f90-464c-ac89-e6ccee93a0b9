{"chat": [{"type": "checkbox", "id": "chat-login-init", "title": "Login initialization", "content": "Initialize and display the chat widget only when the user is logged in.", "keywords": ""}, {"type": "checkbox", "id": "init-dashboard", "title": "Dashboard display", "content": "Display the dashboard instead of the chat area on initialization."}, {"type": "checkbox", "id": "disable-dashboard", "title": "Disable dashboard", "content": "Disable the dashboard, and allow only one conversation per user."}, {"type": "checkbox", "id": "force-one-conversation", "title": "Allow only one conversation", "content": "Allow only one conversation per user."}, {"type": "checkbox", "id": "chat-timetable-disable", "title": "Hide chat outside of office hours", "content": "Disable and hide the chat widget outside of scheduled office hours."}, {"type": "checkbox", "id": "chat-offline-disable", "title": "Hide chat if no agents online", "content": "Disable and hide the chat widget if all agents are offline."}, {"type": "select", "id": "front-auto-translations", "title": "Language", "content": "Set the chat language or translate it automatically to match the user language. De<PERSON><PERSON> is English.", "value": [["", "<PERSON><PERSON><PERSON>"], ["auto", "Multilingual"], ["am", "Amharic"], ["ar", "Arabic"], ["bg", "Bulgarian"], ["br", "Portuguese - Brasilian"], ["cs", "Czech"], ["da", "Danish"], ["de", "German"], ["el", "Greek"], ["es", "Spanish"], ["et", "Estonian"], ["fa", "Persian"], ["fi", "Finnish"], ["fr", "French"], ["he", "Hebrew"], ["hi", "Hindi"], ["hr", "Croatian"], ["hu", "Hungarian"], ["id", "Indonesian"], ["is", "Icelandic"], ["it", "Italian"], ["ja", "Japanese"], ["ka", "Georgian"], ["ko", "Korean"], ["lt", "Lithuanian"], ["mk", "Macedonian"], ["mn", "Mongolian"], ["ms", "Malay"], ["my", "Burmese"], ["nl", "Dutch"], ["no", "Norwegian"], ["pa", "Punjabi - <PERSON><PERSON><PERSON><PERSON><PERSON>"], ["pl", "Polish"], ["pt", "Portuguese"], ["ro", "Romanian"], ["ru", "Russian"], ["sk", "Slovak"], ["sl", "Slovenian"], ["sq", "Albanian"], ["sr", "Serbian"], ["su", "Sundanese"], ["sv", "Swedish"], ["th", "Thai"], ["tl", "Filipino"], ["tr", "Turkish"], ["uk", "Ukrainian"], ["vi", "Vietnamese"], ["zh", "Chinese"], ["zt", "Chinese - Traditional"]]}, {"type": "checkbox", "id": "auto-open", "title": "Open automatically", "content": "Open the chat window automatically when a new message is received."}, {"type": "checkbox", "id": "disable-uploads", "title": "Disable uploads", "content": "Disable file uploading capabilities within the chat."}, {"type": "checkbox", "id": "disable-voice-messages", "title": "Disable voice messages", "content": "Disable voice message capabilities within the chat."}, {"type": "checkbox", "id": "close-chat", "title": "Close chat", "content": "Allow the user to archive a conversation and hide archived conversations."}, {"type": "checkbox", "id": "chat-manual-init", "title": "Manual initialization", "content": "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it."}, {"type": "multi-input", "id": "agents-menu", "title": "Agents menu", "content": "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.", "value": [{"type": "checkbox", "id": "agents-menu-active", "title": "Active"}, {"type": "checkbox", "id": "agents-menu-force-one", "title": "One conversation per agent"}, {"type": "checkbox", "id": "agents-menu-online-only", "title": "Display online agents only"}, {"type": "text", "id": "agents-menu-title", "title": "Dashboard title"}]}, {"type": "multi-input", "id": "messaging-channels", "title": "Messaging channels", "content": "Allow users to contact you via their favorite messaging apps.", "value": [{"type": "checkbox", "id": "messaging-channels-active", "title": "Active"}, {"type": "text", "id": "messaging-channels-title", "title": "Dashboard title"}, {"type": "text", "id": "messaging-channels-wa", "title": "WhatsApp"}, {"type": "text", "id": "messaging-channels-fb", "title": "<PERSON>"}, {"type": "text", "id": "messaging-channels-ig", "title": "Instagram"}, {"type": "text", "id": "messaging-channels-tw", "title": "Twitter"}, {"type": "text", "id": "messaging-channels-tg", "title": "Telegram"}, {"type": "text", "id": "messaging-channels-vb", "title": "Viber"}, {"type": "text", "id": "messaging-channels-za", "title": "<PERSON><PERSON>"}, {"type": "text", "id": "messaging-channels-ln", "title": "LINE"}, {"type": "text", "id": "messaging-channels-wc", "title": "WeChat"}, {"type": "text", "id": "messaging-channels-em", "title": "Email"}, {"type": "text", "id": "messaging-channels-tk", "title": "Ticket"}]}], "messages": [{"type": "multi-input", "id": "welcome-message", "title": "Welcome message", "content": "Send a message to new users when they visit the website for the first time.", "help": "https://board.support/docs/#welcome-message", "value": [{"type": "checkbox", "id": "welcome-active", "title": "Active"}, {"type": "checkbox", "id": "welcome-open", "title": "Open chat"}, {"type": "checkbox", "id": "welcome-sound", "title": "Sound"}, {"type": "checkbox", "id": "welcome-disable-office-hours", "title": "Disable outside of office hours"}, {"type": "select", "id": "welcome-trigger", "title": "<PERSON><PERSON>", "value": [["load", "On page load"], ["open", "On chat open"]]}, {"type": "number", "id": "welcome-delay", "title": "Delay (ms)"}, {"type": "textarea", "id": "welcome-msg", "title": "Message"}]}, {"type": "multi-input", "id": "follow-message", "title": "Follow up message", "content": "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.", "help": "https://board.support/docs/#follow-up-message", "value": [{"type": "checkbox", "id": "follow-active", "title": "Active"}, {"type": "checkbox", "id": "follow-disable-office-hours", "title": "Disable outside of office hours"}, {"type": "checkbox", "id": "follow-disable-channels", "title": "Disable for messaging channels"}, {"type": "checkbox", "id": "follow-name", "title": "Name"}, {"type": "checkbox", "id": "follow-last-name", "title": "Last name"}, {"type": "checkbox", "id": "follow-phone", "title": "Phone"}, {"type": "checkbox", "id": "follow-phone-required", "title": "Phone required"}, {"type": "text", "id": "follow-title", "title": "Title"}, {"type": "number", "id": "follow-delay", "title": "Delay (ms)"}, {"type": "checkbox", "id": "follow-sound", "title": "Sound"}, {"type": "textarea", "id": "follow-msg", "title": "Message"}, {"type": "textarea", "id": "follow-success", "title": "Success message"}, {"type": "text", "id": "follow-placeholder", "title": "Placeholder text"}]}, {"type": "multi-input", "setting": "emails", "id": "email-subscribe", "title": "Follow up email", "content": "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.", "value": [{"type": "text", "id": "email-subscribe-subject", "title": "Subject"}, {"type": "textarea", "id": "email-subscribe-content", "title": "Content"}]}, {"type": "multi-input", "id": "rating-message", "title": "Rating", "content": "Display the feedback form to rate the conversation when it is archived.", "help": "https://board.support/docs/#rating", "value": [{"type": "checkbox", "id": "rating-active", "title": "Active"}, {"type": "checkbox", "id": "rating-message-area", "title": "Message area"}]}, {"type": "multi-input", "id": "chat-timetable", "title": "Offline message", "content": "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.", "help": "https://board.support/docs/#offline-message", "value": [{"type": "checkbox", "id": "chat-timetable-active", "title": "Active"}, {"type": "checkbox", "id": "chat-timetable-hide", "title": "Hide timetable"}, {"type": "checkbox", "id": "chat-timetable-agents", "title": "Disable agents check"}, {"type": "select", "id": "chat-timetable-type", "title": "Message type", "value": [["", "Chat message"], ["info", "Info message"], ["header", "Header"]]}, {"type": "text", "id": "chat-timetable-title", "title": "Title"}, {"type": "textarea", "id": "chat-timetable-msg", "title": "Message"}]}, {"type": "multi-input", "id": "close-message", "title": "Close message", "content": "Send a message to the user when the agent archive the conversation.", "keywords": "transcript", "value": [{"type": "checkbox", "id": "close-active", "title": "Active"}, {"type": "checkbox", "id": "close-transcript", "title": "Send transcript to user's email"}, {"type": "textarea", "id": "close-msg", "title": "Message"}]}, {"type": "multi-input", "id": "popup-message", "title": "Popup message", "content": "Show a pop-up notification to all users.", "help": "https://board.support/docs/#pop-up-message", "value": [{"type": "checkbox", "id": "popup-active", "title": "Active"}, {"type": "checkbox", "id": "popup-mobile-hidden", "title": "Hide on mobile"}, {"type": "upload-image", "id": "popup-image", "title": "Profile image"}, {"type": "text", "id": "popup-title", "title": "Title"}, {"type": "textarea", "id": "popup-msg", "title": "Message"}]}, {"type": "multi-input", "id": "privacy", "title": "Privacy message", "content": "Before initiating the chat, the user must accept a privacy message in order to gain access.", "help": "https://board.support/docs/#privacy-message", "value": [{"type": "checkbox", "id": "privacy-active", "title": "Active"}, {"type": "checkbox", "id": "privacy-disable-channels", "title": "Disable for messaging channels"}, {"type": "text", "id": "privacy-title", "title": "Title"}, {"type": "textarea", "id": "privacy-msg", "title": "Message"}, {"type": "textarea", "id": "privacy-msg-decline", "title": "Declined message"}, {"type": "text", "id": "privacy-link", "title": "Optional link"}, {"type": "text", "id": "privacy-link-text", "title": "Link name"}, {"type": "text", "id": "privacy-btn-approve", "title": "Accept button text"}, {"type": "text", "id": "privacy-btn-decline", "title": "Decline button text"}]}], "admin": [{"type": "checkbox", "id": "collapse", "title": "Collapse panels", "content": "Automatically collapse the conversation details panel, and other panels, of the admin area."}, {"type": "checkbox", "id": "hide-conversation-details", "title": "Hide conversation details panel", "content": "Automatically hide the conversation details panel."}, {"type": "text", "id": "admin-title", "title": "Admin title", "content": "Set the title of the administration area."}, {"type": "upload-image", "id": "login-icon", "title": "Admin login logo", "content": "Replace the brand logo on the admin login page.", "background-size": "auto 64px"}, {"type": "upload-image", "id": "admin-icon", "title": "Admin icon", "content": "Replace the top-left brand icon on the admin area and the browser favicon.", "background-size": "auto 64px"}, {"type": "text", "id": "login-message", "title": "Admin login message", "content": "Replace the admin login page message."}, {"type": "checkbox", "id": "admin-auto-translations", "title": "Automatically translate admin area", "content": "Automatically translate the admin area to match the agent profile language or browser language.", "help": "https://board.support/docs/#multilingual-admin"}, {"type": "checkbox", "id": "admin-disable-settings-translations", "title": "Do not translate settings area", "content": "Activate this option if you don't want to translate the settings area."}, {"type": "checkbox", "id": "admin-auto-archive", "title": "Automatically archive conversations", "content": "Archive automatically the conversations marked as read every 24h."}, {"type": "checkbox", "id": "order-by-date", "title": "Sort conversations by date", "content": "Always sort conversations by date in the admin area."}, {"type": "multi-input", "id": "agents", "title": "Agent privileges", "content": "Set which actions to allow agents.", "value": [{"type": "checkbox", "id": "agents-users-area", "title": "Users area"}, {"type": "checkbox", "id": "agents-articles-area", "title": "Articles area"}, {"type": "checkbox", "id": "agents-edit-user", "title": "Edit user"}, {"type": "checkbox", "id": "agents-tab", "title": "Agents and admins tab"}, {"type": "checkbox", "id": "agents-delete-conversation", "title": "Delete conversation"}, {"type": "checkbox", "id": "agents-delete-message", "title": "Delete message"}, {"type": "checkbox", "id": "agents-update-department", "title": "Update conversation department"}]}, {"type": "repeater", "id": "supervisors", "title": "Supervisors", "content": "Set which actions to allow supervisors.", "help": "https://board.support/docs/#agents", "items": [{"type": "text", "id": "supervisor-id", "name": "Admin IDs"}, {"type": "checkbox", "id": "supervisor-users-area", "name": "Users area"}, {"type": "checkbox", "id": "supervisor-settings-area", "name": "Settings area"}, {"type": "checkbox", "id": "supervisor-reports-area", "name": "Reports area"}, {"type": "checkbox", "id": "supervisor-articles-area", "name": "Articles area"}, {"type": "checkbox", "id": "supervisor-edit-user", "name": "Edit user"}, {"type": "checkbox", "id": "supervisor-agents-tab", "name": "Agents and admins tab"}, {"type": "checkbox", "id": "supervisor-delete-conversation", "name": "Delete conversation"}, {"type": "checkbox", "id": "supervisor-delete-message", "name": "Delete message"}, {"type": "checkbox", "id": "supervisor-send-message", "name": "Send message"}]}, {"type": "checkbox", "id": "show-profile-images-admin", "title": "Show profile images", "content": "Show the profile image of agents and users within the conversation."}, {"type": "multi-input", "id": "disable", "title": "Disable features", "content": "Disable any features that you don't need.", "value": [{"type": "checkbox", "id": "disable-channels-filter", "title": "Channels filter"}, {"type": "checkbox", "id": "disable-filters", "title": "Filters"}, {"type": "checkbox", "id": "disable-attachments", "title": "Attachments list"}, {"type": "checkbox", "id": "disable-tags", "title": "Tags"}, {"type": "checkbox", "id": "disable-notes", "title": "Notes"}]}, {"type": "multi-input", "id": "notes-settings", "title": "Notes settings", "content": "Manage the notes settings.", "help": "https://board.support/docs/#notes", "value": [{"type": "checkbox", "id": "notes-hide-name", "title": "Hide username"}]}, {"type": "multi-input", "id": "tags-settings", "title": "Tags settings", "content": "Manage the tags settings.", "help": "https://board.support/docs/#tags", "value": [{"type": "checkbox", "id": "tags-show", "title": "Display in conversation list"}, {"type": "checkbox", "id": "tags-starred", "title": "Starred tag"}]}, {"type": "repeater", "id": "tags", "title": "Tags", "content": "Add and manage tags.", "help": "https://board.support/docs/#tags", "items": [{"type": "text", "name": "Name", "id": "tag-name"}, {"type": "color-palette", "name": "Color", "id": "tag-color"}]}, {"type": "multi-input", "id": "transcript", "title": "Transcript", "content": "Transcript settings.", "help": "https://board.support/docs/#transcript", "value": [{"type": "select", "id": "transcript-type", "title": "Type", "value": [["csv", "CSV"], ["txt", "Text"]]}, {"type": "select", "id": "transcript-action", "title": "Button action", "value": [["download", "Download"], ["email", "Send to user's email"]]}, {"type": "textarea", "id": "transcript-message", "title": "Message"}]}, {"type": "repeater", "id": "user-table-extra-columns", "title": "Users table additional columns", "content": "Displays additional columns in the user table. Enter the name of the fields to add.", "help": "https://board.support/docs/#user-table-extra-columns", "items": [{"type": "text", "name": "Name", "id": "user-table-extra-slug"}]}, {"type": "repeater", "id": "saved-replies", "title": "Saved replies", "content": "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.", "help": "https://board.support/docs/#saved-replies", "items": [{"type": "text", "name": "Name", "id": "reply-name"}, {"type": "textarea", "name": "Text", "id": "reply-text"}]}, {"type": "text", "id": "custom-js", "title": "Custom JS", "content": "Enter the URL of a .js file, to load it automatically in the admin area."}, {"type": "text", "id": "custom-css", "title": "Custom CSS", "content": "Enter the URL of a .css file, to load it automatically in the admin area."}, {"type": "text", "id": "manifest-url", "title": "Manifest file URL", "content": "Use this option to change the PWA icon. See the docs for more details.", "help": "https://board.support/docs/#pwa"}, {"type": "color", "id": "color-admin-1", "title": "Primary color", "content": "Set the primary color of the admin area."}, {"type": "color", "id": "color-admin-2", "title": "Secondary color", "content": "Set the secondary color of the admin area."}], "notifications": [{"type": "multi-input", "id": "sound-settings", "title": "Sound settings", "content": "Play a sound for new messages and conversations.", "help": "https://board.support/docs/#sound", "value": [{"type": "checkbox", "id": "sound-settings-active", "title": "Active"}, {"type": "checkbox", "id": "sound-settings-active-admin", "title": "Active - admin"}, {"type": "select", "id": "sound-settings-volume", "title": "Volume", "value": [["", "<PERSON><PERSON><PERSON>"], [0.1, 1], [0.2, 2], [0.3, 3], [0.4, 4], [0.5, 5], [0.6, 6], [0.7, 7], [0.8, 8], [0.9, 9], [1, 10]]}, {"type": "select", "id": "sound-settings-volume-admin", "title": "Volume - admin", "value": [["", "<PERSON><PERSON><PERSON>"], [0.1, 1], [0.2, 2], [0.3, 3], [0.4, 4], [0.5, 5], [0.6, 6], [0.7, 7], [0.8, 8], [0.9, 9], [1, 10]]}, {"type": "select", "id": "sound-settings-repeat", "title": "Repeat", "value": [["", "<PERSON><PERSON><PERSON>"], [1, 2], [2, 3], [3, 4], [4, 5], [5, 6], [6, 7], [7, 8], [8, 9], [9, 10], [9999, "Infinite loop"]]}, {"type": "select", "id": "sound-settings-repeat-admin", "title": "Repeat - admin", "value": [["", "<PERSON><PERSON><PERSON>"], [1, 2], [2, 3], [3, 4], [4, 5], [5, 6], [6, 7], [7, 8], [8, 9], [9, 10], [9999, "Infinite loop"]]}, {"type": "text", "id": "sound-settings-file-admin", "title": "Audio file URL - admin"}]}, {"type": "checkbox", "id": "online-users-notification", "title": "Online users notification", "content": "Show a notification and play a sound when a new user is online.", "help": "https://board.support/docs/#users-info"}, {"type": "checkbox", "id": "away-mode", "title": "Away mode", "content": "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes."}, {"type": "select", "id": "desktop-notifications", "title": "Desktop notifications", "content": "Show a desktop notification when a new message is received.", "value": [["", "Disabled"], ["all", "Users and agents"], ["users", "Users only"], ["agents", "Agents only"]]}, {"type": "select", "id": "flash-notifications", "title": "Flash notifications", "content": "Show a browser tab notification when a new message is received.", "value": [["", "Disabled"], ["all", "Users and agents"], ["users", "Users only"], ["agents", "Agents only"]]}, {"type": "multi-input", "id": "push-notifications", "title": "Push notifications", "content": "Push notifications settings.", "help": "https://board.support/docs/#push", "value": [{"type": "select", "id": "push-notifications-provider", "title": "Provider", "value": [["pusher", "<PERSON><PERSON><PERSON>"], ["onesignal", "OneSignal"]]}, {"type": "checkbox", "id": "push-notifications-active", "title": "Active for agents"}, {"type": "checkbox", "id": "push-notifications-users-active", "title": "Active for users"}, {"type": "text", "id": "push-notifications-id", "title": "Instance ID"}, {"type": "password", "id": "push-notifications-key", "title": "Secret key"}, {"type": "text", "id": "push-notifications-sw-url", "title": "Service Worker URL"}, {"type": "text", "id": "push-notifications-onesignal-app-id", "title": "App ID"}, {"type": "password", "id": "push-notifications-onesignal-api-key", "title": "API key"}, {"type": "button", "id": "push-notifications-sw-path", "title": "Service Worker path", "button-text": "Get Service Worker path", "button-url": "#"}, {"type": "button", "id": "push-notifications-btn", "title": "Subscribe", "button-text": "Subscribe", "button-url": "#"}]}, {"type": "checkbox", "id": "notify-agent-email", "title": "Agent email notifications", "content": "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.", "help": "https://board.support/docs/#email"}, {"type": "checkbox", "id": "notify-user-email", "title": "User email notifications", "content": "Send an email to the user when an agent replies and the user is offline.", "help": "https://board.support/docs/#email"}, {"type": "checkbox", "id": "stop-notify-admins", "title": "Do not send email notifications to admins", "content": "Prevent admins from receiving email notifications."}, {"type": "checkbox", "id": "notify-email-cron", "title": "Email notifications via cron job", "content": "Enable this option if email notifications are sent via cron job.", "help": "https://board.support/docs/#email-cron"}, {"type": "multi-input", "setting": "emails", "id": "email-user", "title": "User notification email", "content": "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "value": [{"type": "text", "id": "email-user-subject", "title": "Subject"}, {"type": "textarea", "id": "email-user-content", "title": "Content"}]}, {"type": "multi-input", "setting": "emails", "id": "email-agent", "title": "Agent notification email", "content": "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "value": [{"type": "text", "id": "email-agent-subject", "title": "Subject"}, {"type": "textarea", "id": "email-agent-content", "title": "Content"}]}, {"type": "input-button", "id": "test-email-user", "title": "Send a user email notification", "content": "Send an email notification to the provided email address.", "button-text": "Send email"}, {"type": "input-button", "id": "test-email-agent", "title": "Send an agent email notification", "content": "Send an email notification to the provided email address.", "button-text": "Send email"}, {"type": "multi-input", "id": "email-server", "title": "SMTP", "content": "Outgoing SMTP server information.", "value": [{"type": "text", "id": "email-server-host", "title": "Host"}, {"type": "text", "id": "email-server-user", "title": "Username"}, {"type": "password", "id": "email-server-password", "title": "Password"}, {"type": "number", "id": "email-server-port", "title": "Port"}, {"type": "text", "id": "email-server-from", "title": "Sender email"}, {"type": "text", "id": "email-sender-name", "title": "Sender name"}, {"type": "button", "id": "email-server-troubleshoot", "title": "Troubleshoot", "button-url": "#", "button-text": "Troubleshoot problems"}]}, {"type": "repeater", "id": "email-piping", "title": "Email piping", "content": "Email piping server information and more settings.", "help": "https://board.support/docs/#email-piping", "items": [{"type": "checkbox", "id": "email-piping-active", "name": "Active"}, {"type": "text", "id": "email-piping-host", "name": "Host"}, {"type": "text", "id": "email-piping-user", "name": "Username"}, {"type": "password", "id": "email-piping-password", "name": "Password"}, {"type": "select", "id": "email-piping-port", "name": "Port", "value": [[993, 993], [995, 995], [110, 110], [143, 143]]}, {"type": "checkbox", "id": "email-piping-delimiter", "name": "Delimiter"}, {"type": "checkbox", "id": "email-piping-all", "name": "Convert all emails"}, {"type": "checkbox", "id": "email-piping-disable-cron", "name": "Disable cron job"}, {"type": "number", "id": "email-piping-department", "name": "Department ID"}, {"type": "text", "id": "email-piping-filters", "name": "Filters"}, {"type": "button", "id": "email-piping-sync", "name": "Synchronize emails", "button-url": "#", "button-text": "Synchronize now"}]}, {"type": "textarea", "setting": "emails", "id": "email-signature", "title": "Email signature", "content": "Set the default email signature that will be appended to automated emails and direct emails.", "multilingual": true}, {"type": "textarea", "setting": "emails", "id": "email-header", "title": "Email header", "content": "Set the default email header that will be prepended to automated emails and direct emails.", "multilingual": true}, {"type": "multi-input", "id": "sms", "title": "Text message notifications", "content": "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.", "help": "https://board.support/docs/#sms", "value": [{"type": "checkbox", "id": "sms-active-agents", "title": "Active for agents"}, {"type": "checkbox", "id": "sms-active-users", "title": "Active for users"}, {"type": "text", "id": "sms-user", "title": "Account SID"}, {"type": "password", "id": "sms-token", "title": "Token"}, {"type": "text", "id": "sms-sender", "title": "Sender number"}, {"type": "textarea", "id": "sms-message-agent", "title": "Agent message template"}, {"type": "textarea", "id": "sms-message-user", "title": "User message template"}, {"type": "button", "id": "sms-btn", "title": "Configuration URL", "button-text": "Get configuration URL", "button-url": "#"}]}, {"type": "input-button", "id": "test-sms-user", "title": "Send a user text message notification", "content": "Send a text message to the provided phone number.", "button-text": "Send text message"}, {"type": "input-button", "id": "test-sms-agent", "title": "Send an agent text message notification", "content": "Send a text message to the provided phone number.", "button-text": "Send text message"}, {"type": "upload-image", "id": "notifications-icon", "title": "Notifications icon", "content": "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.", "background-size": "auto 64px"}], "users": [{"type": "select", "id": "registration-required", "title": "Require registration", "content": "Require the user registration or login before start a chat. To enable the login area the password field must be included.", "value": [["", "Disabled"], ["registration-login", "Registration and login form"], ["registration", "Registration form"], ["login", "Login form"]], "help": "https://board.support/docs/#registration"}, {"type": "checkbox", "id": "registration-timetable", "title": "Disable registration during office hours", "content": "Enable the registration outside of scheduled office hours only."}, {"type": "checkbox", "id": "registration-offline", "title": "Disable registration if agents online", "content": "Enable the registration only if all agents are offline."}, {"type": "text", "id": "registration-link", "title": "Registration link", "content": "Redirect the user to the registration link instead of showing the registration form."}, {"type": "text", "id": "login-verification-url", "title": "Login verification URL", "content": "Send login details to the specified URL and allow access only if the response is positive.", "help": "https://board.support/docs/#login-verification-url"}, {"type": "multi-input", "id": "registration", "title": "Registration form", "content": "User registration form information.", "value": [{"type": "text", "id": "registration-title", "title": "Form title"}, {"type": "textarea", "id": "registration-msg", "title": "Form message"}, {"type": "textarea", "id": "registration-success", "title": "Success message"}, {"type": "text", "id": "registration-btn-text", "title": "Button text"}, {"type": "text", "id": "registration-terms-link", "title": "Terms link"}, {"type": "text", "id": "registration-privacy-link", "title": "Privacy link"}]}, {"type": "multi-input", "id": "login", "title": "Login form", "content": "User login form information.", "value": [{"type": "text", "id": "login-title", "title": "Form title"}, {"type": "textarea", "id": "login-msg", "title": "Form message"}]}, {"type": "multi-input", "id": "registration-fields", "title": "Registration fields", "content": "Choose which fields to include in the registration form. The name field is included by default.", "value": [{"type": "checkbox", "id": "reg-email", "title": "Email"}, {"type": "checkbox", "id": "reg-required-email", "title": "Required"}, {"type": "checkbox", "id": "reg-phone", "title": "Phone"}, {"type": "checkbox", "id": "reg-required-phone", "title": "Required"}, {"type": "checkbox", "id": "reg-last-name", "title": "Last name"}, {"type": "checkbox", "id": "reg-required-last-name", "title": "Required"}, {"type": "checkbox", "id": "reg-profile-img", "title": "Profile image"}, {"type": "checkbox", "id": "reg-required-profile-img", "title": "Required"}, {"type": "checkbox", "id": "reg-city", "title": "City"}, {"type": "checkbox", "id": "reg-required-city", "title": "Required"}, {"type": "checkbox", "id": "reg-country", "title": "Country"}, {"type": "checkbox", "id": "reg-required-country", "title": "Required"}, {"type": "checkbox", "id": "reg-language", "title": "Language"}, {"type": "checkbox", "id": "reg-required-language", "title": "Required"}, {"type": "checkbox", "id": "reg-birthday", "title": "Birthday"}, {"type": "checkbox", "id": "reg-required-birthday", "title": "Required"}, {"type": "checkbox", "id": "reg-company", "title": "Company"}, {"type": "checkbox", "id": "reg-required-company", "title": "Required"}, {"type": "checkbox", "id": "reg-website", "title": "Website"}, {"type": "checkbox", "id": "reg-required-website", "title": "Required"}]}, {"type": "repeater", "id": "user-additional-fields", "title": "Custom fields", "content": "Add custom fields to the user profile details.", "items": [{"type": "text", "name": "Name", "id": "extra-field-name"}, {"type": "text", "name": "Slug", "id": "extra-field-slug"}, {"type": "checkbox", "name": "Required", "id": "extra-field-required"}]}, {"type": "checkbox", "id": "registration-extra", "title": "Include custom fields", "content": "Include custom fields in the registration form."}, {"type": "checkbox", "id": "registration-user-details-success", "title": "User details in success message", "content": "Append the registration user details to the success message."}, {"type": "checkbox", "id": "registration-otp", "title": "Email verification", "content": "Enable email verification with OTP."}, {"type": "multi-input", "setting": "email-otp", "id": "email-otp", "title": "Email verification email", "content": "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.", "value": [{"type": "text", "id": "email-otp-subject", "title": "Subject"}, {"type": "textarea", "id": "email-otp-content", "title": "Content"}]}, {"type": "checkbox", "id": "visitors-registration", "title": "Register all visitors", "content": "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered."}, {"type": "multi-input", "id": "envato-validation", "title": "Envato purchase code validation", "content": "Request a valid Envato purchase code for registration.", "help": "https://board.support/docs/#envato-validation", "value": [{"type": "checkbox", "id": "envato-validation-active", "title": "Active"}, {"type": "password", "id": "envato-validation-token", "title": "Token"}, {"type": "text", "id": "envato-validation-product-ids", "title": "Product IDs"}, {"type": "checkbox", "id": "envato-validation-extended-license-only", "title": "Allow only extended licenses"}]}, {"type": "select", "id": "phone-code", "title": "Single phone country code", "content": "Force users to use only one phone country code.", "value": [["", ""], ["+1", "+1"], ["+7", "+7"], ["+20", "+20"], ["+27", "+27"], ["+30", "+30"], ["+31", "+31"], ["+32", "+32"], ["+33", "+33"], ["+34", "+34"], ["+36", "+36"], ["+39", "+39"], ["+40", "+40"], ["+41", "+41"], ["+43", "+43"], ["+44", "+44"], ["+45", "+45"], ["+46", "+46"], ["+47", "+47"], ["+48", "+48"], ["+49", "+49"], ["+51", "+51"], ["+52", "+52"], ["+53", "+53"], ["+54", "+54"], ["+55", "+55"], ["+56", "+56"], ["+57", "+57"], ["+58", "+58"], ["+60", "+60"], ["+61", "+61"], ["+62", "+62"], ["+63", "+63"], ["+64", "+64"], ["+65", "+65"], ["+66", "+66"], ["+81", "+81"], ["+82", "+82"], ["+84", "+84"], ["+86", "+86"], ["+90", "+90"], ["+91", "+91"], ["+92", "+92"], ["+93", "+93"], ["+94", "+94"], ["+95", "+95"], ["+98", "+98"], ["+211", "+211"], ["+212", "+212"], ["+213", "+213"], ["+216", "+216"], ["+218", "+218"], ["+220", "+220"], ["+221", "+221"], ["+222", "+222"], ["+223", "+223"], ["+224", "+224"], ["+225", "+225"], ["+226", "+226"], ["+227", "+227"], ["+228", "+228"], ["+229", "+229"], ["+230", "+230"], ["+231", "+231"], ["+232", "+232"], ["+233", "+233"], ["+234", "+234"], ["+235", "+235"], ["+236", "+236"], ["+237", "+237"], ["+238", "+238"], ["+239", "+239"], ["+240", "+240"], ["+241", "+241"], ["+242", "+242"], ["+243", "+243"], ["+244", "+244"], ["+245", "+245"], ["+246", "+246"], ["+248", "+248"], ["+249", "+249"], ["+250", "+250"], ["+251", "+251"], ["+252", "+252"], ["+253", "+253"], ["+254", "+254"], ["+255", "+255"], ["+256", "+256"], ["+257", "+257"], ["+258", "+258"], ["+260", "+260"], ["+261", "+261"], ["+262", "+262"], ["+263", "+263"], ["+264", "+264"], ["+265", "+265"], ["+266", "+266"], ["+267", "+267"], ["+268", "+268"], ["+269", "+269"], ["+290", "+290"], ["+291", "+291"], ["+297", "+297"], ["+298", "+298"], ["+299", "+299"], ["+350", "+350"], ["+351", "+351"], ["+352", "+352"], ["+353", "+353"], ["+354", "+354"], ["+355", "+355"], ["+356", "+356"], ["+357", "+357"], ["+358", "+358"], ["+359", "+359"], ["+370", "+370"], ["+371", "+371"], ["+372", "+372"], ["+373", "+373"], ["+374", "+374"], ["+375", "+375"], ["+376", "+376"], ["+377", "+377"], ["+378", "+378"], ["+379", "+379"], ["+380", "+380"], ["+381", "+381"], ["+382", "+382"], ["+385", "+385"], ["+386", "+386"], ["+387", "+387"], ["+389", "+389"], ["+420", "+420"], ["+421", "+421"], ["+423", "+423"], ["+500", "+500"], ["+501", "+501"], ["+502", "+502"], ["+503", "+503"], ["+504", "+504"], ["+505", "+505"], ["+506", "+506"], ["+507", "+507"], ["+508", "+508"], ["+509", "+509"], ["+590", "+590"], ["+591", "+591"], ["+592", "+592"], ["+593", "+593"], ["+594", "+594"], ["+595", "+595"], ["+596", "+596"], ["+597", "+597"], ["+598", "+598"], ["+599", "+599"], ["+670", "+670"], ["+672", "+672"], ["+673", "+673"], ["+674", "+674"], ["+675", "+675"], ["+676", "+676"], ["+677", "+677"], ["+678", "+678"], ["+679", "+679"], ["+680", "+680"], ["+681", "+681"], ["+682", "+682"], ["+683", "+683"], ["+685", "+685"], ["+686", "+686"], ["+687", "+687"], ["+688", "+688"], ["+689", "+689"], ["+690", "+690"], ["+691", "+691"], ["+692", "+692"], ["+850", "+850"], ["+852", "+852"], ["+853", "+853"], ["+855", "+855"], ["+856", "+856"], ["+870", "+870"], ["+880", "+880"], ["+886", "+886"], ["+960", "+960"], ["+961", "+961"], ["+962", "+962"], ["+963", "+963"], ["+964", "+964"], ["+965", "+965"], ["+966", "+966"], ["+967", "+967"], ["+968", "+968"], ["+970", "+970"], ["+971", "+971"], ["+972", "+972"], ["+973", "+973"], ["+974", "+974"], ["+975", "+975"], ["+976", "+976"], ["+977", "+977"], ["+992", "+992"], ["+993", "+993"], ["+994", "+994"], ["+995", "+995"], ["+996", "+996"], ["+998", "+998"], ["+1-242", "+1-242"], ["+1-246", "+1-246"], ["+1-264", "+1-264"], ["+1-268", "+1-268"], ["+1-284", "+1-284"], ["+1-340", "+1-340"], ["+1-345", "+1-345"], ["+1-441", "+1-441"], ["+1-473", "+1-473"], ["+1-649", "+1-649"], ["+1-664", "+1-664"], ["+1-670", "+1-670"], ["+1-671", "+1-671"], ["+1-684", "+1-684"], ["+1-758", "+1-758"], ["+1-767", "+1-767"], ["+1-784", "+1-784"], ["+1-809", "+1-809"], ["+1-868", "+1-868"], ["+1-869", "+1-869"], ["+1-876", "+1-876"], ["+44-1481", "+44-1481"], ["+44-1534", "+44-1534"], ["+44-1624", "+44-1624"], ["+358-18", "+358-18"]]}, {"type": "text", "id": "visitor-prefix", "title": "Visitor name prefix", "content": "<PERSON>ame the visitor name prefix. De<PERSON><PERSON> is 'User'."}, {"type": "text", "id": "visitor-default-name", "title": "Visitor default name", "content": "Set the default username to use in bot messages and emails when the user doesn't have a name."}, {"type": "checkbox", "id": "visitor-autodata", "title": "Full visitor details", "content": "Save useful information like user country and language also for visitors."}, {"type": "checkbox", "id": "display-users-thumb", "title": "Display user's profile image", "content": "Display the user's profile image within the chat."}, {"type": "checkbox", "id": "hide-agents-thumb", "title": "Hide agent's profile image", "content": "Hide the agent's profile image within the chat."}, {"type": "select", "id": "sender-name", "title": "Show sender's name", "content": "Show the sender's name in every message.", "value": [["", "Disabled"], ["chat", "Cha<PERSON>"], ["chat-admin", "Chat and admin"]]}, {"type": "text", "id": "bot-name", "title": "Bot name", "content": "<PERSON>ame the chat bot. <PERSON><PERSON><PERSON> is '<PERSON><PERSON>'."}, {"type": "upload-image", "id": "bot-image", "title": "Bo<PERSON> profile image", "content": "Set a profile image for the chat bot.", "background-size": "auto 64px"}, {"type": "button", "id": "delete-leads", "title": "Delete leads", "content": "Delete all leads and all messages and conversations linked to them.", "button-text": "Delete leads", "button-url": "#"}, {"type": "button", "id": "sb-import-users", "title": "Import users", "content": "Import users from a CSV file.", "button-text": "Import users", "button-url": "#", "help": "https://board.support/docs/#import-users"}], "design": [{"type": "color", "id": "color-1", "title": "Primary color", "content": "Set the primary color of the chat widget."}, {"type": "color", "id": "color-2", "title": "Secondary color", "content": "Set the secondary color of the chat widget."}, {"type": "color", "id": "color-3", "title": "Tertiary color", "content": "Set the tertiary color of the chat widget."}, {"type": "select", "id": "chat-position", "title": "Chat position", "content": "Set the position of the chat widget.", "value": [["right", "Right"], ["left", "Left"]]}, {"type": "text", "id": "header-headline", "title": "Header title", "content": "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent."}, {"type": "text", "id": "header-msg", "title": "Header message", "content": "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent."}, {"type": "select", "id": "header-type", "title": "Header type", "content": "Set the header appearance.", "value": [["agents", "Agents"], ["brand", "Brand"], ["minimal", "Minimal"]]}, {"type": "checkbox", "id": "header-name", "title": "Display user name in header", "content": "Replace the header title with the user's first name and last name when available."}, {"type": "upload-image", "id": "brand-img", "title": "Header brand image", "content": "Display a brand image in the header area. This only applies for the 'brand' header type.", "background-size": "auto 64px"}, {"type": "upload-image", "id": "header-img", "title": "Header background image", "content": "Apply a custom background image for the header area."}, {"type": "upload-image", "id": "chat-icon", "title": "Chat button icon", "content": "Change the chat button image with a custom one.", "background-size": "auto 64px"}, {"type": "select-images", "id": "chat-sb-icons", "title": "Built-in chat button icons", "content": "Change the chat button image with a custom one.", "images": ["button-chat-2.svg", "button-chat-3.svg", "button-chat-4.svg", "button-chat-5.svg"]}, {"type": "select-images", "id": "chat-background", "title": "Chat background", "content": "Choose a background texture for the chat header and conversation area.", "images": ["textures/square/texture-1.png", "textures/square/texture-2.png", "textures/square/texture-3.png", "textures/square/texture-4.png", "textures/square/texture-5.png", "textures/square/texture-6.png", "textures/square/texture-7.png", "textures/square/texture-8.png", "textures/square/texture-9.png"]}, {"type": "multi-input", "id": "chat-button-offset", "title": "Chat button offset", "content": "Adjust the chat button position. Values are in px.", "value": [{"type": "number", "id": "chat-button-offset-top", "title": "Top"}, {"type": "number", "id": "chat-button-offset-bottom", "title": "Bottom"}, {"type": "number", "id": "chat-button-offset-right", "title": "Right"}, {"type": "number", "id": "chat-button-offset-left", "title": "Left"}, {"type": "select", "id": "chat-button-offset-mobile", "title": "Apply to", "value": [["all", "All"], ["desktop", "Only desktop"], ["mobile", "Only mobile devices"]]}]}], "miscellaneous": [{"type": "multi-input", "id": "webhooks", "title": "Webhooks", "content": "Webhooks are information sent in background to a unique URL defined by you when something happens.", "help": "https://board.support/docs/api/web#Webhooks", "value": [{"type": "checkbox", "id": "webhooks-active", "title": "Active"}, {"type": "text", "id": "webhooks-url", "title": "URL"}, {"type": "password", "id": "webhooks-key", "title": "Secret key"}, {"type": "text", "id": "webhooks-allowed", "title": "Active webhooks"}]}, {"type": "password", "id": "envato-purchase-code", "title": "Envato Purchase Code", "content": "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.", "help": "https://help.market.envato.com/hc/en-us/articles/202822600-Where-Is-My-Purchase-Code-"}, {"type": "checkbox", "id": "auto-updates", "title": "Automatic updates", "content": "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required."}, {"type": "multi-input", "id": "pusher", "title": "<PERSON><PERSON><PERSON>", "content": "Improve chat performance with <PERSON><PERSON><PERSON> and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.", "help": "https://board.support/docs/#pusher", "value": [{"type": "checkbox", "id": "pusher-active", "title": "Active"}, {"type": "text", "id": "pusher-id", "title": "App ID"}, {"type": "text", "id": "pusher-key", "title": "App Key"}, {"type": "password", "id": "pusher-secret", "title": "App Secret"}, {"type": "text", "id": "pusher-cluster", "title": "App Cluster"}]}, {"type": "multi-input", "id": "newsletter", "title": "Newsletter", "content": "Subscribe users to your preferred newsletter service when they provide an email.", "help": "https://board.support/docs/#newsletter", "value": [{"type": "checkbox", "id": "newsletter-active", "title": "Active"}, {"type": "select", "id": "newsletter-service", "title": "Service", "value": [["mailchimp", "Mailchimp"], ["sendinblue", "Brevo"], ["sendgrid", "SendGrid"], ["elasticemail", "Elastic Email"], ["campaignmonitor", "Campaign Monitor"], ["hubspot", "Hubspot"], ["moosend", "Moosend"], ["getresponse", "GetResponse"], ["convertkit", "ConvertKit"], ["activecampaign", "ActiveCampaign"], ["mailerlite", "MailerLite"], ["mailjet", "Mailjet"], ["sendy", "Sendy"], ["sendfox", "SendFox"]]}, {"type": "text", "id": "newsletter-list-id", "title": "List ID"}, {"type": "password", "id": "newsletter-key", "title": "Key"}]}, {"type": "timetable", "id": "timetable", "title": "Scheduled office hours", "content": "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.", "help": "https://board.support/docs/#office-hours"}, {"type": "number", "id": "timetable-utc", "title": "UTC offset", "content": "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty."}, {"type": "multi-input", "id": "departments-settings", "title": "Departments settings", "content": "Manage here the departments settings.", "help": "https://board.support/docs/#departments", "value": [{"type": "checkbox", "id": "departments-dashboard", "title": "Display in dashboard"}, {"type": "checkbox", "id": "departments-images", "title": "Display images"}, {"type": "checkbox", "id": "departments-show-list", "title": "Display in conversation list"}, {"type": "checkbox", "id": "departments-force-one", "title": "One conversation per department"}, {"type": "text", "id": "departments-label", "title": "Label"}, {"type": "text", "id": "departments-single-label", "title": "Single label"}, {"type": "text", "id": "departments-title", "title": "Dashboard title"}]}, {"type": "repeater", "id": "departments", "title": "Departments", "content": "Add and manage additional support departments.", "items": [{"type": "text", "name": "Name", "id": "department-name"}, {"type": "color-palette", "name": "Color", "id": "department-color"}, {"type": "upload-image", "name": "Image", "id": "department-image"}, {"type": "auto-id", "id": "department-id"}]}, {"type": "multi-input", "id": "queue", "title": "Queue", "content": "Distribute conversations proportionately between agents and notify visitors of their position within the queue.", "help": "https://board.support/docs/#queue-routing", "value": [{"type": "checkbox", "id": "queue-active", "title": "Active"}, {"type": "number", "id": "queue-concurrent-chats", "title": "Concurrent chats"}, {"type": "number", "id": "queue-response-time", "title": "Response time"}, {"type": "checkbox", "id": "queue-sound", "title": "Sound"}, {"type": "textarea", "id": "queue-message", "title": "Message"}, {"type": "textarea", "id": "queue-message-success", "title": "Your turn message"}]}, {"type": "multi-input", "id": "routing", "title": "Routing", "content": "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.", "help": "https://board.support/docs/#queue-routing", "value": [{"type": "checkbox", "id": "routing-active", "title": "Active"}, {"type": "checkbox", "id": "routing-disable-status-check", "title": "Disable online status check"}]}, {"type": "multi-input", "id": "agent-hide-conversations", "title": "Hide conversations of other agents", "content": "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.", "help": "https://board.support/docs/#manual-routing", "value": [{"type": "checkbox", "id": "agent-hide-conversations-active", "title": "Active"}, {"type": "checkbox", "id": "agent-hide-conversations-menu", "title": "Agents menu"}, {"type": "checkbox", "id": "agent-hide-conversations-routing", "title": "Routing if offline"}, {"type": "checkbox", "id": "agent-hide-conversations-view", "title": "View unassigned conversations"}]}, {"type": "repeater", "setting": "rich-messages", "id": "rich-messages", "title": "Rich messages", "content": "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.", "help": "https://board.support/docs/#rich-messages", "items": [{"type": "text", "name": "Name", "id": "rich-message-name"}, {"type": "textarea", "name": "Content", "id": "rich-message-content"}]}, {"type": "text", "id": "cookie-domain", "title": "Cookie domain", "content": "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains."}, {"type": "multi-input", "id": "performance", "title": "Performance optimization", "content": "Disable features you don't use and improve the chat performance.", "value": [{"type": "checkbox", "id": "performance-minify", "title": "Minify JS"}, {"type": "checkbox", "id": "performance-reports", "title": "Reports"}, {"type": "checkbox", "id": "performance-articles", "title": "Articles"}]}, {"type": "multi-input", "id": "amazon-s3", "title": "Amazon S3", "content": "Upload attachments to Amazon S3.", "help": "https://board.support/docs/#aws", "value": [{"type": "checkbox", "id": "amazon-s3-active", "title": "Active"}, {"type": "password", "id": "amazon-s3-access-key", "title": "Access key"}, {"type": "password", "id": "amazon-s3-secret-access-key", "title": "Secret access key"}, {"type": "text", "id": "amazon-s3-bucket-name", "title": "Bucket name"}, {"type": "text", "id": "amazon-s3-region", "title": "Region"}]}, {"type": "checkbox", "id": "logs", "title": "Logs", "content": "Enable logging of agent activity.", "help": "https://board.support/docs/#logs"}, {"type": "button", "id": "system-requirements", "title": "System requirements", "content": "Check the server configurations and make sure it has all the requirements.", "help": "https://board.support/docs/#requirements", "button-text": "Check Requirements", "button-url": "#"}, {"type": "text", "id": "ip-ban", "title": "IP banning", "content": "Restrict chat access by blocking IPs. List IPs with commas."}, {"type": "button", "id": "sb-path", "title": "Support Board Path", "content": "This returns the Support Board path of your server.", "button-text": "Get Path", "button-url": "#"}, {"type": "button", "id": "sb-url", "title": "Installation URL", "content": "This returns your installation URL.", "button-text": "Get URL", "button-url": "#"}, {"type": "button", "id": "sb-export-settings", "title": "Export settings", "content": "Export all settings.", "button-text": "Export settings", "button-url": "#"}, {"type": "button", "id": "sb-import-settings", "title": "Import settings", "content": "Import all settings.", "button-text": "Import settings", "button-url": "#"}], "articles": [{"type": "checkbox", "id": "articles-active", "title": "Display in dashboard", "content": "Show the articles panel on the chat dashboard."}, {"type": "checkbox", "id": "articles-categories", "title": "Display categories", "content": "Show the categories instead of the articles list."}, {"type": "text", "id": "articles-title", "title": "Panel title", "content": "Set the articles panel title. De<PERSON><PERSON> is 'Help Center'."}, {"type": "text", "id": "articles-button-link", "title": "Articles button link", "content": "Customize the link for the 'All articles' button."}, {"type": "text", "id": "articles-page-url", "title": "Articles page URL", "content": "Enter the URL of the articles page."}, {"type": "checkbox", "id": "articles-url-rewrite", "title": "URL rewrite", "content": "Change the default URLs with user-friendly URLs.", "help": "https://board.support/docs/#articles-url-rewrite"}]}