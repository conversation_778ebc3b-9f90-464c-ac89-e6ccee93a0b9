"use strict";!function(e){function t(){c.addClass("sb-collapsing"),setTimeout(function(){c.removeClass("sb-collapsing")},1e3)}function s(e){if(void 0===e)return window.sb_current_user;window.sb_current_user=e}function i(e){return SBF.translate(e)}function n(){0!=s()&&e(m).setProfile("#"==s().get("last_name").charAt(0)?i("Account"):s().name)}function a(e=!1){let t=e&&"title"in e.details&&!SBF.null(e.details.title)?e.get("title"):SBF.setting("tickets_conversation_name");r(t&&-1!=t?t:s().name)}function o(){let t="",n=SBForm.getAll(f),a="department"in n?n.department[0]:null,o=[];SBChat.clear(),g.sbActive(!1);for(var l in n)n[l][1]&&n[l][0]&&(t+=`*${i(n[l][1])}*\n${"department"==l?f.find("#department li.sb-active").html():n[l][0]}\n\n`);t+=g.find("textarea").val().trim(),f.find(".sb-attachments > div").each(function(){o.push([e(this).attr("data-name"),e(this).attr("data-value")])}),s()?SBChat.newConversation(2,-1,t,o,a,null,function(){F.welcome()}):SBChat.addUserAndLogin(()=>{SBChat.newConversation(2,-1,t,o,a,null,function(){F.welcome()})})}function l(){c.on("click",".sb-btn-collapse",function(){t(),c.find(".sb-panel-"+(e(this).hasClass("sb-left")?"left":"right")).toggleClass("sb-collapsed"),e(this).toggleClass("sb-active")}),g.on("focus focusout","textarea",function(){e(this).parent().parent().toggleClass("sb-focus")}),w||(g.on("click",".sb-btn-emoji",function(){let t="new-ticket"==p?[f,"padding-top",415]:[c,"margin-top",335];if(g.find(".sb-emoji").sbActive()){let s=e(this).offset().top+e(t[0])[0].scrollTop-window.scrollY,i=c.offset().top-window.scrollY;s-i<380&&e(t[0]).css(t[1],t[2]-(s-i)+"px")}else e(t[0]).css(t[1],"")}),g.on("click",".sb-emoji-list > ul > li",function(){let t="new-ticket"==p?[f,"padding-top",415]:[c,"margin-top",335];e(t[0]).css(t[1],"")})),d.on("click","> .sb-top .sb-close",function(){F.showPanel()}),d.on("click",".sb-create-ticket",function(){let t=!1;if(g.removeClass("sb-error"),SBForm.errors(f)&&(SBForm.showErrorMessage(f,"Please fill in all the required fields."),t=!0),g.find("textarea").val().trim()||g.hasClass("sb-audio-message-active")||(t||SBForm.showErrorMessage(f,"Please write a message."),g.addClass("sb-error"),t=!0),!t&&!SBChat.is_busy){if(SBChat.busy(!0),SBF.setting("tickets_recaptcha"))return void grecaptcha.ready(function(){grecaptcha.execute(SBF.setting("tickets_recaptcha"),{action:"submit"}).then(function(t){SBF.ajax({function:"recaptcha",token:t},t=>{!0===t?(o(),e(".grecaptcha-badge").hide()):SBChat.busy(!1)})})});o()}}),c.on("click",".sb-new-ticket",function(){F.showPanel("new-ticket")}),u.on("click","li",function(){SBChat.clear(),F.selectConversation(e(this).attr("data-conversation-id")),b.sbLoading(!0),w&&c.find(".sb-panel-left").addClass("sb-collapsed")}),c.find(".sb-panel-left .sb-search-btn input").on("input",function(){let t=e(this).val(),n=e(this).prev();SBF.search(t,()=>{if(t.length>1){if(function(t){return!!e(t).sbLoading()||(e(t).sbLoading(!0),!1)}(n))return;SBF.ajax({function:"search-user-conversations",search:t},e=>{n.sbLoading(!1);let t=[],a=e.length;for(var o=0;o<a;o++)SBF.setting("tickets_close")||t.push(new SBConversation([new SBMessage(e[o])],e[o]));u.html(a?s().getConversationsCode(t):"<p>"+i("No results found.")+"</p>")})}else SBChat.populateConversations()})}),c.on("click",".sb-panel-left .sb-search-btn i",function(){SBF.searchClear(this,()=>{SBChat.populateConversations()})}),f.on("click",".sb-login-area",function(){F.showPanel("login")}),f.on("click",".sb-registration-area",function(){F.showPanel("registration")}),c.on("click",'.sb-profile-menu [data-value="edit-profile"]',function(){F.showPanel("edit-profile")}),c.on("click",'.sb-profile-menu [data-value="logout"]',function(){SBF.logout(!1),F.showPanel("login")}),f.on("click","> .sb-buttons .sb-submit",function(){if(!e(this).sbLoading()){let i=SBForm.getAll(f),a=SBForm.getAll(f.find(".sb-form-extra")),o="edit-profile"==p;for(var t in i)i[t]=i[t][0];SBForm.errors(f)?SBForm.showErrorMessage(f,SBForm.getRegistrationErrorMessage(f)):(e(this).sbLoading(!0),i.user_type="user",SBF.ajax({function:o||s()?"update-user":"add-user-and-login",settings:i,settings_extra:a},t=>{if(t&&!SBF.errorValidation(t)){if(SBF.loginCookie(t[1]),s()){for(var l in i)s().set(l,i[l][0]);for(var l in a)s().setExtra(l,a[l][0])}else{s(new SBUser(t[0]));for(var l in a)s().setExtra(l,a[l][0]);SBPusher.start(),SBChat.initChat()}n(),o?F.showPanel():(SBF.event("SBRegistrationForm",{user:i}),SBF.event("SBNewEmailAddress",{name:s().name,email:s().get("email")}),F.showPanel("new-ticket")),SBF.setting("wp_registration")&&"email"in i&&"password"in i?(console.log(i),SBApps.wordpress.ajax("wp_registration",{user_id:t[0].id,first_name:t[0].first_name,last_name:t[0].last_name,password:i.password[0],email:i.email[0]})):"wp"==SBF.setting("wp_users_system")&&SBApps.wordpress.ajax("wp_login",{user:i.email[0],password:i.password[0]})}else SBForm.showErrorMessage(f,SBForm.getRegistrationErrorMessage(t,"response"));e(this).sbLoading(!1)}))}}),f.on("click",".sb-submit-login",function(){SBF.loginForm(this,f,e=>{s(new SBUser(e[0])),n(),SBChat.populateConversations(e=>{0==e.length?(c.addClass("sb-no-conversations"),F.showPanel("new-ticket")):(SBChat.openConversation(e[0].id),F.showPanel())})})})}function r(t){e(h).html(t).sbActive(t)}var c,d,b,f,g,p,u,h,v,m,S,B={},C={},w=e(window).width()<426,k=!1,F={showPanel:function(t="",s=!1){let n=p;switch(p=t,c.addClass("sb-panel-active sb-load").removeClass("sb-panel-form").attr("data-panel",t),k&&k.hide(),t){case"privacy":SBF.ajax({function:"get-block-setting",value:"privacy"},e=>{r(i(e.title)),f.append(`<div class="sb-privacy sb-init-form" data-decline="${i(e.decline.replace(/"/g,""))}"><div class="sb-text">${i(e.message)}</div>`+(""!=e.link?`<a target="_blank" href="${e.link}">${i(e["link-name"])}</a>`:"")+`<div class="sb-buttons"><a class="sb-btn sb-approve">${i(e["btn-approve"])}</a><a class="sb-btn sb-decline">${i(e["btn-decline"])}</a></div></div>`)}),this.showSidePanels(!1);break;case"articles":r(i(0==s?"Articles":s)),this.showSidePanels(!1);break;case"edit-profile":case"login":case"registration":let l="edit-profile"==t;this.showSidePanels(!1),c.addClass("sb-panel-form"),t in C?(f.html(C[t]),r(i(f.find(".sb-top").html()))):(SBF.ajax({function:"get-rich-message",name:(l?"registration":t)+"-tickets"},e=>{f.html(e);let s=f.find(".sb-top").html();l&&f.find(".sb-top").html(i("Edit profile")),r(i(s)),setTimeout(function(){r(i(s))},300),f.find(".sb-link-area").insertAfter(".sb-buttons"),f.find(".sb-info").insertBefore(".sb-buttons"),C[t]=f.html()}),f.html('<div class="sb-loading"></div>'));break;case"new-ticket":let u={title:"Title",message:"Message",panel:"Create a new ticket",button:"Create a new ticket"};if(this.showSidePanels(!1),SBF.setting("tickets_names")){let e=SBF.setting("tickets_names");for(var o in e)e[o]&&(u[o.replace("tickets-names-","")]=e[o])}r(i(u.panel)),f.html(`<div class="sb-info"></div><div class="sb-input sb-input-text sb-ticket-title"><span>${i(u.title)}</span><input type="text" required></div>${c.find(".sb-ticket-fields").html()}<div class="sb-input sb-editor-cnt"><span>${i(u.message)}</span></div><div class="sb-btn sb-icon sb-create-ticket"><i class="sb-icon-plus"></i>${i(u.button)}</div>`),d.find(".sb-editor-cnt").append(g),SBF.setting("tickets_recaptcha")&&(k?k.show():e.getScript("https://www.google.com/recaptcha/api.js?render="+SBF.setting("tickets_recaptcha"),()=>{setTimeout(()=>{k=e(".grecaptcha-badge")},500)}));break;default:this.showSidePanels(!0),"new-ticket"==n&&(g.find("textarea").val(""),g.sbActive(!1).removeClass("sb-error"),b.after(g)),f.html(""),c.removeClass("sb-panel-active sb-load").removeAttr("data-panel"),a(SBChat.conversation)}SBF.event("SBPanelActive",t),setTimeout(function(){c.removeClass("sb-load")},300)},showSidePanels:function(s=!0){let i=c.find(".sb-btn-collapse");if(t(),!s||S>800){let e=c.find(".sb-panel-left,.sb-panel-right");s?e.removeClass("sb-collapsed"):e.addClass("sb-collapsed")}else S<=800&&e(i).sbActive(!0);e(i).css("display",s?"":"none")},setAgent:function(t){let s=c.find(".sb-agent-label").sbActive(!1);if(SBChat.agent_id=t,t in B){let i=B[t];e(v).setProfile(i.name,i.image).sbActive(!0),SBChat.updateUsersActivity(),"details"in i&&SBF.getLocationTimeString(i.extra,t=>{e(s).html((i.get("flag")?`<img src="${SB_URL}/media/flags/${i.get("flag")}">`:'<i class="sb-icon sb-icon-marker"></i>')+t).sbActive(!0)})}else SBF.ajax({function:"get-agent",agent_id:t},e=>{0!=e&&(B[t]=new SBUser(e),this.setAgent(t))})},activateConversation:function(t){if(t instanceof SBConversation){let n=SBChat.lastAgent(),o=["id","creation_time","last_update"],l="";this.selectConversation(t.id),a(t),c.find(".sb-panel-right .sb-scroll-area > div").sbActive(!1),n?(e(v).setProfile(n.full_name,n.profile_image),this.setAgent(n.user_id),setTimeout(()=>{SBChat.updateUsersActivity()},300)):e(v).sbActive(!1),""!=t.get("department")&&SBChat.getDepartmentCode(t.get("department"),t=>{let s=c.find(".sb-department");e(s).html(`<span class="sb-title">${e(s).data("label")}</span>${t}`).sbActive(!0)});for(s=0;s<o.length;s++){let e;switch(o[s]){case"id":e=["padlock",i("Ticket ID"),t.id];break;case"creation_time":e=["calendar",i("Creation time"),SBF.beautifyTime(t.get("creation_time"),!0)];break;case"last_update":e=["reload",i("Last update"),SBF.beautifyTime(t.getLastMessage()?t.getLastMessage().get("creation_time"):t.get("last_update_time"),!0)]}l+=`<div data-id="${o[s]}"><i class="sb-icon sb-icon-${e[0]}"></i><span>${e[1]}</span><div>${e[2]}</div></div>`}c.find(".sb-ticket-details").html(l);let r=t.getAttachments();l="";for(var s=0;s<r.length;s++)l+=`<a href="${r[s][1]}" target="_blank"><i class="sb-icon sb-icon-file"></i>${r[s][0]}</a>`;c.find(".sb-conversation-attachments").html((l?`<div class="sb-title">${i("Attachments")}</div>`:"")+l),b.sbLoading(!1)}else SBF.error("Value not of type SBConversation","activateConversation")},selectConversation:function(e){let t=u.find(`[data-conversation-id="${e}"]`);u.find("> li").sbActive(!1),1==t.attr("data-conversation-status")&&t.attr("data-conversation-status",0),t.find("[data-count]").remove(),t.sbActive(!0)},getActiveConversation:function(e=""){let t=u.find(" > .sb-active");return!!t.length&&("ID"==e?t.attr("data-conversation-id"):t)},welcome:function(){let e=SBF.setting("tickets_welcome_message");e&&!SBF.storage("tickets-welcome")&&setTimeout(()=>{SBChat.sendMessage(SBF.setting("bot_id"),e),SBF.storage("tickets-welcome",!0)},1e3)},init:function(){if(c=e("body").find(".sb-tickets"),d=c.find(" > div > .sb-panel-main"),f=d.find(".sb-panel"),g=d.find(".sb-editor"),h=d.find(" > .sb-top .sb-title"),u=c.find(".sb-user-conversations"),b=d.find(".sb-list"),v=c.find(".sb-profile-agent"),m=c.find(".sb-panel-right > .sb-top .sb-profile"),S=c.width(),B[SBF.setting("bot_id")]={name:SBF.setting("bot_name"),image:SBF.setting("bot_image")},l(),!c.length)return;if(!SBF.setting("tickets_registration_required")||s()&&!["visitor","lead"].includes(s().type))s()&&s().conversations.length?F.getActiveConversation()||SBChat.openConversation(SBF.getURL("conversation")?SBF.getURL("conversation"):s().conversations[0].id):(c.addClass("sb-no-conversations"),SBF.setting("privacy")&&!SBF.storage("privacy_approved")?F.showPanel("privacy"):SBF.setting("tickets_disable_first")?a():F.showPanel("new-ticket"));else{let e=SBF.setting("tickets_registration_redirect");if(e)return void(document.location=e+(e.includes("?")?"&":"?")+"sb=true");c.addClass("sb-no-conversations"),F.showPanel(SBF.setting("tickets_default_form"))}let t=parseInt(SBF.null(c.data("height"))?e(window).height():c.data("height")),i=parseInt(SBF.null(c.data("offset"))?0:c.data("offset"));S<=800?(c.addClass("sb-800"),c.find(".sb-panel-left,.sb-panel-right").addClass("sb-collapsed"),c.find(".sb-btn-collapse").sbActive(!0)):S<=1e3?c.addClass("sb-1000"):S<=1300&&c.addClass("sb-1300"),n(),c.removeClass("sb-loading").find(".sb-tickets-area").attr("style",`height: ${t-i}px`),setTimeout(function(){c.removeClass("sb-load")},300),SBChat.startRealTime(),SBF.event("SBTicketsInit")},onMessageSent:function(){if("new-ticket"==p){let e=d.find(".sb-ticket-title input").val();SBChat.updateConversations(),c.find(".sb-panel-right .sb-scroll-area > div").sbActive(!1),c.find(".sb-conversation-attachments,.sb-ticket-details").html(""),c.removeClass("sb-no-conversations"),F.showPanel(),r(e)}},onConversationReceived:function(e){e.id==SBChat.conversation.id&&F.getActiveConversation("ID")!=e.id&&setTimeout(()=>{F.activateConversation(SBChat.conversation),u.length&&u.scrollTop(u[0].scrollHeight)},300)},onNewMessageReceived:function(t,s){if(t instanceof SBMessage&&s==SBChat.conversation.id){let s=SBChat.lastAgent(),i=SBChat.conversation.getCode(),n=F.getActiveConversation();n?e(n).html(i):u.append(`<li data-conversation-id="${SBChat.conversation.id}" class="sb-active">${i}</li>`).find(" > p").remove(),s&&SBF.isAgent(t.get("user_type"))&&s.id!=t.get("user_id")&&F.setAgent(t.get("user_id"))}}};window.SBTickets=F}(jQuery);