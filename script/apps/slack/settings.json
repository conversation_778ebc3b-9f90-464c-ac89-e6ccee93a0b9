[{"type": "checkbox", "id": "slack-active", "title": "Active", "content": "Activate the Slack integration."}, {"type": "button", "id": "slack-button", "title": "Synchronization", "content": "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.", "help": "https://board.support/docs/#slack-sync", "button-text": "Synchronize now", "button-url": "#"}, {"type": "text", "id": "slack-workspace", "title": "Workspace", "content": "The workspace name you are using to synchronize Slack."}, {"type": "password", "id": "slack-token", "title": "Bot Access Token", "content": "You will get this code by completing the Slack synchronization."}, {"type": "password", "id": "slack-token-user", "title": "User Access Token", "content": "You will get this code by completing the Slack synchronization."}, {"type": "text", "id": "slack-channel", "title": "Channel ID", "content": "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization."}, {"type": "double-select", "id": "slack-agents", "title": "Agent linking", "content": "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent."}, {"type": "repeater", "id": "slack-departments", "title": "Department linking", "content": "Set a dedicated Slack channel for each department.", "help": "https://board.support/docs/#slack-departments", "items": [{"type": "number", "name": "Department ID", "id": "slack-departments-id"}, {"type": "text", "name": "Channel ID", "id": "slack-departments-channel"}]}, {"type": "text", "id": "slack-user-details", "title": "User details", "content": "Set which user details to send to the main channel. Add comma separated values.", "help": "https://board.support/docs/#slack-user-fields"}, {"type": "button", "id": "slack-channel-ids", "title": "Channels", "content": "Show the list of all Slack channels.", "button-text": "View channels", "button-url": "#"}, {"type": "button", "id": "slack-test", "title": "Test Slack", "content": "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.", "button-text": "Send message to Slack", "button-url": "#"}, {"type": "button", "id": "slack-archive-channels", "title": "Archive channels", "content": "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.", "button-text": "Archive channels now", "button-url": "#"}, {"type": "checkbox", "id": "slack-disable-invitation", "title": "Disable invitation", "content": "Disable the automatic invitation of agents to the channels."}]