[{"type": "multi-input", "id": "open-ai", "title": "OpenAI", "content": "OpenAI settings.", "help": "https://board.support/docs/#open-ai", "value": [{"type": "select", "id": "open-ai-sync-mode", "title": "Sync mode", "value": [["auto", "Automatic"], ["manual", "Manual"]]}, {"type": "password", "id": "open-ai-key", "title": "API key"}, {"type": "select", "id": "open-ai-mode", "title": "Chatbot mode", "value": [["", "All questions"], ["sources", "Only questions related to your sources"], ["general", "Only general questions"], ["assistant", "Assistant"]]}, {"type": "select", "id": "open-ai-model", "title": "Model", "value": [["", "<PERSON><PERSON><PERSON>"], ["gpt-4.1-nano", "gpt-4.1-nano"], ["gpt-4.1-mini", "gpt-4.1-mini"], ["gpt-4.1", "gpt-4.1"], ["o4-mini", "o4-mini"], ["o3-mini", "o3-mini"], ["o1", "o1"], ["gpt-4o-mini", "gpt-4o-mini"], ["gpt-4o", "gpt-4o"], ["gpt-4-turbo", "gpt-4-turbo"], ["gpt-4", "gpt-4"], ["gpt-3.5-turbo", "gpt-3.5-turbo"], ["gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct"]]}, {"type": "text", "id": "open-ai-assistant-id", "title": "Assistant ID"}, {"type": "text", "id": "open-ai-fallback-message", "title": "Fallback message"}, {"type": "textarea", "id": "open-ai-prompt", "title": "Prompt"}, {"type": "text", "id": "open-ai-prompt-message-rewrite", "title": "Prompt - Message rewriting"}, {"type": "number", "id": "open-ai-tokens", "title": "Max tokens"}, {"type": "number", "id": "open-ai-temperature", "title": "Temperature"}, {"type": "number", "id": "open-ai-presence-penalty", "title": "Presence penalty"}, {"type": "number", "id": "open-ai-frequency-penalty", "title": "Frequency penalty"}, {"type": "text", "id": "open-ai-logit-bias", "title": "Logit bias"}, {"type": "text", "id": "open-ai-custom-model", "title": "Custom model ID"}, {"type": "select", "id": "open-ai-training-data-language", "title": "Training data language", "value": [["", "<PERSON><PERSON><PERSON>"], ["am", "Amharic"], ["ar", "Arabic"], ["bg", "Bulgarian"], ["br", "Portuguese - Brasilian"], ["cs", "Czech"], ["da", "Danish"], ["de", "German"], ["el", "Greek"], ["es", "Spanish"], ["et", "Estonian"], ["fa", "Persian"], ["fi", "Finnish"], ["fr", "French"], ["he", "Hebrew"], ["hi", "Hindi"], ["hr", "Croatian"], ["hu", "Hungarian"], ["id", "Indonesian"], ["is", "Icelandic"], ["it", "Italian"], ["ja", "Japanese"], ["ka", "Georgian"], ["ko", "Korean"], ["lt", "Lithuanian"], ["mk", "Macedonian"], ["mn", "Mongolian"], ["ms", "Malay"], ["my", "Burmese"], ["nl", "Dutch"], ["no", "Norwegian"], ["pa", "Punjabi - <PERSON><PERSON><PERSON><PERSON><PERSON>"], ["pl", "Polish"], ["pt", "Portuguese"], ["ro", "Romanian"], ["ru", "Russian"], ["sk", "Slovak"], ["sl", "Slovenian"], ["sq", "Albanian"], ["sr", "Serbian"], ["su", "Sundanese"], ["sv", "Swedish"], ["th", "Thai"], ["tl", "Filipino"], ["tr", "Turkish"], ["uk", "Ukrainian"], ["vi", "Vietnamese"], ["zh", "Chinese"], ["zt", "Chinese - Traditional"]]}, {"type": "checkbox", "id": "open-ai-active", "title": "<PERSON><PERSON><PERSON>"}, {"type": "checkbox", "id": "open-ai-spelling-correction", "title": "Spelling correction"}, {"type": "checkbox", "id": "open-ai-smart-reply", "title": "Smart reply"}, {"type": "checkbox", "id": "open-ai-spelling-correction-dialogflow", "title": "Dialogflow spelling correction"}, {"type": "checkbox", "id": "open-ai-rewrite", "title": "Message rewrite button"}, {"type": "checkbox", "id": "open-ai-user-expressions", "title": "Generate user questions"}, {"type": "checkbox", "id": "open-ai-speech-recognition", "title": "Speech recognition"}, {"type": "checkbox", "id": "open-ai-vision", "title": "Image recognition"}, {"type": "checkbox", "id": "open-ai-source-links", "title": "Source links"}, {"type": "checkbox", "id": "open-ai-note-scraping", "title": "Note data scraping"}, {"type": "checkbox", "id": "open-ai-training-cron-job", "title": "Training via cron job"}, {"type": "checkbox", "id": "open-ai-user-train-conversations", "title": "Use conversations for training"}, {"type": "checkbox", "id": "open-ai-context-awareness", "title": "Context awareness"}, {"type": "button", "id": "open-ai-troubleshoot", "title": "Troubleshoot", "button-url": "#", "button-text": "Troubleshoot problems"}]}, {"type": "repeater", "id": "open-ai-assistants", "title": "OpenAI Assistants - Department linking", "content": "Set a dedicated OpenAI Assistants for each department.", "help": "https://board.support/docs/#open-ai-assistant", "items": [{"type": "number", "name": "Department ID", "id": "open-ai-assistants-department-id"}, {"type": "text", "name": "Assistant ID", "id": "open-ai-assistants-id"}]}, {"type": "multi-input", "id": "google", "title": "Google", "content": "Google and Dialogflow settings.", "help": "https://board.support/docs/#google-sync", "value": [{"type": "select", "id": "google-sync-mode", "title": "Sync mode", "value": [["auto", "Automatic"], ["manual", "Manual"]]}, {"type": "text", "id": "google-client-id", "title": "Client ID"}, {"type": "password", "id": "google-client-secret", "title": "Client secret"}, {"type": "text", "id": "google-refresh-token", "title": "Refresh token"}, {"type": "text", "id": "google-project-id", "title": "Project ID or Agent Name"}, {"type": "select", "id": "dialogflow-location", "title": "Dialogflow location", "content": "", "value": [["", "Global"], ["us-central1-", "us-central1"], ["northamerica-northeast1-", "northamerica-northeast1"], ["us-east1-", "us-east1"], ["us-west1-", "us-west1"], ["europe-west1-", "europe-west1"], ["europe-west2-", "europe-west2"], ["australia-southeast1-", "australia-southeast1"], ["asia-northeast1-", "asia-northeast1"], ["asia-southeast1-", "asia-southeast1"]]}, {"type": "select", "id": "dialogflow-edition", "title": "Dialogflow edition", "content": "", "value": [["es", "Dialogflow ES"], ["cx", "Dialogflow CX"]]}, {"type": "number", "id": "dialogflow-confidence", "title": "Dialogflow Intent detection confidence", "help": "https://board.support/docs/#dialogflow-confidence"}, {"type": "button", "id": "dialogflow-sync-btn", "title": "Synchronize", "button-text": "Synchronize", "button-url": "#"}, {"type": "button", "id": "dialogflow-redirect-url-btn", "title": "Authorised redirect URI", "button-text": "Get URL", "button-url": "#"}, {"type": "button", "id": "dialogflow-saved-replies", "title": "Add Intents to saved replies", "button-text": "Add Intents", "button-url": "#", "help": "https://board.support/docs/#dialogflow-saved-replies"}, {"type": "checkbox", "id": "dialogflow-active", "title": "Dialogflow chatbot"}, {"type": "checkbox", "id": "google-multilingual", "title": "Multilingual", "help": "https://board.support/docs/#google-multilingual"}, {"type": "checkbox", "id": "google-multilingual-translation", "title": "Multilingual via translation", "help": "https://board.support/docs/#google-translation"}, {"type": "checkbox", "id": "google-translation", "title": "Automatic translation", "help": "https://board.support/docs/#google-translation"}, {"type": "checkbox", "id": "google-language-detection", "title": "Language detection", "help": "https://board.support/docs/#google-translation"}, {"type": "text", "id": "google-language-detection-message", "title": "Language detection message"}, {"type": "checkbox", "id": "dialogflow-welcome", "title": "Dialogflow welcome Intent", "help": "https://board.support/docs/#dialogflow-welcome"}, {"type": "checkbox", "id": "dialogflow-send-user-details", "title": "Send user details", "help": "https://board.support/docs/#dialogflow-send-details"}, {"type": "button", "id": "google-troubleshoot", "title": "Troubleshoot", "button-url": "#", "button-text": "Troubleshoot problems"}]}, {"type": "repeater", "id": "dialogflow-departments", "title": "Dialogflow - Department linking", "content": "Set a dedicated Dialogflow agent for each department.", "help": "https://board.support/docs/#dialogflow-departments", "items": [{"type": "number", "name": "Department ID", "id": "dialogflow-departments-id"}, {"type": "text", "name": "Project ID", "id": "dialogflow-departments-agent"}]}, {"type": "multi-input", "id": "dialogflow-google-search", "title": "Google search", "content": "Let the chatbot search on Google to find answers to user questions.", "help": "https://board.support/docs/#google-search", "value": [{"type": "checkbox", "id": "dialogflow-google-search-active", "title": "Active"}, {"type": "checkbox", "id": "dialogflow-google-search-spelling-active", "title": "Spelling correction"}, {"type": "text", "id": "dialogflow-google-search-id", "title": "Search engine ID"}, {"type": "password", "id": "dialogflow-google-search-key", "title": "API key"}]}, {"type": "checkbox", "id": "dialogflow-timetable", "title": "Disable during office hours", "content": "Enable the chatbot outside of scheduled office hours only."}, {"type": "checkbox", "id": "dialogflow-disable-tickets", "title": "Disable for the tickets area", "content": "Disable the chatbot for the tickets area."}, {"type": "multi-input", "id": "dialogflow-human-takeover", "title": "Human takeover", "content": "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.", "help": "https://board.support/docs/#human-takeover", "value": [{"type": "checkbox", "id": "dialogflow-human-takeover-active", "title": "Active"}, {"type": "checkbox", "id": "dialogflow-human-takeover-auto", "title": "Automatic human takeover"}, {"type": "checkbox", "id": "dialogflow-human-takeover-disable-chatbot", "title": "Disable chatbot"}, {"type": "textarea", "id": "dialogflow-human-takeover-message", "title": "Message"}, {"type": "textarea", "id": "dialogflow-human-takeover-message-confirmation", "title": "Confirmation message"}, {"type": "textarea", "id": "dialogflow-human-takeover-message-fallback", "title": "Fallback message"}, {"type": "text", "id": "dialogflow-human-takeover-confirm", "title": "Confirm button text"}, {"type": "text", "id": "dialogflow-human-takeover-cancel", "title": "Cancel button text"}]}, {"type": "number", "id": "dialogflow-bot-delay", "title": "Bot response delay", "content": "Add a delay (ms) to the bot's responses. De<PERSON>ult is 2000."}, {"type": "checkbox", "id": "ai-smart-reply", "title": "Smart reply", "content": "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.", "help": "https://board.support/docs/#smart-reply"}, {"type": "checkbox", "id": "dialogflow-email-piping", "title": "Reply to user emails", "content": "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active."}, {"type": "checkbox", "id": "dialogflow-sms", "title": "Reply to user text messages", "content": "Allow the chatbot to reply to the user's text messages if the answer is known."}, {"type": "multi-input", "id": "chatbot-usage-limit", "title": "Usage Limit", "content": "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.", "value": [{"type": "number", "id": "chatbot-usage-limit-quota", "title": "Max message limit"}, {"type": "number", "id": "chatbot-usage-limit-interval", "title": "Interval (sec)"}, {"type": "textarea", "id": "chatbot-usage-limit-message", "title": "Message"}]}]