{"entities": ["sys.date-time", "sys.date", "sys.date-period", "sys.time", "sys.unit-area", "sys.unit-currency", "sys.unit-length", "sys.unit-speed", "sys.unit-volume", "sys.unit-weight", "sys.unit-information", "sys.percentage", "sys.temperature", "sys.duration", "sys.age", "sys.currency-name", "sys.unit-area-name", "sys.unit-length-name", "sys.unit-speed-name", "sys.unit-volume-name", "sys.unit-weight-name", "sys.unit-information-name", "sys.address", "sys.zip-code", "sys.geo-capital", "sys.geo-country-code", "sys.geo-country", "sys.geo-city", "sys.geo-state", "sys.place-attraction", "sys.airport", "sys.location", "sys.email", "sys.phone-number", "sys.given-name", "sys.last-name", "sys.person", "sys.music-artist", "sys.music-genre", "sys.color", "sys.language", "sys.url", "sys.any", "sys.number", "sys.number-integer"], "entities-values": {"sys.date-time": ["2020", "May, 2020", "January, 15", "February", "August", "March"], "sys.date": ["2020", "May, 2020", "January, 15", "February", "August", "March"], "sys.date-period": ["2020 to 2021", "May, 2020 to June, 2020", "January, 15 to February, 3", "February to March", "August to September", "March to July"], "sys.time": ["15:30", "6pm", "7am", "8pm", "17:30", "9am"], "sys.unit-area": ["100cm2", "40ft2", "1m2", "5 square miles", "18 square kilometres", "ten square feet"], "sys.unit-currency": ["15$", "20€", "25 pounds", "4$", "10 USD", "8 EUROS"], "sys.unit-length": ["100cm", "40ft", "1m", "5 miles", "18 kilometres", "100 feets"], "sys.unit-speed": ["50km/h", "80miles/hour", "25 km/h", "30mph", "100mph", "45mph"], "sys.unit-volume": ["2 liters", "5 cubic meters", "6 liters", "15 cubic centimeters", "25 liters", "30 liters"], "sys.unit-weight": ["5 kilos", "10 grams", "80 milligrams", "15 pounds", "90 kilos", "8 grams"], "sys.unit-information": ["250 megabytes", "3 bytes", "15 kilobytes", "100 gigabytes", "9 bytes", "50 megabytes"], "sys.percentage": ["10%", "3%", "25%", "20%", "50%", "80%"], "sys.temperature": ["25°F", "5°", "11 degrees", "17°F", "14°", "13°F"], "sys.duration": ["33 seconds", "16 minutes", "10 hours", "12 days", "78 minutes", "45 days"], "sys.age": ["5 y.o.", "10 months old", "2 years old", "7 y.o.", "80 years old", "90 years old"], "sys.currency-name": ["dollars", "pounds", "euros", "yens", "australian dollar", "swiss franc"], "sys.unit-area-name": ["square meters", "square miles"], "sys.unit-length-name": ["meters", "kilometres", "feets", "centimeters", "inches"], "sys.unit-speed-name": ["kilometer per hour", "miles per hour", "feet per second"], "sys.unit-volume-name": ["cubic meters", "cubic centimeters", "liters"], "sys.unit-weight-name": ["kilograms", "grams", "pounds"], "sys.unit-information-name": ["megabytes", "bytes", "gigabytes", "terabytes", "kilobytes"], "sys.address": ["1600 Amphitheatre Pkwy, Mountain View, CA 94043", "10 Downing Street, London, SW1A 2AA", "20 Cooper Square, New York, NY 10003, USA", "80 Washington Square E, New York, NY 10003, USA", "Santa Maria 45, 28012, Madrid", "Via Mier 234, 32100 Belluno, Italy"], "sys.zip-code": ["94122", "SW1P 3PA", "H3B 4W2", "E14HR", "32100", "SW1A 2AA"], "sys.geo-capital": ["Paris", "London", "Rome", "Washington D.C.", "Tokyo", "Berlin"], "sys.geo-country-code": ["United States", "US", "USA", "840"], "sys.geo-country": ["United States", "Great Britain", "Italy", "Chile", "Japan", "Germany"], "sys.geo-city": ["New York", "Milan", "San Francisco", "London", "Chicago", "Lima"], "sys.geo-state": ["CA", "Scotland", "IT", "ES", "Spain", "Portugal"], "sys.place-attraction": ["Golden Gate Bridge", "Buckingham Palace", "Central Park", "Yosemite National Park", "The Shard", "The Colosseum"], "sys.airport": ["Heathrow Airport", "SFO", "KSFO"], "sys.location": ["1800 Amphitheatre Pkwy, Mountain View, CA 94044", "15 Downing Street, London, SW1B 3AA", "18 Cooper Square, New York, NY 10003, USA", "14 Washington Square E, New York, NY 10001, USA"], "sys.email": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "sys.phone-number": ["(*************", "+****************", "+39 123456789", "123456789", "001 123 4567890"], "sys.given-name": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "sys.last-name": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "sys.person": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "sys.music-artist": ["Beatles", "Rolling Stones", "Eminem", "Iggy Pop", "<PERSON>", "<PERSON>"], "sys.music-genre": ["Blues", "Rock", "Pop", "Jazz", "Indie", "Electronic"], "sys.color": ["green", "magenta", "red", "orange", "yellow", "pink"], "sys.language": ["English", "Japanese", "Italian", "Spanish", "Dutch", "French"], "sys.url": ["www.dialogflow.com", "www.google.com", "board.support", "wikipedia.org", "www.twitter.com"], "sys.any": ["products", "items", "objects", "pieces", "elements"], "sys.number": ["5.55", "0.2", "70", "15", "9.99"], "sys.number-integer": ["2", "5", "3", "10", "7"]}}