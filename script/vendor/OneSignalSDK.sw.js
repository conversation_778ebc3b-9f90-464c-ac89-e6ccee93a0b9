(()=>{var e={470:function(e,t,i){var n;n=function(){function e(e){function t(t){var i=e.match(t);return i&&i.length>1&&i[1]||""}var i,n,o,s=t(/(ipod|iphone|ipad)/i).toLowerCase(),r=!/like android/i.test(e)&&/android/i.test(e),a=/nexus\s*[0-6]\s*/i.test(e),c=!a&&/nexus\s*[0-9]+/i.test(e),d=/CrOS/.test(e),l=/silk/i.test(e),u=/sailfish/i.test(e),p=/tizen/i.test(e),g=/(web|hpw)os/i.test(e),f=/windows phone/i.test(e),h=(/SamsungBrowser/i.test(e),!f&&/windows/i.test(e)),v=!s&&!l&&/macintosh/i.test(e),m=!r&&!u&&!p&&!g&&/linux/i.test(e),b=t(/edge\/(\d+(\.\d+)?)/i),y=t(/version\/(\d+(\.\d+)?)/i),$=/tablet/i.test(e)&&!/tablet pc/i.test(e),S=!$&&/[^-]mobi/i.test(e),w=/xbox/i.test(e);/opera/i.test(e)?i={name:"Opera",opera:!0,version:y||t(/(?:opera|opr|opios)[\s\/](\d+(\.\d+)?)/i)}:/opr\/|opios/i.test(e)?i={name:"Opera",opera:!0,version:t(/(?:opr|opios)[\s\/](\d+(\.\d+)?)/i)||y}:/SamsungBrowser/i.test(e)?i={name:"Samsung Internet for Android",samsungBrowser:!0,version:y||t(/(?:SamsungBrowser)[\s\/](\d+(\.\d+)?)/i)}:/coast/i.test(e)?i={name:"Opera Coast",coast:!0,version:y||t(/(?:coast)[\s\/](\d+(\.\d+)?)/i)}:/yabrowser/i.test(e)?i={name:"Yandex Browser",yandexbrowser:!0,version:y||t(/(?:yabrowser)[\s\/](\d+(\.\d+)?)/i)}:/ucbrowser/i.test(e)?i={name:"UC Browser",ucbrowser:!0,version:t(/(?:ucbrowser)[\s\/](\d+(?:\.\d+)+)/i)}:/mxios/i.test(e)?i={name:"Maxthon",maxthon:!0,version:t(/(?:mxios)[\s\/](\d+(?:\.\d+)+)/i)}:/epiphany/i.test(e)?i={name:"Epiphany",epiphany:!0,version:t(/(?:epiphany)[\s\/](\d+(?:\.\d+)+)/i)}:/puffin/i.test(e)?i={name:"Puffin",puffin:!0,version:t(/(?:puffin)[\s\/](\d+(?:\.\d+)?)/i)}:/sleipnir/i.test(e)?i={name:"Sleipnir",sleipnir:!0,version:t(/(?:sleipnir)[\s\/](\d+(?:\.\d+)+)/i)}:/k-meleon/i.test(e)?i={name:"K-Meleon",kMeleon:!0,version:t(/(?:k-meleon)[\s\/](\d+(?:\.\d+)+)/i)}:f?(i={name:"Windows Phone",windowsphone:!0},b?(i.msedge=!0,i.version=b):(i.msie=!0,i.version=t(/iemobile\/(\d+(\.\d+)?)/i))):/msie|trident/i.test(e)?i={name:"Internet Explorer",msie:!0,version:t(/(?:msie |rv:)(\d+(\.\d+)?)/i)}:d?i={name:"Chrome",chromeos:!0,chromeBook:!0,chrome:!0,version:t(/(?:chrome|crios|crmo)\/(\d+(\.\d+)?)/i)}:/chrome.+? edge/i.test(e)?i={name:"Microsoft Edge",msedge:!0,version:b}:/vivaldi/i.test(e)?i={name:"Vivaldi",vivaldi:!0,version:t(/vivaldi\/(\d+(\.\d+)?)/i)||y}:u?i={name:"Sailfish",sailfish:!0,version:t(/sailfish\s?browser\/(\d+(\.\d+)?)/i)}:/seamonkey\//i.test(e)?i={name:"SeaMonkey",seamonkey:!0,version:t(/seamonkey\/(\d+(\.\d+)?)/i)}:/firefox|iceweasel|fxios/i.test(e)?(i={name:"Firefox",firefox:!0,version:t(/(?:firefox|iceweasel|fxios)[ \/](\d+(\.\d+)?)/i)},/\((mobile|tablet);[^\)]*rv:[\d\.]+\)/i.test(e)&&(i.firefoxos=!0)):l?i={name:"Amazon Silk",silk:!0,version:t(/silk\/(\d+(\.\d+)?)/i)}:/phantom/i.test(e)?i={name:"PhantomJS",phantom:!0,version:t(/phantomjs\/(\d+(\.\d+)?)/i)}:/slimerjs/i.test(e)?i={name:"SlimerJS",slimer:!0,version:t(/slimerjs\/(\d+(\.\d+)?)/i)}:/blackberry|\bbb\d+/i.test(e)||/rim\stablet/i.test(e)?i={name:"BlackBerry",blackberry:!0,version:y||t(/blackberry[\d]+\/(\d+(\.\d+)?)/i)}:g?(i={name:"WebOS",webos:!0,version:y||t(/w(?:eb)?osbrowser\/(\d+(\.\d+)?)/i)},/touchpad\//i.test(e)&&(i.touchpad=!0)):/bada/i.test(e)?i={name:"Bada",bada:!0,version:t(/dolfin\/(\d+(\.\d+)?)/i)}:p?i={name:"Tizen",tizen:!0,version:t(/(?:tizen\s?)?browser\/(\d+(\.\d+)?)/i)||y}:/qupzilla/i.test(e)?i={name:"QupZilla",qupzilla:!0,version:t(/(?:qupzilla)[\s\/](\d+(?:\.\d+)+)/i)||y}:/chromium/i.test(e)?i={name:"Chromium",chromium:!0,version:t(/(?:chromium)[\s\/](\d+(?:\.\d+)?)/i)||y}:/chrome|crios|crmo/i.test(e)?i={name:"Chrome",chrome:!0,version:t(/(?:chrome|crios|crmo)\/(\d+(\.\d+)?)/i)}:r?i={name:"Android",version:y}:/safari|applewebkit/i.test(e)?(i={name:"Safari",safari:!0},y&&(i.version=y)):s?(i={name:"iphone"==s?"iPhone":"ipad"==s?"iPad":"iPod"},y&&(i.version=y)):i=/googlebot/i.test(e)?{name:"Googlebot",googlebot:!0,version:t(/googlebot\/(\d+(\.\d+))/i)||y}:{name:t(/^(.*)\/(.*) /),version:(n=/^(.*)\/(.*) /,(o=e.match(n))&&o.length>1&&o[2]||"")},!i.msedge&&/(apple)?webkit/i.test(e)?(/(apple)?webkit\/537\.36/i.test(e)?(i.name=i.name||"Blink",i.blink=!0):(i.name=i.name||"Webkit",i.webkit=!0),!i.version&&y&&(i.version=y)):!i.opera&&/gecko\//i.test(e)&&(i.name=i.name||"Gecko",i.gecko=!0,i.version=i.version||t(/gecko\/(\d+(\.\d+)?)/i)),i.windowsphone||i.msedge||!r&&!i.silk?i.windowsphone||i.msedge||!s?v?i.mac=!0:w?i.xbox=!0:h?i.windows=!0:m&&(i.linux=!0):(i[s]=!0,i.ios=!0):i.android=!0;var I="";i.windows?I=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}(t(/Windows ((NT|XP)( \d\d?.\d)?)/i)):i.windowsphone?I=t(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i):i.mac?I=(I=t(/Mac OS X (\d+([_\.\s]\d+)*)/i)).replace(/[_\s]/g,"."):s?I=(I=t(/os (\d+([_\s]\d+)*) like mac os x/i)).replace(/[_\s]/g,"."):r?I=t(/android[ \/-](\d+(\.\d+)*)/i):i.webos?I=t(/(?:web|hpw)os\/(\d+(\.\d+)*)/i):i.blackberry?I=t(/rim\stablet\sos\s(\d+(\.\d+)*)/i):i.bada?I=t(/bada\/(\d+(\.\d+)*)/i):i.tizen&&(I=t(/tizen[\/\s](\d+(\.\d+)*)/i)),I&&(i.osversion=I);var k=!i.windows&&I.split(".")[0];return $||c||"ipad"==s||r&&(3==k||k>=4&&!S)||i.silk?i.tablet=!0:(S||"iphone"==s||"ipod"==s||r||a||i.blackberry||i.webos||i.bada)&&(i.mobile=!0),i.msedge||i.msie&&i.version>=10||i.yandexbrowser&&i.version>=15||i.vivaldi&&i.version>=1||i.chrome&&i.version>=20||i.samsungBrowser&&i.version>=4||i.firefox&&i.version>=20||i.safari&&i.version>=6||i.opera&&i.version>=10||i.ios&&i.osversion&&i.osversion.split(".")[0]>=6||i.blackberry&&i.version>=10.1||i.chromium&&i.version>=20?i.a=!0:i.msie&&i.version<10||i.chrome&&i.version<20||i.firefox&&i.version<20||i.safari&&i.version<6||i.opera&&i.version<10||i.ios&&i.osversion&&i.osversion.split(".")[0]<6||i.chromium&&i.version<20?i.c=!0:i.x=!0,i}var t=e("undefined"!=typeof navigator&&navigator.userAgent||"");function i(e){return e.split(".").length}function n(e,t){var i,n=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(i=0;i<e.length;i++)n.push(t(e[i]));return n}function o(e){for(var t=Math.max(i(e[0]),i(e[1])),o=n(e,function(e){var o=t-i(e);return n((e+=Array(o+1).join(".0")).split("."),function(e){return Array(20-e.length).join("0")+e}).reverse()});--t>=0;){if(o[0][t]>o[1][t])return 1;if(o[0][t]!==o[1][t])return -1;if(0===t)return 0}}function s(i,n,s){var r=t;"string"==typeof n&&(s=n,n=void 0),void 0===n&&(n=!1),s&&(r=e(s));var a=""+r.version;for(var c in i)if(i.hasOwnProperty(c)&&r[c]){if("string"!=typeof i[c])throw Error("Browser version in the minVersion map should be a string: "+c+": "+String(i));return 0>o([a,i[c]])}return n}return t.test=function(e){for(var i=0;i<e.length;++i){var n=e[i];if("string"==typeof n&&n in t)return!0}return!1},t.isUnsupportedBrowser=s,t.compareVersions=o,t.check=function(e,t,i){return!s(e,t,i)},t._detect=e,t},e.exports?e.exports=n():i.amdD("bowser",n)}},t={};function i(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n].call(s.exports,s,s.exports,i),s.exports}i.amdD=function(){throw Error("define cannot be used indirect")},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";function e(e,t,i,n){return new(i||(i=Promise))(function(o,s){function r(e){try{c(n.next(e))}catch(t){s(t)}}function a(e){try{c(n.throw(e))}catch(t){s(t)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof i?t:new i(function(e){e(t)})).then(r,a)}c((n=n.apply(e,t||[])).next())})}Object.create,Object.create,function(e){e.Development="Development",e.Staging="Staging",e.Production="Production"}(N||(N={})),function(e){e.ServiceWorker="ServiceWorker",e.Host="Host"}(T||(T={}));class t extends Error{constructor(e=""){super(e),Object.defineProperty(this,"message",{configurable:!0,enumerable:!1,value:e,writable:!0}),Object.defineProperty(this,"name",{configurable:!0,enumerable:!1,value:this.constructor.name,writable:!0}),Error.hasOwnProperty("captureStackTrace")?Error.captureStackTrace(this,this.constructor):(Object.defineProperty(this,"stack",{configurable:!0,enumerable:!1,value:Error(e).stack,writable:!0}),Object.setPrototypeOf(this,t.prototype))}}!function(e){e[e.Empty=0]="Empty",e[e.Malformed=1]="Malformed",e[e.EnumOutOfRange=2]="EnumOutOfRange",e[e.WrongType=3]="WrongType"}(A||(A={}));class n extends t{constructor(e,t,i){let o;switch(t){case A.Empty:o=`Supply a non-empty value to '${e}'. ${i}`;break;case A.Malformed:o=`The value for '${e}' was malformed. ${i}`;break;case A.EnumOutOfRange:o=`The value for '${e}' was out of range of the expected input enum. ${i}`;break;case A.WrongType:o=`The value for '${e}' was of the wrong type. ${i}`}super(o),this.argument=e,this.reason=A[t],Object.setPrototypeOf(this,n.prototype)}}let o=["outcomes","on_focus"];class s{static getBuildEnv(){return N.Production}static getApiEnv(){return N.Production}static getOrigin(){return _.isBrowser()?window.location.origin:"undefined"!=typeof self&&"undefined"!=typeof ServiceWorkerGlobalScope?self.location.origin:"Unknown"}static getWindowEnv(){if("undefined"==typeof window){if("undefined"!=typeof self&&"undefined"!=typeof ServiceWorkerGlobalScope)return T.ServiceWorker;throw Error("OneSignalSDK: Unsupported JS runtime!")}return T.Host}static getBuildEnvPrefix(e=s.getBuildEnv()){switch(e){case N.Development:return"Dev-";case N.Staging:return"Staging-";case N.Production:return"";default:throw new n("buildEnv",A.EnumOutOfRange)}}static getOneSignalApiUrl(e=s.getApiEnv(),t){let i="localhost";switch(e){case N.Development:return new URL(s.isTurbineEndpoint(t)?`https://${i}:18080/api/v1`:`https://${i}:3001/api/v1`);case N.Staging:return new URL(`https://${i}/api/v1`);case N.Production:return new URL("https://onesignal.com/api/v1");default:throw new n("buildEnv",A.EnumOutOfRange)}}static getOneSignalStaticResourcesUrl(){return new URL("https://media.onesignal.com/web-sdk")}static getOneSignalResourceUrlPath(e=s.getBuildEnv()){let t="localhost",i;switch(e){case N.Development:i=`http://${t}:4000`;break;case N.Staging:i=`https://${t}`;break;case N.Production:i="https://onesignal.com";break;default:throw new n("buildEnv",A.EnumOutOfRange)}return new URL(`${i}/sdks/web/v16`)}static getOneSignalCssFileName(e=s.getBuildEnv()){let t="OneSignalSDK.page.styles.css";switch(e){case N.Development:return`Dev-${t}`;case N.Staging:return`Staging-${t}`;case N.Production:return t;default:throw new n("buildEnv",A.EnumOutOfRange)}}static isTurbineEndpoint(e){return!!e&&o.some(t=>e.indexOf(t)>-1)}}var r,a,c,d,l,u,p,g,f,h,v,m,b,y,$,S,w,I,k,O,P,x,C,N,T,A,E,D=i(470),M=i.n(D);function U(){return{mobile:D.mobile,tablet:D.tablet,name:D.name.toLowerCase(),version:D.version}}function W(){return"undefined"!=typeof PushSubscriptionOptions&&PushSubscriptionOptions.prototype.hasOwnProperty("applicationServerKey")}class _{static isBrowser(){return"undefined"!=typeof window}static useSafariLegacyPush(){var e;return this.isBrowser()&&null!=(null===(e=window.safari)||void 0===e?void 0:e.pushNotification)}static useSafariVapidPush(){return"safari"==U().name&&W()&&!this.useSafariLegacyPush()}static version(){return Number(160201)}static get TRADITIONAL_CHINESE_LANGUAGE_TAG(){return["tw","hant"]}static get SIMPLIFIED_CHINESE_LANGUAGE_TAG(){return["cn","hans"]}static getLanguage(){let e=navigator.language;if(e){let t=(e=e.toLowerCase()).split("-");if("zh"==t[0]){for(let i of _.TRADITIONAL_CHINESE_LANGUAGE_TAG)if(-1!==t.indexOf(i))return"zh-Hant";for(let n of _.SIMPLIFIED_CHINESE_LANGUAGE_TAG)if(-1!==t.indexOf(n))return"zh-Hans";return"zh-Hant"}return t[0].substring(0,2)}return"en"}static supportsServiceWorkers(){return s.getWindowEnv()===T.ServiceWorker||"undefined"!=typeof navigator&&"serviceWorker"in navigator}static getSdkStylesVersionHash(){return"undefined"==typeof __SRC_STYLESHEETS_MD5_HASH__?"2":__SRC_STYLESHEETS_MD5_HASH__}}class R{static shouldLog(){try{if("undefined"==typeof window||void 0===window.localStorage)return!1;let e=window.localStorage.getItem("loglevel");return!(!e||"trace"!==e.toLowerCase())}catch(t){return!1}}static setLevel(e){if("undefined"!=typeof window&&void 0!==window.localStorage)try{window.localStorage.setItem("loglevel",e),R.proxyMethodsCreated=void 0,R.createProxyMethods()}catch(t){return}}static createProxyMethods(){if(void 0!==R.proxyMethodsCreated)return;R.proxyMethodsCreated=!0;let e={log:"debug",trace:"trace",info:"info",warn:"warn",error:"error"};for(let t of Object.keys(e)){let i=void 0!==console[t],n=e[t],o=i&&(R.shouldLog()||"error"===n);R[n]=o?console[t].bind(console):function(){}}}}R.createProxyMethods();class V{static getRegistration(t){return e(this,void 0,void 0,function*(){try{return yield navigator.serviceWorker.getRegistration(location.origin+t)}catch(e){return R.warn("[Service Worker Status] Error Checking service worker registration",t,e),null}})}static getAvailableServiceWorker(e){let t=e.active||e.installing||e.waiting;return t||R.warn("Could not find an available ServiceWorker instance!"),t}static waitUntilActive(e){return new Promise(t=>{let i=e.installing||e.waiting;i&&i.addEventListener("statechange",()=>{R.debug("OneSignal Service Worker state changed:",i.state),e.active&&t()}),e.active&&t()})}}!function(e){e.WorkerVersion="GetWorkerVersion",e.Subscribe="Subscribe",e.SubscribeNew="SubscribeNew",e.AmpSubscriptionState="amp-web-push-subscription-state",e.AmpSubscribe="amp-web-push-subscribe",e.AmpUnsubscribe="amp-web-push-unsubscribe",e.NotificationWillDisplay="notification.willDisplay",e.NotificationClicked="notification.clicked",e.NotificationDismissed="notification.dismissed",e.RedirectPage="command.redirect",e.SessionUpsert="os.session.upsert",e.SessionDeactivate="os.session.deactivate",e.AreYouVisible="os.page_focused_request",e.AreYouVisibleResponse="os.page_focused_response",e.SetLogging="os.set_sw_logging"}(E||(E={}));class B{constructor(){this.replies={}}addListener(e,t,i){let n={callback:t,onceListenerOnly:i},o=this.replies[e.toString()];o?o.push(n):this.replies[e.toString()]=[n]}findListenersForMessage(e){return this.replies[e.toString()]||[]}deleteListenerRecords(e){this.replies[e.toString()]=null}deleteAllListenerRecords(){this.replies={}}deleteListenerRecord(e,t){let i=this.replies[e.toString()];if(null!=i)for(let n=i.length-1;n>=0;n--)i[n]===t&&i.splice(n,1)}}class L{constructor(e,t=new B){this.context=e,this.replies=t}broadcast(t,i){return e(this,void 0,void 0,function*(){if(s.getWindowEnv()!==T.ServiceWorker)return;let e=yield self.clients.matchAll({type:"window",includeUncontrolled:!0});for(let n of e)R.debug(`[Worker Messenger] [SW -> Page] Broadcasting '${t.toString()}' to window client ${n.url}.`),n.postMessage({command:t,payload:i})})}unicast(t,i,o){return e(this,void 0,void 0,function*(){if(s.getWindowEnv()===T.ServiceWorker){if(!o)throw new n("windowClient",A.Empty);R.debug(`[Worker Messenger] [SW -> Page] Unicasting '${t.toString()}' to window client ${o.url}.`),o.postMessage({command:t,payload:i})}else R.debug(`[Worker Messenger] [Page -> SW] Unicasting '${t.toString()}' to service worker.`),this.directPostMessageToSW(t,i)})}directPostMessageToSW(t,i){return e(this,void 0,void 0,function*(){var e;R.debug(`[Worker Messenger] [Page -> SW] Direct command '${t.toString()}' to service worker.`);let n=yield null===(e=this.context)||void 0===e?void 0:e.serviceWorkerManager.getRegistration();if(!n)return void R.error("`[Worker Messenger] [Page -> SW] Could not get ServiceWorkerRegistration to postMessage!");let o=V.getAvailableServiceWorker(n);o?o.postMessage({command:t,payload:i}):R.error("`[Worker Messenger] [Page -> SW] Could not get ServiceWorker to postMessage!")})}listen(){return e(this,void 0,void 0,function*(){_.supportsServiceWorkers()&&(s.getWindowEnv()===T.ServiceWorker?(self.addEventListener("message",this.onWorkerMessageReceivedFromPage.bind(this)),R.debug("[Worker Messenger] Service worker is now listening for messages.")):yield this.listenForPage())})}listenForPage(){return e(this,void 0,void 0,function*(){navigator.serviceWorker.addEventListener("message",this.onPageMessageReceivedFromServiceWorker.bind(this)),R.debug(`(${location.origin}) [Worker Messenger] Page is now listening for messages.`)})}onWorkerMessageReceivedFromPage(e){let t=e.data;if(!t||!t.command)return;let i=this.replies.findListenersForMessage(t.command),n=[],o=[];for(let s of(R.debug("[Worker Messenger] Service worker received message:",e.data),i))s.onceListenerOnly&&n.push(s),o.push(s);for(let r=n.length-1;r>=0;r--){let a=n[r];this.replies.deleteListenerRecord(t.command,a)}for(let c of o)c.callback.apply(null,[t.payload])}onPageMessageReceivedFromServiceWorker(e){let t=e.data;if(!t||!t.command)return;let i=this.replies.findListenersForMessage(t.command),n=[],o=[];for(let s of(R.debug("[Worker Messenger] Page received message:",e.data),i))s.onceListenerOnly&&n.push(s),o.push(s);for(let r=n.length-1;r>=0;r--){let a=n[r];this.replies.deleteListenerRecord(t.command,a)}for(let c of o)c.callback.apply(null,[t.payload])}on(e,t){this.replies.addListener(e,t,!1)}once(e,t){this.replies.addListener(e,t,!0)}off(e){e?this.replies.deleteListenerRecords(e):this.replies.deleteAllListenerRecords()}}let F="os_pageViews",H="requiresPrivacyConsent";class K{static removeLegacySubscriptionOptions(){localStorage.removeItem("isOptedOut"),localStorage.removeItem("isPushNotificationsEnabled")}static setConsentRequired(e){localStorage.setItem(H,e.toString())}static getConsentRequired(){return"true"===localStorage.getItem(H)}static setLocalPageViewCount(e){localStorage.setItem(F,e.toString())}static getLocalPageViewCount(){return Number(localStorage.getItem(F))}}class j{constructor(){this.incrementedPageViewCount=!1}getPageViewCount(){try{let e=sessionStorage.getItem(j.SESSION_STORAGE_KEY_NAME),t=e?parseInt(e):0;return isNaN(t)?0:t}catch(i){return 0}}setPageViewCount(e){try{sessionStorage.setItem(j.SESSION_STORAGE_KEY_NAME,e.toString())}catch(t){}}incrementPageViewCount(){if(this.incrementedPageViewCount)return;let e=this.getPageViewCount()+1,t=this.getLocalPageViewCount()+1;this.setPageViewCount(e),this.setLocalPageViewCount(t),this.incrementedPageViewCount=!0,R.debug(`Incremented page view count: newCountSingleTab: ${e},
      newCountAccrossTabs: ${t}.`)}simulatePageNavigationOrRefresh(){this.incrementedPageViewCount=!1}isFirstPageView(){return 1===this.getPageViewCount()}getLocalPageViewCount(){return K.getLocalPageViewCount()}setLocalPageViewCount(e){K.setLocalPageViewCount(e)}}j.SESSION_STORAGE_KEY_NAME="onesignal-pageview-count";class z{static get STORED_PERMISSION_KEY(){return"storedNotificationPermission"}getPermissionStatus(){return e(this,void 0,void 0,function*(){if(!OneSignal.context)throw new t("OneSignal.context is undefined. Make sure to call OneSignal.init() before calling getPermissionStatus().");return yield OneSignal.context.permissionManager.getNotificationPermission(OneSignal.config.safariWebId)})}getNotificationPermission(t){return e(this,void 0,void 0,function*(){return _.useSafariLegacyPush()?z.getLegacySafariNotificationPermission(t):this.getW3cNotificationPermission()})}static getLegacySafariNotificationPermission(e){if(e)return window.safari.pushNotification.permission(e).permission;throw new n("safariWebId",A.Empty)}getW3cNotificationPermission(){return Notification.permission}}class q{constructor(e){if(!e)throw new n("path",A.Empty);this.path=e.trim()}getQueryString(){let e=this.path.indexOf("?");return -1===e?null:this.path.length>e?this.path.substring(e+1):null}getWithoutQueryString(){return this.path.split(q.QUERY_STRING)[0]}getFileName(){var e;return null===(e=this.getWithoutQueryString().split("\\").pop())||void 0===e?void 0:e.split("/").pop()}getFileNameWithQuery(){var e;return null===(e=this.path.split("\\").pop())||void 0===e?void 0:e.split("/").pop()}getFullPath(){return this.path}getPathWithoutFileName(){let e=this.getWithoutQueryString(),t=e.lastIndexOf(this.getFileName());return e.substring(0,t).replace(/[\\/]$/,"")}}q.QUERY_STRING="?";class G{constructor(){this._events={}}on(e,t){return this._events[e]=this._events[e]||[],this._events[e].push(t),this}once(e,t){let i=this;function n(){i.off(e,n),t.apply(this,arguments)}return n.listener=t,this.on(e,n),this}off(e,t){let i=this._events[e];if(void 0!==i){for(let n=0;n<i.length;n+=1)if(i[n]===t||i[n].listener===t){i.splice(n,1);break}0===i.length&&this.removeAllListeners(e)}return this}removeAllListeners(e){return e?delete this._events[e]:this._events={},this}listeners(e){try{return this._events[e]}catch(t){return}}numberOfListeners(e){let t=this.listeners(e);return t?t.length:0}emit(...t){return e(this,void 0,void 0,function*(){let e=t.shift(),i=this._events[e];if(void 0!==i){let n=(i=i.slice(0)).length;for(let o=0;o<n;o+=1)yield i[o].apply(this,t)}return this})}}!function(e){e.Identity="identity",e.Properties="properties",e.PushSubscriptions="pushSubscriptions",e.EmailSubscriptions="emailSubscriptions",e.SmsSubscriptions="smsSubscriptions"}(r||(r={}));class Y extends t{constructor(e="The asynchronous operation has timed out."){super(e),this.message=e,Object.setPrototypeOf(this,Y.prototype)}}class J{static contains(e,t){return!!e&&-1!==e.indexOf(t)}static trimUndefined(e){for(let t in e)void 0===e[t]&&delete e[t];return e}static capitalize(e){return e.charAt(0).toUpperCase()+e.slice(1)}static isNullOrUndefined(e){return null==e}static valueOrDefault(e,t){return null==e?t:e}static stringify(e){return JSON.stringify(e,(e,t)=>"function"==typeof t?"[Function]":t,4)}static encodeHashAsUriComponent(e){let t="",i=Object.keys(e);for(let n of i)t+=`${encodeURIComponent(n)}=${encodeURIComponent(e[n])}`;return t}static timeoutPromise(e,t){return Promise.race([e,new Promise((e,i)=>{setTimeout(()=>{i(new Y)},t)})])}static getValueOrDefault(e,t){return null!=e?e:t}static padStart(e,t,i){let n=e;for(;n.length<t;)n=i+n;return n}static parseVersionString(e){let t=e.toString().split("."),i;return Number(`${J.padStart(t[0],2,"0")}.${i=t[1]?J.padStart(t[1],2,"0"):"00"}`)}static lastParts(e,t,i){let n=e.split(t),o=Math.max(n.length-i,0);return n.slice(o).join(t)}static enforceAppId(e){if(!e)throw Error("App id cannot be empty")}static enforceAlias(e){if(!e.label)throw Error("Alias label cannot be empty");if(!e.id)throw Error("Alias id cannot be empty")}static sortArrayOfObjects(e,t,i=!1,n=!0){let o=n?e:e.slice();return o.sort((e,n)=>{let o=t(e),s=t(n);return o>s?i?-1:1:o<s?i?1:-1:0}),o}}let Q=J;class X{constructor(e,t=5){this.databaseName=e,this.dbVersion=t,this.emitter=new G}open(e){return new Promise(t=>{let i;try{i=indexedDB.open(e,this.dbVersion)}catch(n){}if(!i)return null;i.onerror=this.onDatabaseOpenError.bind(this),i.onblocked=this.onDatabaseOpenBlocked.bind(this),i.onupgradeneeded=this.onDatabaseUpgradeNeeded.bind(this),i.onsuccess=()=>{this.database=i.result,this.database.onerror=this.onDatabaseError,this.database.onversionchange=this.onDatabaseVersionChange,t(this.database)}})}close(){return e(this,void 0,void 0,function*(){var e;(yield this.ensureDatabaseOpen()).close(),null===(e=this.database)||void 0===e||e.close()})}ensureDatabaseOpen(){return e(this,void 0,void 0,function*(){return this.openLock||(this.openLock=this.open(this.databaseName)),yield this.openLock})}onDatabaseOpenError(e){e.preventDefault();let t=e.target.error;Q.contains(t.message,"The operation failed for reasons unrelated to the database itself and not covered by any other error code")||Q.contains(t.message,"A mutation operation was attempted on a database that did not allow mutations")?R.warn("OneSignal: IndexedDb web storage is not available on this origin since this profile's IndexedDb schema has been upgraded in a newer version of Firefox. See: https://bugzilla.mozilla.org/show_bug.cgi?id=1236557#c6"):R.warn("OneSignal: Fatal error opening IndexedDb database:",t)}onDatabaseError(e){R.debug("IndexedDb: Generic database error",e.target.errorCode)}onDatabaseOpenBlocked(){R.debug("IndexedDb: Blocked event")}onDatabaseVersionChange(e){R.debug("IndexedDb: versionchange event")}onDatabaseUpgradeNeeded(e){R.debug("IndexedDb: Database is being rebuilt or upgraded (upgradeneeded event).");let t=e.target,i=t.transaction;if(!i)throw Error("Can't migrate DB without a transaction");let n=t.result,o=e.newVersion||Number.MAX_SAFE_INTEGER;o>=1&&e.oldVersion<1&&(n.createObjectStore("Ids",{keyPath:"type"}),n.createObjectStore("NotificationOpened",{keyPath:"url"}),n.createObjectStore("Options",{keyPath:"key"})),o>=2&&e.oldVersion<2&&(n.createObjectStore("Sessions",{keyPath:"sessionKey"}),n.createObjectStore("NotificationReceived",{keyPath:"notificationId"}),n.createObjectStore("NotificationClicked",{keyPath:"notificationId"})),o>=3&&e.oldVersion<3&&n.createObjectStore("SentUniqueOutcome",{keyPath:"outcomeName"}),o>=4&&e.oldVersion<4&&(n.createObjectStore(r.Identity,{keyPath:"modelId"}),n.createObjectStore(r.Properties,{keyPath:"modelId"}),n.createObjectStore(r.PushSubscriptions,{keyPath:"modelId"}),n.createObjectStore(r.SmsSubscriptions,{keyPath:"modelId"}),n.createObjectStore(r.EmailSubscriptions,{keyPath:"modelId"})),o>=5&&e.oldVersion<5&&(this.migrateOutcomesNotificationClickedTableForV5(n,i),this.migrateOutcomesNotificationReceivedTableForV5(n,i)),o>=6&&e.oldVersion,"undefined"!=typeof OneSignal&&(OneSignal._isNewVisitor=!0)}migrateOutcomesNotificationClickedTableForV5(e,t){let i="Outcomes.NotificationClicked";e.createObjectStore(i,{keyPath:"notificationId"});let n="NotificationClicked",o=t.objectStore(n).openCursor();o.onsuccess=()=>{if(!o.result)return void e.deleteObjectStore(n);let s=o.result.value;t.objectStore(i).put({notificationId:s.notificationId||s.notification.id,appId:s.appId,timestamp:s.timestamp}),o.result.continue()},o.onerror=()=>{console.error("Could not migrate NotificationClicked records",o.error)}}migrateOutcomesNotificationReceivedTableForV5(e,t){let i="Outcomes.NotificationReceived";e.createObjectStore(i,{keyPath:"notificationId"});let n="NotificationReceived",o=t.objectStore(n).openCursor();o.onsuccess=()=>{o.result?(t.objectStore(i).put(o.result.value),o.result.continue()):e.deleteObjectStore(n)},o.onerror=()=>{console.error("Could not migrate NotificationReceived records",o.error)}}get(t,i){return e(this,void 0,void 0,function*(){let e=yield this.ensureDatabaseOpen();return i?yield new Promise((n,o)=>{let s=e.transaction(t).objectStore(t).get(i);s.onsuccess=()=>{n(s.result)},s.onerror=()=>{o(s.error)}}):yield new Promise((i,n)=>{let o={},s=e.transaction(t).objectStore(t).openCursor();s.onsuccess=e=>{let t=e.target.result;t?(o[t.key]=t.value,t.continue()):i(o)},s.onerror=()=>{n(s.error)}})})}getAll(t){return e(this,void 0,void 0,function*(){let e=yield this.ensureDatabaseOpen();return yield new Promise((i,n)=>{let o=e.transaction(t).objectStore(t).openCursor(),s=[];o.onsuccess=e=>{let t=e.target.result;t?(s.push(t.value),t.continue()):i(s)},o.onerror=()=>{n(o.error)}})})}put(t,i){return e(this,void 0,void 0,function*(){return yield this.ensureDatabaseOpen(),yield new Promise((e,n)=>{try{let o=this.database.transaction([t],"readwrite").objectStore(t).put(i);o.onsuccess=()=>{e(i)},o.onerror=e=>{R.error("Database PUT Transaction Error:",e),n(e)}}catch(s){R.error("Database PUT Error:",s),n(s)}})})}remove(t,i){return e(this,void 0,void 0,function*(){let e=yield this.ensureDatabaseOpen();return new Promise((n,o)=>{try{let s=e.transaction([t],"readwrite").objectStore(t),r=i?s.delete(i):s.clear();r.onsuccess=()=>{n(i)},r.onerror=e=>{R.error("Database REMOVE Transaction Error:",e),o(e)}}catch(a){R.error("Database REMOVE Error:",a),o(a)}})})}}class Z{constructor(){this.lastKnownOptedIn=!0}}class ee{}class et{serialize(){return{deviceId:this.deviceId,subscriptionToken:this.subscriptionToken,optedOut:this.optedOut,createdAt:this.createdAt,expirationTime:this.expirationTime}}static deserialize(e){let t=new et;return t.deviceId=e.deviceId,t.subscriptionToken=e.subscriptionToken,t.optedOut=e.optedOut,t.createdAt=e.createdAt,t.expirationTime=e.expirationTime,t}}(function(e){e.Active="active",e.Inactive="inactive"})(a||(a={})),function(e){e[e.UserCreate=1]="UserCreate",e[e.UserNewSession=2]="UserNewSession",e[e.VisibilityVisible=3]="VisibilityVisible",e[e.VisibilityHidden=4]="VisibilityHidden",e[e.BeforeUnload=5]="BeforeUnload",e[e.PageRefresh=6]="PageRefresh",e[e.Focus=7]="Focus",e[e.Blur=8]="Blur"}(c||(c={}));let ei="oneSignalSession";function en(e){let t=(new Date).getTime(),i=e&&e.notificationId||null;return{sessionKey:ei,appId:e.appId,startTimestamp:t,accumulatedDuration:0,notificationId:i,status:a.Active,lastDeactivatedTimestamp:null,lastActivatedTimestamp:t}}class eo{static toDatabase(e){let t=e.notification,i=e.result;return{id:t.notificationId,heading:t.title,content:t.body,data:t.additionalData,url:i.url,rr:t.confirmDelivery,icon:t.icon,image:t.image,tag:t.topic,badge:t.badgeIcon,action:i.actionId,buttons:this.toDatabaseButtons(t.actionButtons),timestamp:e.timestamp}}static toDatabaseButtons(e){return null==e?void 0:e.map(e=>({action:e.actionId,title:e.text,icon:e.icon,url:e.launchURL}))}static fromDatabase(e){return{result:{actionId:e.action,url:e.url},notification:{notificationId:e.id,title:e.heading,body:e.content,additionalData:e.data,launchURL:e.url,confirmDelivery:e.rr,icon:e.icon,image:e.image,topic:e.tag,badgeIcon:e.badge,actionButtons:this.toOSNotificationButtons(e.buttons)},timestamp:e.timestamp}}static toOSNotificationButtons(e){return null==e?void 0:e.map(e=>({actionId:e.action,text:e.title,icon:e.icon,launchURL:e.url}))}}class es{static toDatabase(e,t){return{appId:e,notificationId:t.notification.notificationId,timestamp:t.timestamp}}static fromDatabase(e){return{appId:e.appId,notificationId:e.notificationId,timestamp:e.timestamp}}}class er{static toDatabase(e,t,i){return{appId:e,notificationId:t.notificationId,timestamp:i}}static fromDatabase(e){return{appId:e.appId,notificationId:e.notificationId,timestamp:e.timestamp}}}!function(e){e[e.SET=0]="SET"}(d||(d={}));let ea="Outcomes.NotificationClicked",ec="Outcomes.NotificationReceived";class ed{constructor(e){this.databaseName=e,this.emitter=new G,this.database=new X(this.databaseName)}static resetInstance(){ed.databaseInstance=null}static get singletonInstance(){return ed.databaseInstanceName||(ed.databaseInstanceName="ONE_SIGNAL_SDK_DB"),ed.databaseInstance||(ed.databaseInstance=new ed(ed.databaseInstanceName)),ed.databaseInstance}static applyDbResultFilter(e,t,i){switch(e){case"Options":return i&&t?i.value:i&&!t?i:null;case"Ids":return i&&t?i.id:i&&!t?i:null;default:return i||null}}get(t,i){return e(this,void 0,void 0,function*(){let e=yield this.database.get(t,i);return ed.applyDbResultFilter(t,i,e)})}getAll(t){return e(this,void 0,void 0,function*(){return yield this.database.getAll(t)})}put(t,i){return e(this,void 0,void 0,function*(){yield new Promise((e,n)=>{this.database.put(t,i).then(()=>e())}),this.emitter.emit(ed.EVENTS.SET,i)})}remove(e,t){return this.database.remove(e,t)}getAppConfig(){return e(this,void 0,void 0,function*(){let e={},t=yield this.get("Ids","appId");return e.appId=t,e.vapidPublicKey=yield this.get("Options","vapidPublicKey"),e})}setAppConfig(t){return e(this,void 0,void 0,function*(){t.appId&&(yield this.put("Ids",{type:"appId",id:t.appId})),t.vapidPublicKey&&(yield this.put("Options",{key:"vapidPublicKey",value:t.vapidPublicKey}))})}getAppState(){return e(this,void 0,void 0,function*(){let e=new Z;return e.defaultNotificationUrl=yield this.get("Options","defaultUrl"),e.defaultNotificationTitle=yield this.get("Options","defaultTitle"),e.lastKnownPushEnabled=yield this.get("Options","isPushEnabled"),e.pendingNotificationClickEvents=yield this.getAllPendingNotificationClickEvents(),e.lastKnownPushId=yield this.get("Options","lastPushId"),e.lastKnownPushToken=yield this.get("Options","lastPushToken"),e.lastKnownOptedIn=yield this.get("Options","lastOptedIn"),e})}setIsPushEnabled(t){return e(this,void 0,void 0,function*(){yield this.put("Options",{key:"isPushEnabled",value:t})})}setAppState(t){return e(this,void 0,void 0,function*(){if(t.defaultNotificationUrl&&(yield this.put("Options",{key:"defaultUrl",value:t.defaultNotificationUrl})),(t.defaultNotificationTitle||""===t.defaultNotificationTitle)&&(yield this.put("Options",{key:"defaultTitle",value:t.defaultNotificationTitle})),null!=t.lastKnownPushEnabled&&(yield this.setIsPushEnabled(t.lastKnownPushEnabled)),null!=t.lastKnownPushId&&(yield this.put("Options",{key:"lastPushId",value:t.lastKnownPushId})),null!=t.lastKnownPushToken&&(yield this.put("Options",{key:"lastPushToken",value:t.lastKnownPushToken})),null!=t.lastKnownOptedIn&&(yield this.put("Options",{key:"lastOptedIn",value:t.lastKnownOptedIn})),t.pendingNotificationClickEvents){let e=Object.keys(t.pendingNotificationClickEvents);for(let i of e){let n=t.pendingNotificationClickEvents[i];n?yield this.put("NotificationOpened",{url:i,data:n.data,timestamp:n.timestamp}):null===n&&(yield this.remove("NotificationOpened",i))}}})}getUserState(){return e(this,void 0,void 0,function*(){let e=new ee;return e.previousOneSignalId="",e.previousExternalId="",e.previousOneSignalId=yield this.get("Options","previousOneSignalId"),e.previousExternalId=yield this.get("Options","previousExternalId"),e})}setUserState(t){return e(this,void 0,void 0,function*(){yield this.put("Options",{key:"previousOneSignalId",value:t.previousOneSignalId}),yield this.put("Options",{key:"previousExternalId",value:t.previousExternalId})})}getSubscription(){return e(this,void 0,void 0,function*(){let e=new et;e.deviceId=yield this.get("Ids","userId"),e.subscriptionToken=yield this.get("Ids","registrationId");let t=yield this.get("Options","optedOut"),i=yield this.get("Options","subscription"),n=yield this.get("Options","subscriptionCreatedAt"),o=yield this.get("Options","subscriptionExpirationTime");return e.optedOut=null!=t?t:null!=i&&!i,e.createdAt=n,e.expirationTime=o,e})}setDeviceId(t){return e(this,void 0,void 0,function*(){yield this.put("Ids",{type:"userId",id:t})})}setSubscription(t){return e(this,void 0,void 0,function*(){t.deviceId&&(yield this.setDeviceId(t.deviceId)),void 0!==t.subscriptionToken&&(yield this.put("Ids",{type:"registrationId",id:t.subscriptionToken})),null!=t.optedOut&&(yield this.put("Options",{key:"optedOut",value:t.optedOut})),null!=t.createdAt&&(yield this.put("Options",{key:"subscriptionCreatedAt",value:t.createdAt})),null!=t.expirationTime?yield this.put("Options",{key:"subscriptionExpirationTime",value:t.expirationTime}):yield this.remove("Options","subscriptionExpirationTime")})}setJWTToken(t){return e(this,void 0,void 0,function*(){yield this.put("Ids",{type:"jwtToken",id:t})})}getJWTToken(){return e(this,void 0,void 0,function*(){return yield this.get("Ids","jwtToken")})}setProvideUserConsent(t){return e(this,void 0,void 0,function*(){yield this.put("Options",{key:"userConsent",value:t})})}getConsentGiven(){return e(this,void 0,void 0,function*(){return yield this.get("Options","userConsent")})}getSession(t){return e(this,void 0,void 0,function*(){return yield this.get("Sessions",t)})}setSession(t){return e(this,void 0,void 0,function*(){yield this.put("Sessions",t)})}removeSession(t){return e(this,void 0,void 0,function*(){yield this.remove("Sessions",t)})}getLastNotificationClickedForOutcomes(t){return e(this,void 0,void 0,function*(){let e=[];try{e=yield this.getAllNotificationClickedForOutcomes()}catch(i){R.error("Database.getLastNotificationClickedForOutcomes",i)}return e.find(e=>e.appId===t)||null})}getAllNotificationClickedForOutcomes(){return e(this,void 0,void 0,function*(){return(yield this.getAll(ea)).map(e=>es.fromDatabase(e))})}putNotificationClickedForOutcomes(t,i){return e(this,void 0,void 0,function*(){yield this.put(ea,es.toDatabase(t,i))})}putNotificationClickedEventPendingUrlOpening(t){return e(this,void 0,void 0,function*(){yield this.put("NotificationOpened",eo.toDatabase(t))})}getAllPendingNotificationClickEvents(){return e(this,void 0,void 0,function*(){let e={},t=yield this.getAll("NotificationOpened");for(let i of t){let n=eo.fromDatabase(i),o=n.result.url;o&&(e[o]=n)}return e})}removeAllNotificationClickedForOutcomes(){return e(this,void 0,void 0,function*(){yield this.remove(ea)})}getAllNotificationReceivedForOutcomes(){return e(this,void 0,void 0,function*(){return(yield this.getAll(ec)).map(e=>er.fromDatabase(e))})}putNotificationReceivedForOutcomes(t,i){return e(this,void 0,void 0,function*(){yield this.put(ec,er.toDatabase(t,i,(new Date).getTime()))})}resetSentUniqueOutcomes(){return e(this,void 0,void 0,function*(){yield Promise.all((yield this.getAll("SentUniqueOutcome")).map(e=>(e.sentDuringSession=null,ed.put("SentUniqueOutcome",e))))})}static rebuild(){return e(this,void 0,void 0,function*(){return Promise.all([ed.singletonInstance.remove("Ids"),ed.singletonInstance.remove("NotificationOpened"),ed.singletonInstance.remove("Options"),ed.singletonInstance.remove(ec),ed.singletonInstance.remove(ea),ed.singletonInstance.remove("SentUniqueOutcome")])})}static on(...t){return e(this,void 0,void 0,function*(){return ed.singletonInstance.emitter.on.apply(ed.singletonInstance.emitter,t)})}static setIsPushEnabled(t){return e(this,void 0,void 0,function*(){return ed.singletonInstance.setIsPushEnabled(t)})}static getCurrentSession(){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.getSession(ei)})}static upsertSession(t){return e(this,void 0,void 0,function*(){yield ed.singletonInstance.setSession(t)})}static cleanupCurrentSession(){return e(this,void 0,void 0,function*(){yield ed.singletonInstance.removeSession(ei)})}static setSubscription(t){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.setSubscription(t)})}static getSubscription(){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.getSubscription()})}static setJWTToken(t){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.setJWTToken(t)})}static getJWTToken(){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.getJWTToken()})}static setConsentGiven(t){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.setProvideUserConsent(t)})}static getConsentGiven(){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.getConsentGiven()})}static setAppState(t){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.setAppState(t)})}static getAppState(){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.getAppState()})}static setUserState(t){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.setUserState(t)})}static getUserState(){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.getUserState()})}static setAppConfig(t){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.setAppConfig(t)})}static getAppConfig(){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.getAppConfig()})}static getLastNotificationClickedForOutcomes(t){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.getLastNotificationClickedForOutcomes(t)})}static removeAllNotificationClickedForOutcomes(){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.removeAllNotificationClickedForOutcomes()})}static getAllNotificationReceivedForOutcomes(){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.getAllNotificationReceivedForOutcomes()})}static putNotificationReceivedForOutcomes(t,i){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.putNotificationReceivedForOutcomes(t,i)})}static getAllNotificationClickedForOutcomes(){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.getAllNotificationClickedForOutcomes()})}static putNotificationClickedForOutcomes(t,i){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.putNotificationClickedForOutcomes(t,i)})}static putNotificationClickedEventPendingUrlOpening(t){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.putNotificationClickedEventPendingUrlOpening(t)})}static resetSentUniqueOutcomes(){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.resetSentUniqueOutcomes()})}static setDeviceId(t){return e(this,void 0,void 0,function*(){yield ed.singletonInstance.setDeviceId(t)})}static remove(t,i){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.remove(t,i)})}static put(t,i){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.put(t,i)})}static get(t,i){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.get(t,i)})}static getAll(t){return e(this,void 0,void 0,function*(){return yield ed.singletonInstance.getAll(t)})}}ed.EVENTS=d;let el=["notifyButtonHovering","notifyButtonHover","notifyButtonButtonClick","notifyButtonLauncherClick","animatedElementHiding","animatedElementHidden","animatedElementShowing","animatedElementShown","activeAnimatedElementActivating","activeAnimatedElementActive","activeAnimatedElementInactivating","activeAnimatedElementInactive"];class eu{static trigger(t,i,n){return e(this,void 0,void 0,function*(){if(!Q.contains(el,t)){let e=i,o=Q.capitalize(s.getWindowEnv().toString());e||!1===e?R.debug(`(${o}) \xbb ${t}:`,e):R.debug(`(${o}) \xbb ${t}`)}if(_.isBrowser()){if(t===OneSignal.EVENTS.SDK_INITIALIZED){if(OneSignal.initialized)return;OneSignal.initialized=!0}n?yield n.emit(t,i):yield OneSignal.emitter.emit(t,i)}})}}class ep extends t{constructor(e,t){super("Registration of a Service Worker failed."),this.status=e,this.statusText=t,Object.setPrototypeOf(this,ep.prototype)}}let eg=ep;class ef{static getBaseUrl(){return location.origin}static isLocalhostAllowedAsSecureOrigin(){return OneSignal.config&&OneSignal.config.userConfig&&!0===OneSignal.config.userConfig.allowLocalhostAsSecureOrigin}static redetectBrowserUserAgent(){return""===U().name&&""===U().version?M()._detect(navigator.userAgent):M()}static isValidUuid(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/.test(e)}static getRandomUuid(){let e="undefined"==typeof window?i.g.crypto:window.crypto||window.msCrypto;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){let i=e.getRandomValues(new Uint8Array(1))[0]%16|0;return("x"==t?i:3&i|8).toString(16)})}static logMethodCall(e,...t){return R.debug(`Called ${e}(${t.map(J.stringify).join(", ")})`)}static isSafari(){return _.isBrowser()&&void 0!==window.safari}}let eh=ef;class ev{static debug(...e){self.shouldLog&&console.debug(...e)}static trace(...e){self.shouldLog&&console.trace(...e)}static info(...e){self.shouldLog&&console.info(...e)}static warn(...e){self.shouldLog&&console.warn(...e)}static error(...e){self.shouldLog&&console.error(...e)}}let em=()=>{ev.debug("Do nothing")};function eb(t,i){let n=1e3*i,o,s,r=new Promise((i,r)=>{let a=!1;o=self.setTimeout(()=>e(this,void 0,void 0,function*(){a=!0;try{yield t(),i()}catch(e){ev.error("Failed to execute callback",e),r()}}),n),s=()=>{ev.debug("Cancel called"),self.clearTimeout(o),a||i()}});return s?{promise:r,cancel:s}:(ev.warn("clearTimeoutHandle was not assigned."),{promise:r,cancel:em})}!function(e){e[e.Direct=1]="Direct",e[e.Indirect=2]="Indirect",e[e.Unattributed=3]="Unattributed",e[e.NotSupported=4]="NotSupported"}(l||(l={}));class ey{static triggerNotificationPermissionChanged(t=!1){return e(this,void 0,void 0,function*(){if(!ey.executing){ey.executing=!0;try{yield ey.privateTriggerNotificationPermissionChanged(t)}finally{ey.executing=!1}}})}static privateTriggerNotificationPermissionChanged(t){return e(this,void 0,void 0,function*(){let e=yield OneSignal.context.permissionManager.getPermissionStatus(),i=yield ed.get("Options","notificationPermission");(e!==i||t)&&(yield ed.put("Options",{key:"notificationPermission",value:e}),eu.trigger(OneSignal.EVENTS.NOTIFICATION_PERMISSION_CHANGED_AS_STRING,e),this.triggerBooleanPermissionChangeEvent(i,e,t))})}static triggerBooleanPermissionChangeEvent(e,t,i){let n="granted"===t;(n!==("granted"===e)||i)&&eu.trigger(OneSignal.EVENTS.NOTIFICATION_PERMISSION_CHANGED_AS_BOOLEAN,n)}}function e$(){return e(this,void 0,void 0,function*(){return new Promise(e=>{OneSignal.initialized?e():OneSignal.emitter.once(OneSignal.EVENTS.SDK_INITIALIZED,e)})})}function eS(e,...t){return ef.logMethodCall(e,...t)}function ew(e,t){if("string"==typeof e){let i=document.querySelector(e);if(null===i)throw Error(`Cannot find element with selector "${e}"`);i.classList.add(t)}else{if("object"!=typeof e)throw Error(`${e} must be a CSS selector string or DOM Element object.`);e.classList.add(t)}}function eI(e){return ef.isValidUuid(e)}function ek(e){return JSON.parse(JSON.stringify(e))}ey.executing=!1;class eO{constructor(e,t,i,n){this.outcomeName=i,this.config=t,this.appId=e,this.isUnique=n}getAttribution(){return e(this,void 0,void 0,function*(){return yield eO.getAttribution(this.config)})}beforeOutcomeSend(){return e(this,void 0,void 0,function*(){return(eS(this.isUnique?"sendUniqueOutcome":"sendOutcome",this.outcomeName),this.config)?this.outcomeName?(yield e$(),!!(yield OneSignal.context.subscriptionManager.isPushNotificationsEnabled())||(R.warn("Reporting outcomes is supported only for subscribed users."),!1)):(R.error("Outcome name is required"),!1):(R.debug("Outcomes feature not supported by main application yet."),!1)})}getAttributedNotifsByUniqueOutcomeName(){return e(this,void 0,void 0,function*(){return(yield ed.getAll("SentUniqueOutcome")).filter(e=>e.outcomeName===this.outcomeName).reduce((e,t)=>[...e,...t.notificationIds||[]],[])})}getNotifsToAttributeWithUniqueOutcome(t){return e(this,void 0,void 0,function*(){let e=yield this.getAttributedNotifsByUniqueOutcomeName();return t.filter(t=>-1===e.indexOf(t))})}shouldSendUnique(e,t){return e.type===l.Unattributed||t.length>0}saveSentUniqueOutcome(t){return e(this,void 0,void 0,function*(){let e=this.outcomeName,i=yield ed.get("SentUniqueOutcome",e),n=yield ed.getCurrentSession(),o;yield ed.put("SentUniqueOutcome",{outcomeName:e,notificationIds:[...i?i.notificationIds:[],...t],sentDuringSession:n?n.startTimestamp:null})})}wasSentDuringSession(){return e(this,void 0,void 0,function*(){let e=yield ed.get("SentUniqueOutcome",this.outcomeName);if(!e)return!1;let t=yield ed.getCurrentSession(),i=t&&e.sentDuringSession===t.startTimestamp,n=!t&&!!e.sentDuringSession;return i||n})}send(t){return e(this,void 0,void 0,function*(){let{type:e,notificationIds:i,weight:n}=t;switch(e){case l.Direct:return this.isUnique&&(yield this.saveSentUniqueOutcome(i)),void(yield OneSignal.context.updateManager.sendOutcomeDirect(this.appId,i,this.outcomeName,n));case l.Indirect:return this.isUnique&&(yield this.saveSentUniqueOutcome(i)),void(yield OneSignal.context.updateManager.sendOutcomeInfluenced(this.appId,i,this.outcomeName,n));case l.Unattributed:if(this.isUnique){if(yield this.wasSentDuringSession())return void R.warn("(Unattributed) unique outcome was already sent during this session");yield this.saveSentUniqueOutcome([])}return void(yield OneSignal.context.updateManager.sendOutcomeUnattributed(this.appId,this.outcomeName,n));default:return void R.warn("You are on a free plan. Please upgrade to use this functionality.")}})}static getAttribution(t){return e(this,void 0,void 0,function*(){if(t.direct&&t.direct.enabled){let e=yield ed.getAllNotificationClickedForOutcomes();if(e.length>0)return{type:l.Direct,notificationIds:[e[0].notificationId]}}if(t.indirect&&t.indirect.enabled){let i=60*t.indirect.influencedTimePeriodMin*1e3,n=new Date((new Date).getTime()-i).getTime(),o=yield ed.getAllNotificationReceivedForOutcomes();if(R.debug(`	Found total of ${o.length} received notifications`),o.length>0){let s=t.indirect.influencedNotificationsLimit,r=J.sortArrayOfObjects(o,e=>e.timestamp,!0,!1),a=r.filter(e=>e.timestamp>=n).slice(0,s).map(e=>e.notificationId);R.debug(`	Total of ${a.length} received notifications are within reporting window.`);let c=r.filter(e=>-1===a.indexOf(e.notificationId)).map(e=>e.notificationId);if(c.forEach(e=>ed.remove(ec,e)),R.debug(`	${c.length} received notifications will be deleted.`),a.length>0)return{type:l.Indirect,notificationIds:a}}}return t.unattributed&&t.unattributed.enabled?{type:l.Unattributed,notificationIds:[]}:{type:l.NotSupported,notificationIds:[]}})}}(function(e){e.ChromePush="ChromePush",e.SafariPush="SafariPush",e.SafariLegacyPush="SafariLegacyPush",e.FirefoxPush="FirefoxPush",e.Email="Email",e.SMS="SMS"})(u||(u={})),function(e){e.Safari="safari",e.Firefox="firefox",e.Chrome="chrome",e.Opera="opera",e.Edge="edge",e.Other="other"}(p||(p={}));class eP{static getEnvironmentInfo(){return{browserType:this.getBrowser(),browserVersion:this.getBrowserVersion(),isBrowserAndSupportsServiceWorkers:this.supportsServiceWorkers(),requiresUserInteraction:this.requiresUserInteraction(),osVersion:this.getOsVersion()}}static getBrowser(){return"chrome"===U().name?p.Chrome:"msedge"===U().name?p.Edge:"opera"===U().name?p.Opera:"firefox"===U().name?p.Firefox:this.isMacOSSafari()?p.Safari:p.Other}static isMacOSSafari(){return void 0!==window.safari}static getBrowserVersion(){return Q.parseVersionString(U().version)}static supportsServiceWorkers(){return window.navigator&&"serviceWorker"in window.navigator}static requiresUserInteraction(){return"firefox"===this.getBrowser()&&this.getBrowserVersion()>=72||"safari"===this.getBrowser()&&this.getBrowserVersion()>=12.1}static getOsVersion(){return M().osversion}}(function(e){e[e.NoNativePermission=0]="NoNativePermission",e[e.Subscribed=1]="Subscribed",e[e.UserOptedOut=-2]="UserOptedOut",e[e.NotSubscribed=-10]="NotSubscribed",e[e.TemporaryWebRecord=-20]="TemporaryWebRecord",e[e.PermissionRevoked=-21]="PermissionRevoked",e[e.PushSubscriptionRevoked=-22]="PushSubscriptionRevoked",e[e.ServiceWorkerStatus403=-23]="ServiceWorkerStatus403",e[e.ServiceWorkerStatus404=-24]="ServiceWorkerStatus404"})(g||(g={})),function(e){e[e.ChromeLike=5]="ChromeLike",e[e.SafariLegacy=7]="SafariLegacy",e[e.Firefox=8]="Firefox",e[e.Email=11]="Email",e[e.Edge=12]="Edge",e[e.SMS=14]="SMS",e[e.SafariVapid=17]="SafariVapid"}(f||(f={}));class ex{constructor(e){let t=eP.getEnvironmentInfo();this.token=this._getToken(e),this.type=ex.getSubscriptionType(),this.notificationTypes=g.Subscribed,this.sdk=String(160201),this.deviceModel=navigator.platform,this.deviceOs=isNaN(t.browserVersion)?-1:t.browserVersion,this.webAuth=e.w3cAuth,this.webp256=e.w3cP256dh}_getToken(e){return e.w3cEndpoint?e.w3cEndpoint.toString():e.safariDeviceToken}serialize(){return{type:this.type,token:this.token,enabled:this.enabled,notification_types:this.notificationTypes,sdk:this.sdk,device_model:this.deviceModel,device_os:this.deviceOs,web_auth:this.webAuth,web_p256:this.webp256}}static getSubscriptionType(){return eh.redetectBrowserUserAgent().firefox?u.FirefoxPush:_.useSafariVapidPush()?u.SafariPush:_.useSafariLegacyPush()?u.SafariLegacyPush:u.ChromePush}static getDeviceType(){switch(this.getSubscriptionType()){case u.FirefoxPush:return f.Firefox;case u.SafariLegacyPush:return f.SafariLegacy;case u.SafariPush:return f.SafariVapid}return f.ChromeLike}}class eC{constructor(e,t){this.label=e,this.id=t}}eC.ONESIGNAL_ID="onesignal_id",eC.EXTERNAL_ID="external_id",function(e){e[e.MissingAppId=0]="MissingAppId",e[e.RetryLimitReached=1]="RetryLimitReached"}(h||(h={}));class eN extends t{constructor(e){let t;switch(e){case h.MissingAppId:t="The API call is missing an app ID.";break;case h.RetryLimitReached:t="The API call has reached the retry limit."}super(t),Object.setPrototypeOf(this,eN.prototype)}}function eT(e){return new Promise(t=>setTimeout(t,e))}let eA={5:1e4,4:2e4,3:3e4,2:3e4,1:3e4};class eE{static get(e,t,i){return eE.call("GET",e,t,i)}static post(e,t,i){return eE.call("POST",e,t,i)}static put(e,t,i){return eE.call("PUT",e,t,i)}static delete(e,t,i){return eE.call("DELETE",e,t,i)}static patch(e,t,i){return eE.call("PATCH",e,t,i)}static call(e,t,i,n){if(!this.requestHasAppId(t,i))return Promise.reject(new eN(h.MissingAppId));let o=new Headers;if(o.append("Origin",s.getOrigin()),o.append("SDK-Version",`onesignal/web/${_.version()}`),o.append("Content-Type","application/json;charset=UTF-8"),n)for(let r of Object.keys(n))o.append(r,n[r]);let a={method:e||"NO_METHOD_SPECIFIED",headers:o,cache:"no-cache"};i&&(a.body=JSON.stringify(i));let c=`${s.getOneSignalApiUrl(void 0,t).toString()}/${t}`;return eE.executeFetch(c,a)}static executeFetch(i,n,o=5){return e(this,void 0,void 0,function*(){if(0===o)return Promise.reject(new eN(h.RetryLimitReached));try{let e=yield fetch(i,n),{status:s}=e;return{result:yield e.json(),status:s}}catch(r){if("TypeError"===r.name)return yield eT(eA[o]),R.error(`OneSignal: Network timed out while calling ${i}. Retrying...`),eE.executeFetch(i,n,o-1);throw new t(`OneSignalApiBase: failed to execute HTTP call: ${r}`)}})}static requestHasAppId(e,t){return e.startsWith("apps/")?eI(e.split("/")[1]):!(!t||"string"!=typeof t.app_id)&&eI(t.app_id)}}let eD=eE;function eM(e){return encodeURIComponent(e).replace(/[!'()*]/g,e=>`%${e.charCodeAt(0).toString(16).toUpperCase()}`)}!function(e){e[e.InvalidAppId=0]="InvalidAppId",e[e.AppNotConfiguredForWebPush=1]="AppNotConfiguredForWebPush",e[e.WrongSiteUrl=2]="WrongSiteUrl",e[e.MultipleInitialization=3]="MultipleInitialization",e[e.MissingSafariWebId=4]="MissingSafariWebId",e[e.Unknown=5]="Unknown"}(v||(v={}));class eU extends t{constructor(e,t){let i;switch(e){case v.InvalidAppId:i="OneSignal: This app ID does not match any existing app. Double check your app ID.";break;case v.AppNotConfiguredForWebPush:i="OneSignal: This app ID does not have any web platforms enabled. Double check your app ID, or see step 1 on our setup guide (https://tinyurl.com/2x5jzk83).";break;case v.WrongSiteUrl:i=t&&t.siteUrl?`OneSignal: This web push config can only be used on ${new URL(t.siteUrl).origin}. Your current origin is ${location.origin}.`:"OneSignal: This web push config can not be used on the current site.";break;case v.MultipleInitialization:i="OneSignal: The OneSignal web SDK can only be initialized once. Extra initializations are ignored. Please remove calls initializing the SDK more than once.";break;case v.MissingSafariWebId:i="OneSignal: Safari browser support on Mac OS X requires the Safari web platform to be enabled. Please see the Safari Support steps in our web setup guide.";break;case v.Unknown:i="OneSignal: An unknown initialization error occurred."}super(i),this.reason=v[e],Object.setPrototypeOf(this,eU.prototype)}}class eW{static createUser(t,i){return e(this,void 0,void 0,function*(){let{appId:e,subscriptionId:n}=t,o=n?{"OneSignal-Subscription-Id":n}:void 0,s={};return o&&(s=Object.assign(Object.assign({},s),o)),t.jwtHeader&&(s=Object.assign(Object.assign({},s),t.jwtHeader)),i.refresh_device_metadata=!0,eD.post(`apps/${e}/users`,i,s)})}static getUser(t,i){return e(this,void 0,void 0,function*(){let{appId:e}=t;return eD.get(`apps/${e}/users/by/${i.label}/${i.id}`,t.jwtHeader)})}static updateUser(t,i,n){return e(this,void 0,void 0,function*(){let{appId:e,subscriptionId:o}=t;if(!eh.isValidUuid(e))throw new eU(v.InvalidAppId);let s=o?{"OneSignal-Subscription-Id":o}:void 0,r={};s&&(r=Object.assign(Object.assign({},r),s)),t.jwtHeader&&(r=Object.assign(Object.assign({},r),t.jwtHeader));let a=eM(i.label),c=eM(i.id);return eD.patch(`apps/${e}/users/by/${a}/${c}`,n,r)})}static deleteUser(t,i){return e(this,void 0,void 0,function*(){let{appId:e}=t;return eD.delete(`apps/${e}/users/by/${i.label}/${i.id}`,t.jwtHeader)})}static addAlias(t,i,n){return e(this,void 0,void 0,function*(){let{appId:e}=t;return eD.patch(`apps/${e}/users/by/${i.label}/${i.id}/identity`,{identity:n},t.jwtHeader)})}static getUserIdentity(t,i){return e(this,void 0,void 0,function*(){let{appId:e}=t;return eD.get(`apps/${e}/users/by/${i.label}/${i.id}/identity`,t.jwtHeader)})}static deleteAlias(t,i,n){return e(this,void 0,void 0,function*(){let{appId:e}=t;return eD.delete(`apps/${e}/users/by/${i.label}/${i.id}/identity/${n}`,t.jwtHeader)})}static createSubscription(t,i,n){return e(this,void 0,void 0,function*(){let{appId:e}=t;return eD.post(`apps/${e}/users/by/${i.label}/${i.id}/subscriptions`,n,t.jwtHeader)})}static updateSubscription(t,i,n){return e(this,void 0,void 0,function*(){let{appId:e}=t;return eD.patch(`apps/${e}/subscriptions/${i}`,{subscription:n})})}static deleteSubscription(t,i){return e(this,void 0,void 0,function*(){let{appId:e}=t;return eD.delete(`apps/${e}/subscriptions/${i}`)})}static fetchAliasesForSubscription(t,i){return e(this,void 0,void 0,function*(){let{appId:e}=t;return eD.get(`apps/${e}/subscriptions/${i}/identity`)})}static identifyUserForSubscription(t,i,n){return e(this,void 0,void 0,function*(){let{appId:e}=t;return eD.patch(`apps/${e}/users/by/subscriptions/${i}/identity`,{identity:n},t.jwtHeader)})}static transferSubscription(t,i,n,o){return e(this,void 0,void 0,function*(){let{appId:e}=t;return eD.patch(`apps/${e}/subscriptions/${i}/owner`,{identity:Object.assign({},n),retain_previous_owner:o},t.jwtHeader)})}}class e8{static sendNotification(e,t,i,n,o,s,r,a){let c={app_id:e,contents:n,include_player_ids:t,isAnyWeb:!0,data:r,web_buttons:a};return i&&(c.headings=i),o&&(c.url=o),s&&(c.chrome_web_icon=s,c.firefox_icon=s),Q.trimUndefined(c),eD.post("notifications",c)}static sendOutcome(t){return e(this,void 0,void 0,function*(){R.info("Outcome payload:",t);try{yield eD.post("outcomes/measure",t)}catch(e){R.error("sendOutcome",e)}})}}let e_=class{static downloadServerAppConfig(t){return e(this,void 0,void 0,function*(){Q.enforceAppId(t);let e=yield eE.get(`sync/${t}/web`,null);return null==e?void 0:e.result})}static getUserIdFromSubscriptionIdentifier(e,t,i){return Q.enforceAppId(e),eE.post("players",{app_id:e,device_type:t,identifier:i,notification_types:g.TemporaryWebRecord}).then(e=>e&&e.id?e.id:null).catch(e=>(R.debug("Error getting user ID from subscription identifier:",e),null))}static updateUserSession(t,i,n){return e(this,void 0,void 0,function*(){let e=new eC(eC.ONESIGNAL_ID,i);Q.enforceAppId(t),Q.enforceAlias(e);try{yield eW.updateUser({appId:t,subscriptionId:n},e,{refresh_device_metadata:!0,deltas:{session_count:1}})}catch(o){R.debug("Error updating user session:",o)}})}static sendSessionDuration(t,i,n,o,s){return e(this,void 0,void 0,function*(){let e=new eC(eC.ONESIGNAL_ID,i),r={id:"os__session_duration",app_id:t,session_time:o,notification_ids:s.notificationIds,subscription:{id:n,type:ex.getSubscriptionType()},onesignal_id:i};r.direct=s.type===l.Direct;try{yield eW.updateUser({appId:t,subscriptionId:n},e,{refresh_device_metadata:!0,deltas:{session_time:o}}),r.notification_ids&&r.notification_ids.length>0&&(yield e8.sendOutcome(r))}catch(a){R.debug("Error sending session duration:",a)}})}};class eR{static getServiceWorkerHref(e,t,i){return eR.appendServiceWorkerParams(e.workerPath.getFullPath(),t,i)}static appendServiceWorkerParams(e,t,i){return`${new URL(e,ef.getBaseUrl()).href}?${Q.encodeHashAsUriComponent({appId:t})}&${Q.encodeHashAsUriComponent({sdkVersion:i})}`}static upsertSession(t,i,n,o,s,r,c){return e(this,void 0,void 0,function*(){let e=yield ed.getCurrentSession();if(!e){let d=en({appId:t}),l=yield ed.getLastNotificationClickedForOutcomes(t);return l&&(d.notificationId=l.notificationId),yield ed.upsertSession(d),void(yield eR.sendOnSessionCallIfNotPlayerCreate(t,i,n,r,d))}if(e.status===a.Active)return void ev.debug("Session already active",e);if(!e.lastDeactivatedTimestamp)return void ev.debug("Session is in invalid state",e);let u=(new Date).getTime();if(eR.timeInSecondsBetweenTimestamps(u,e.lastDeactivatedTimestamp)<=o)return e.status=a.Active,e.lastActivatedTimestamp=u,e.lastDeactivatedTimestamp=null,void(yield ed.upsertSession(e));yield eR.finalizeSession(t,i,n,e,s,c);let p=en({appId:t});yield ed.upsertSession(p),yield eR.sendOnSessionCallIfNotPlayerCreate(t,i,n,r,p)})}static deactivateSession(t,i,n,o,s,r){return e(this,void 0,void 0,function*(){let e=yield ed.getCurrentSession();if(!e)return void ev.debug("No active session found. Cannot deactivate.");let c=()=>eR.finalizeSession(t,i,n,e,s,r);if(e.status===a.Inactive)return eb(c,o);if(e.status!==a.Active)return void ev.warn(`Session in invalid state ${e.status}. Cannot deactivate.`);let d=(new Date).getTime(),l=eR.timeInSecondsBetweenTimestamps(d,e.lastActivatedTimestamp);e.lastDeactivatedTimestamp=d,e.accumulatedDuration+=l,e.status=a.Inactive;let u=eb(c,o);return yield ed.upsertSession(e),u})}static sendOnSessionCallIfNotPlayerCreate(t,i,n,o,s){return e(this,void 0,void 0,function*(){o!==c.UserCreate&&(ed.upsertSession(s),ed.resetSentUniqueOutcomes(),yield e_.updateUserSession(t,i,n))})}static finalizeSession(t,i,n,o,s,r){return e(this,void 0,void 0,function*(){if(ev.debug("Finalize session",`started: ${new Date(o.startTimestamp)}`,`duration: ${o.accumulatedDuration}s`),s){ev.debug(`send on_focus reporting session duration -> ${o.accumulatedDuration}s`);let e=yield eO.getAttribution(r);ev.debug("send on_focus with attribution",e),yield e_.sendSessionDuration(t,i,n,o.accumulatedDuration,e)}yield Promise.all([ed.cleanupCurrentSession(),ed.removeAllNotificationClickedForOutcomes()]),ev.debug("Finalize session finished",`started: ${new Date(o.startTimestamp)}`)})}static timeInSecondsBetweenTimestamps(e,t){return e<=t?0:Math.floor((e-t)/1e3)}}!function(e){e.OneSignalWorker="OneSignal Worker",e.ThirdParty="3rd Party",e.None="None",e.Indeterminate="Indeterminate"}(m||(m={}));let eV="onesignal-customlink-subscribe",eB="onesignal-customlink-explanation",eL="onesignal-reset",eF={subscribed:"state-subscribed",unsubscribed:"state-unsubscribed"},e9={containerSelector:".onesignal-customlink-container",subscribeSelector:`.${eV}`,explanationSelector:`.${eB}`};class e0{constructor(e){this.config=e}initialize(){return e(this,void 0,void 0,function*(){var e,t;if(!(null===(e=this.config)||void 0===e?void 0:e.enabled)||!(yield this.loadSdkStyles()))return;R.info("OneSignal: initializing customlink");let i=yield OneSignal.context.subscriptionManager.isPushNotificationsEnabled();if((null===(t=this.config)||void 0===t?void 0:t.unsubscribeEnabled)||!i)for(let n=0;n<this.customlinkContainerElements.length;n++)yield this.injectMarkup(this.customlinkContainerElements[n]);else this.hideCustomLinkContainers()})}injectMarkup(t){return e(this,void 0,void 0,function*(){t.innerHTML="",yield this.mountExplanationNode(t),yield this.mountSubscriptionNode(t)})}mountExplanationNode(t){return e(this,void 0,void 0,function*(){var e;if(null===(e=this.config)||void 0===e?void 0:e.text){if(this.config.text.explanation){let i=document.createElement("p");i.textContent=this.config.text.explanation,ew(i,eL),ew(i,eB),this.config.size&&ew(i,this.config.size),(yield OneSignal.context.subscriptionManager.isPushNotificationsEnabled())?ew(i,eF.subscribed):ew(i,eF.unsubscribed),t.appendChild(i)}}else R.error("CustomLink: required property 'text' is missing in the config")})}mountSubscriptionNode(t){return e(this,void 0,void 0,function*(){var i;if(null===(i=this.config)||void 0===i?void 0:i.text){if(this.config.text.subscribe){let n=document.createElement("button");ew(n,eL),ew(n,eV),this.config.size&&ew(n,this.config.size),this.config.style&&ew(n,this.config.style),(yield OneSignal.context.subscriptionManager.isPushNotificationsEnabled())?ew(n,eF.subscribed):ew(n,eF.unsubscribed),this.setCustomColors(n),yield this.setTextFromPushStatus(n),n.addEventListener("click",()=>e(this,void 0,void 0,function*(){R.info("CustomLink: subscribe clicked"),yield this.handleClick(n)})),n.setAttribute("type","button"),t.appendChild(n)}}else R.error("CustomLink: required property 'text' is missing in the config")})}loadSdkStyles(){return e(this,void 0,void 0,function*(){return 0===(yield OneSignal.context.dynamicResourceLoader.loadSdkStylesheet())||(R.debug("Not initializing custom link button because styles failed to load."),!1)})}hideElement(e){ew(e,"hide")}hideCustomLinkContainers(){this.customlinkContainerElements.forEach(e=>{this.hideElement(e)})}handleClick(t){return e(this,void 0,void 0,function*(){var e;if(!OneSignal.User.PushSubscription.optedIn)return yield OneSignal.User.PushSubscription.optIn(),void((null===(e=this.config)||void 0===e?void 0:e.unsubscribeEnabled)||this.hideCustomLinkContainers());yield OneSignal.User.PushSubscription.optOut(),yield this.setTextFromPushStatus(t)})}setTextFromPushStatus(t){return e(this,void 0,void 0,function*(){var e,i,n,o;(null===(i=null===(e=this.config)||void 0===e?void 0:e.text)||void 0===i?void 0:i.subscribe)&&((yield OneSignal.context.subscriptionManager.isPushNotificationsEnabled())||(t.textContent=this.config.text.subscribe)),(null===(o=null===(n=this.config)||void 0===n?void 0:n.text)||void 0===o?void 0:o.unsubscribe)&&(yield OneSignal.context.subscriptionManager.isPushNotificationsEnabled())&&(t.textContent=this.config.text.unsubscribe)})}setCustomColors(e){var t,i,n,o,s,r,a;(null===(t=this.config)||void 0===t?void 0:t.color)&&this.config.color.text&&("button"===(null===(i=this.config)||void 0===i?void 0:i.style)&&(null===(n=this.config)||void 0===n?void 0:n.color.button)?(e.style.backgroundColor=null===(o=this.config)||void 0===o?void 0:o.color.button,e.style.color=null===(s=this.config)||void 0===s?void 0:s.color.text):"link"===(null===(r=this.config)||void 0===r?void 0:r.style)&&(e.style.color=null===(a=this.config)||void 0===a?void 0:a.color.text))}get customlinkContainerElements(){let e=document.querySelectorAll(e9.containerSelector);return Array.prototype.slice.call(e)}}class eH{static put(e,t){return void 0===eH.store[e]&&(eH.store[e]=[null,null]),eH.store[e].push(t),eH.store[e].length==eH.LIMIT+1&&eH.store[e].shift(),eH.store[e]}static get(e){return void 0===eH.store[e]&&(eH.store[e]=[null,null]),eH.store[e]}static getFirst(e){return eH.get(e)[0]}static getLast(e){return eH.get(e)[1]}static remove(e){delete eH.store[e]}static isEmpty(e){let t=eH.get(e);return null===t[0]&&null===t[1]}}eH.store={},eH.LIMIT=2;let eK=class{static decodeHtmlEntities(e){return"undefined"==typeof DOMParser?e:(new DOMParser).parseFromString(e,"text/html").documentElement.textContent||""}};!function(e){e.Native="native",e.Push="push",e.Category="category",e.Sms="sms",e.Email="email",e.SmsAndEmail="smsAndEmail"}(b||(b={}));class ej{static isCategorySlidedownConfigured(e){if(!e)return!1;let t=ej.getFirstSlidedownPromptOptionsWithType(e,b.Category);return!!t&&!!t.categories&&t.categories.length>0}static isCategorySlidedownConfiguredVersion1(e){var t,i;return((null===(i=null===(t=null==e?void 0:e.categories)||void 0===t?void 0:t.tags)||void 0===i?void 0:i.length)||0)>0}static getFirstSlidedownPromptOptionsWithType(e,t){return e?e.filter(e=>e.type===t)[0]:void 0}static isSlidedownAutoPromptConfigured(e){if(!e)return!1;for(let t=0;t<e.length;t++)if(e[t].autoPrompt)return!0;return!1}static isSlidedownPushDependent(e){return e===b.Push||e===b.Category}}(function(e){e[e.HttpsPermissionRequest="HTTPS permission request"]="HttpsPermissionRequest",e[e.SlidedownPermissionMessage="slidedown permission message"]="SlidedownPermissionMessage",e[e.SubscriptionBell="subscription bell"]="SubscriptionBell"})(y||(y={})),function(e){e[e.MissingAppId=0]="MissingAppId",e[e.RedundantPermissionMessage=1]="RedundantPermissionMessage",e[e.PushPermissionAlreadyGranted=2]="PushPermissionAlreadyGranted",e[e.UnsupportedEnvironment=3]="UnsupportedEnvironment",e[e.MissingDomElement=4]="MissingDomElement",e[e.ServiceWorkerNotActivated=5]="ServiceWorkerNotActivated"}($||($={}));class e1 extends t{constructor(e,t){let i;switch(e){case $.MissingAppId:i="Missing required app ID.";break;case $.RedundantPermissionMessage:{let n="";t&&t.permissionPromptType&&(n=`(${y[t.permissionPromptType]})`),i=`Another permission message ${n} is being displayed.`;break}case $.PushPermissionAlreadyGranted:i="Push permission has already been granted.";break;case $.UnsupportedEnvironment:i="The current environment does not support this operation.";break;case $.ServiceWorkerNotActivated:i="The service worker must be activated first."}super(i),this.description=$[e],this.reason=e,Object.setPrototypeOf(this,e1.prototype)}}class ez{static checkAndTriggerNotificationPermissionChanged(){return e(this,void 0,void 0,function*(){let e=yield ed.get("Options","notificationPermission"),t=yield OneSignal.context.permissionManager.getPermissionStatus();e!==t&&(yield ey.triggerNotificationPermissionChanged(),yield ed.put("Options",{key:"notificationPermission",value:t}))})}static getNotificationIcons(){return e(this,void 0,void 0,function*(){let e=yield ez.getAppId();if(!e)throw new e1($.MissingAppId);let t=`${s.getOneSignalApiUrl().toString()}/apps/${e}/icon`,i=yield(yield fetch(t)).json();if(i.errors)throw R.error(`API call ${t}`,"failed with:",i.errors),Error("Failed to get notification icons.");return i})}static getSlidedownOptions(e){return Q.getValueOrDefault(e.slidedown,{prompts:[]})}static getFullscreenPermissionMessageOptions(e){return e?e.fullscreen?{autoAcceptTitle:e.fullscreen.autoAcceptTitle,actionMessage:e.fullscreen.actionMessage,exampleNotificationTitleDesktop:e.fullscreen.title,exampleNotificationTitleMobile:e.fullscreen.title,exampleNotificationMessageDesktop:e.fullscreen.message,exampleNotificationMessageMobile:e.fullscreen.message,exampleNotificationCaption:e.fullscreen.caption,acceptButton:e.fullscreen.acceptButton,cancelButton:e.fullscreen.cancelButton}:e:null}static getPromptOptionsQueryString(){let e="";if(ez.getFullscreenPermissionMessageOptions(OneSignal.config.userConfig.promptOptions)){let t=ez.getPromptOptionsPostHash();for(let i of Object.keys(t))e+="&"+i+"="+t[i]}return e}static getPromptOptionsPostHash(){let e=ez.getFullscreenPermissionMessageOptions(OneSignal.config.userConfig.promptOptions),t={};if(e){let i={exampleNotificationTitleDesktop:"exampleNotificationTitle",exampleNotificationMessageDesktop:"exampleNotificationMessage",exampleNotificationTitleMobile:"exampleNotificationTitle",exampleNotificationMessageMobile:"exampleNotificationMessage"};for(let n of Object.keys(i)){let o=i[n];e[n]&&(e[o]=e[n])}let s=["autoAcceptTitle","siteName","autoAcceptTitle","subscribeText","showGraphic","actionMessage","exampleNotificationTitle","exampleNotificationMessage","exampleNotificationCaption","acceptButton","cancelButton","timeout"];for(let r=0;r<s.length;r++){let a=s[r],c=e[a],d=encodeURIComponent(c);(c||!1===c||""===c)&&(t[a]=d)}}return t}static getAppId(){return e(this,void 0,void 0,function*(){return OneSignal.config.appId?Promise.resolve(OneSignal.config.appId):yield ed.get("Ids","appId")})}static getDeviceId(){return e(this,void 0,void 0,function*(){return(yield OneSignal.database.getSubscription()).deviceId||void 0})}static getCurrentPushToken(){return e(this,void 0,void 0,function*(){var e,t;if(_.useSafariLegacyPush()){let i=null===(t=null===(e=window.safari)||void 0===e?void 0:e.pushNotification)||void 0===t?void 0:t.permission(OneSignal.config.safariWebId).deviceToken;return(null==i?void 0:i.toLowerCase())||void 0}let n=yield OneSignal.context.serviceWorkerManager.getRegistration();if(!n)return;let o=yield n.pushManager.getSubscription();return null==o?void 0:o.endpoint})}}class eq{constructor(e,t,i,n){this.model=e,this.property=t,this.oldValue=i,this.newValue=n}}class e3{constructor(){this.subscribers=new Set}subscribe(e){return this.subscribers.add(e),()=>this.subscribers.delete(e)}broadcast(e){this.subscribers.forEach(t=>{t(e)})}}!function(e){e.Add="add",e.Remove="remove",e.Update="update",e.Hydrate="hydrate"}(S||(S={}));class eG{constructor(e,t){this.modelId=e,this.payload=t,this.type=S.Update}}class e2{constructor(e,t){this.modelId=e,this.payload=t,this.type=S.Hydrate}}class e7 extends e3{constructor(e,t,i){super(),this.modelName=e,this.modelId=null!=i?i:Math.random().toString(36).substring(2),this.modelName=e,this.data=t,this.onesignalId=void 0,this.awaitOneSignalIdAvailable=new Promise(e=>{this.onesignalIdAvailableCallback=e})}setOneSignalId(e){var t;eS("setOneSignalId",{onesignalId:e}),this.onesignalId=e,e&&(null===(t=this.onesignalIdAvailableCallback)||void 0===t||t.call(this,e))}set(e,t,i=!0){let n;if(eS("set",{property:e,newValue:t}),this.data&&(n=this.data[e],this.data[e]=t),i){let o=new eG(this.modelId,new eq(this,e,n,t));this.broadcast(o)}}hydrate(e){eS("hydrate",{data:e}),this.data=e,this.broadcast(new e2(this.modelId,this))}encode(){let e=this.modelId,t;return Object.assign({modelId:e,modelName:this.modelName,onesignalId:this.onesignalId},this.data)}static decode(e){eS("decode",{encodedModel:e});let{modelId:t,modelName:i,onesignalId:n}=e,o=function(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(i[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(i[n[o]]=e[n[o]])}return i}(e,["modelId","modelName","onesignalId"]),s=new e7(i,o,t);return s.setOneSignalId(n),s}}function e4(e){return void 0!==(null==e?void 0:e.type)&&void 0!==(null==e?void 0:e.id)}class eY{static initializeUser(t){return e(this,void 0,void 0,function*(){eS("initializeUser",{isTemporary:t});let e=OneSignal.coreDirector.getIdentityModel(),i=OneSignal.coreDirector.getPropertiesModel();e&&i?R.debug("User already exists, skipping initialization."):(eY.createUserPropertiesModel(),yield eY.createAnonymousUser(t))})}static resetUserMetaProperties(){let e=e6.createOrGetInstance();e.hasOneSignalId=!1,e.awaitOneSignalIdAvailable=void 0,e.isCreatingUser=!1}static createAnonymousUser(t){return e(this,void 0,void 0,function*(){let e;if(t)e={};else{let i=yield eY.createUserOnServer();if(!i)return;e=i.identity,OneSignal.coreDirector.hydrateUser(i)}let n=new e7(r.Identity,e);n.setOneSignalId(e.onesignal_id),OneSignal.coreDirector.add(r.Identity,n,!1),yield this.copyOneSignalIdPromiseFromIdentityModel()})}static createUserPropertiesModel(){let e={language:_.getLanguage(),timezone_id:Intl.DateTimeFormat().resolvedOptions().timeZone},t=new e7(r.Properties,e);return OneSignal.coreDirector.add(r.Properties,t,!1),t}static createUserOnServer(){return e(this,void 0,void 0,function*(){let e=e6.createOrGetInstance();if(!e.isCreatingUser){e.isCreatingUser=!0;try{let t=yield ez.getAppId(),i=yield OneSignal.coreDirector.getPushSubscriptionModel(),n;e4(null==i?void 0:i.data)&&(n=null==i?void 0:i.data.id);let o=yield eW.createUser({appId:t,subscriptionId:n},(yield eY.getAllUserData()));return e.isCreatingUser=!1,o.result}catch(s){R.error(s)}}})}static createAndHydrateUser(){return e(this,void 0,void 0,function*(){let e=yield eY.createUserOnServer();e&&OneSignal.coreDirector.hydrateUser(e)})}static getAllUserData(){return e(this,void 0,void 0,function*(){eS("LoginManager.getAllUserData");let e=OneSignal.coreDirector.getIdentityModel(),t=OneSignal.coreDirector.getPropertiesModel(),i=yield OneSignal.coreDirector.getAllSubscriptionsModels(),n={};return n.identity=null==e?void 0:e.data,n.properties=null==t?void 0:t.data,n.subscriptions=null==i?void 0:i.map(e=>e.data),n})}static copyOneSignalIdPromiseFromIdentityModel(){return e(this,void 0,void 0,function*(){var e;let t=e6.createOrGetInstance(),i=OneSignal.coreDirector.getIdentityModel();t.awaitOneSignalIdAvailable=null==i?void 0:i.awaitOneSignalIdAvailable,null===(e=t.awaitOneSignalIdAvailable)||void 0===e||e.then(e=>{t.hasOneSignalId=!0,t.onesignalId=e})})}static updateModelWithCurrentUserOneSignalId(t){return e(this,void 0,void 0,function*(){let e=e6.createOrGetInstance();yield e.awaitOneSignalIdAvailable,t.setOneSignalId(e.onesignalId)})}}class e6{constructor(){this.hasOneSignalId=!1,this.isCreatingUser=!1}static createOrGetInstance(){return e6.singletonInstance||(e6.singletonInstance=new e6,eY.initializeUser(!0).then(()=>{eY.copyOneSignalIdPromiseFromIdentityModel().catch(e=>{console.error(e)})}).catch(e=>{console.error(e)})),e6.singletonInstance}addAlias(e,t){if(eS("addAlias",{label:e,id:t}),"string"!=typeof e)throw new n("label",A.WrongType);if("string"!=typeof t)throw new n("id",A.WrongType);if(!e)throw new n("label",A.Empty);if(!t)throw new n("id",A.Empty);this.addAliases({[e]:t})}addAliases(t){if(eS("addAliases",{aliases:t}),!t||0===Object.keys(t).length)throw new n("aliases",A.Empty);Object.keys(t).forEach(t=>e(this,void 0,void 0,function*(){if("string"!=typeof t)throw new n("label",A.WrongType)})),Object.keys(t).forEach(i=>e(this,void 0,void 0,function*(){let e=OneSignal.coreDirector.getIdentityModel();null==e||e.set(i,t[i])}))}removeAlias(e){if(eS("removeAlias",{label:e}),"string"!=typeof e)throw new n("label",A.WrongType);if(!e)throw new n("label",A.Empty);this.removeAliases([e])}removeAliases(t){if(eS("removeAliases",{aliases:t}),!t||0===t.length)throw new n("aliases",A.Empty);t.forEach(t=>e(this,void 0,void 0,function*(){let e=OneSignal.coreDirector.getIdentityModel();null==e||e.set(t,void 0)}))}addEmail(t){return e(this,void 0,void 0,function*(){var e,i,o,s;if(eS("addEmail",{email:t}),"string"!=typeof t)throw new n("email",A.WrongType);if(!t)throw new n("email",A.Empty);if(!((s=t)&&s.match(/^([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x22([^\x0d\x22\x5c\x80-\xff]|\x5c[\x00-\x7f])*\x22)(\x2e([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x22([^\x0d\x22\x5c\x80-\xff]|\x5c[\x00-\x7f])*\x22))*\x40([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x5b([^\x0d\x5b-\x5d\x80-\xff]|\x5c[\x00-\x7f])*\x5d)(\x2e([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x5b([^\x0d\x5b-\x5d\x80-\xff]|\x5c[\x00-\x7f])*\x5d))*$/)))throw new n("email",A.Malformed);let a={type:u.Email,token:t},c=new e7(r.EmailSubscriptions,a);(null===(e=e6.singletonInstance)||void 0===e?void 0:e.isCreatingUser)||(null===(i=e6.singletonInstance)||void 0===i?void 0:i.hasOneSignalId)?(c.setOneSignalId(null===(o=e6.singletonInstance)||void 0===o?void 0:o.onesignalId),OneSignal.coreDirector.add(r.EmailSubscriptions,c,!0)):(OneSignal.coreDirector.add(r.EmailSubscriptions,c,!1),yield eY.createAndHydrateUser()),eY.updateModelWithCurrentUserOneSignalId(c).catch(e=>{throw e})})}addSms(t){return e(this,void 0,void 0,function*(){var e,i,o;if(eS("addSms",{sms:t}),"string"!=typeof t)throw new n("sms",A.WrongType);if(!t)throw new n("sms",A.Empty);let s={type:u.SMS,token:t},a=new e7(r.SmsSubscriptions,s);(null===(e=e6.singletonInstance)||void 0===e?void 0:e.isCreatingUser)||(null===(i=e6.singletonInstance)||void 0===i?void 0:i.hasOneSignalId)?(a.setOneSignalId(null===(o=e6.singletonInstance)||void 0===o?void 0:o.onesignalId),OneSignal.coreDirector.add(r.SmsSubscriptions,a,!0)):(OneSignal.coreDirector.add(r.SmsSubscriptions,a,!1),yield eY.createAndHydrateUser()),eY.updateModelWithCurrentUserOneSignalId(a).catch(e=>{throw e})})}removeEmail(t){if(eS("removeEmail",{email:t}),"string"!=typeof t)throw new n("email",A.WrongType);if(!t)throw new n("email",A.Empty);let i=OneSignal.coreDirector.getEmailSubscriptionModels();Object.keys(i).forEach(n=>e(this,void 0,void 0,function*(){var e;(null===(e=i[n].data)||void 0===e?void 0:e.token)===t&&OneSignal.coreDirector.remove(r.EmailSubscriptions,n)}))}removeSms(t){if(eS("removeSms",{smsNumber:t}),"string"!=typeof t)throw new n("smsNumber",A.WrongType);if(!t)throw new n("smsNumber",A.Empty);let i=OneSignal.coreDirector.getSmsSubscriptionModels();Object.keys(i).forEach(n=>e(this,void 0,void 0,function*(){var e;(null===(e=i[n].data)||void 0===e?void 0:e.token)===t&&OneSignal.coreDirector.remove(r.SmsSubscriptions,n)}))}addTag(e,t){if(eS("addTag",{key:e,value:t}),"string"!=typeof e)throw new n("key",A.WrongType);if("string"!=typeof t)throw new n("value",A.WrongType);if(!e)throw new n("key",A.Empty);if(!t)throw new n("value",A.Empty,"Did you mean to call removeTag?");this.addTags({[e]:t})}addTags(e){var t;if(eS("addTags",{tags:e}),"object"!=typeof e)throw new n("tags",A.WrongType);if(!e)throw new n("tags",A.Empty);let i=OneSignal.coreDirector.getPropertiesModel();e=Object.assign(Object.assign({},null===(t=null==i?void 0:i.data)||void 0===t?void 0:t.tags),e),null==i||i.set("tags",e)}removeTag(e){if(eS("removeTag",{tagKey:e}),"string"!=typeof e)throw new n("tagKey",A.WrongType);if(void 0===e)throw new n("tagKey",A.Empty);this.removeTags([e])}removeTags(e){var t;if(eS("removeTags",{tagKeys:e}),!e||0===e.length)throw new n("tagKeys",A.Empty);let i=OneSignal.coreDirector.getPropertiesModel(),o=JSON.parse(JSON.stringify(null===(t=null==i?void 0:i.data)||void 0===t?void 0:t.tags));o&&(e.forEach(e=>{o[e]=""}),null==i||i.set("tags",o))}getTags(){var e,t;return eS("getTags"),null===(t=null===(e=OneSignal.coreDirector.getPropertiesModel())||void 0===e?void 0:e.data)||void 0===t?void 0:t.tags}}e6.singletonInstance=void 0;class e5{static isValidUrl(e,t){if(t&&t.allowNull&&null===e||t&&t.allowEmpty&&null==e)return!0;try{let i=new URL(e);return!t||!t.requireHttps||"https:"===i.protocol}catch(n){return!1}}static isValidBoolean(e,t){return!(!t||!t.allowNull||null!==e)||!0===e||!1===e}static isValidArray(e,t){return!(!t||!t.allowNull||null!==e)||!(!t||!t.allowEmpty||null!=e)||e instanceof Array}}class eJ{}class eQ extends eJ{constructor(t,i,n){super(),t&&i?(this._optedIn=!i.optedOut,this._permission=n,this._token=i.subscriptionToken,OneSignal.coreDirector.getPushSubscriptionModel().then(e=>{e&&e4(e.data)&&(this._id=e.data.id)}).catch(e=>{R.error(e)}),OneSignal.emitter.on(OneSignal.EVENTS.SUBSCRIPTION_CHANGED,t=>e(this,void 0,void 0,function*(){this._id=null==t?void 0:t.current.id,this._token=null==t?void 0:t.current.token})),OneSignal.emitter.on(OneSignal.EVENTS.NOTIFICATION_PERMISSION_CHANGED_AS_STRING,t=>e(this,void 0,void 0,function*(){this._permission=t}))):R.warn(`PushSubscriptionNamespace: skipping initialization. One or more required params are falsy: initialize: ${t}, subscription: ${i}`)}get id(){return this._id}get token(){return this._token}get optedIn(){return!!this._optedIn&&"granted"===this._permission}optIn(){return e(this,void 0,void 0,function*(){eS("optIn"),yield e$(),this._optedIn=!0,"granted"===(yield OneSignal.context.permissionManager.getPermissionStatus())?yield this._enable(!0):yield OneSignal.Notifications.requestPermission()})}optOut(){return e(this,void 0,void 0,function*(){eS("optOut"),yield e$(),this._optedIn=!1,yield this._enable(!1)})}addEventListener(e,t){OneSignal.emitter.on(e,t)}removeEventListener(e,t){OneSignal.emitter.off(e,t)}_enable(t){return e(this,void 0,void 0,function*(){yield e$();let e=yield ed.getAppConfig(),i=yield ed.getSubscription();if(!e.appId)throw new e1($.MissingAppId);if(!e5.isValidBoolean(t))throw new n("enabled",A.Malformed);i.optedOut=!t,yield ed.setSubscription(i),eZ.onInternalSubscriptionSet(i.optedOut).catch(e=>{R.error(e)}),eZ.checkAndTriggerSubscriptionChanged().catch(e=>{R.error(e)})})}}class eX extends eJ{constructor(e,t,i){super(),this.PushSubscription=new eQ(!1),e&&(this._currentUser=e6.createOrGetInstance(),this.PushSubscription=new eQ(!0,t,i))}get onesignalId(){var e;return null===(e=this._currentUser)||void 0===e?void 0:e.onesignalId}get externalId(){var e;let t=OneSignal.coreDirector.getIdentityModel();return null===(e=null==t?void 0:t.data)||void 0===e?void 0:e.external_id}addAlias(e,t){var i;null===(i=this._currentUser)||void 0===i||i.addAlias(e,t)}addAliases(e){var t;null===(t=this._currentUser)||void 0===t||t.addAliases(e)}removeAlias(e){var t;null===(t=this._currentUser)||void 0===t||t.removeAlias(e)}removeAliases(e){var t;null===(t=this._currentUser)||void 0===t||t.removeAliases(e)}addEmail(e){var t;null===(t=this._currentUser)||void 0===t||t.addEmail(e)}removeEmail(e){var t;null===(t=this._currentUser)||void 0===t||t.removeEmail(e)}addSms(e){var t;null===(t=this._currentUser)||void 0===t||t.addSms(e)}removeSms(e){var t;null===(t=this._currentUser)||void 0===t||t.removeSms(e)}addTag(e,t){var i;null===(i=this._currentUser)||void 0===i||i.addTag(e,t)}addTags(e){var t;null===(t=this._currentUser)||void 0===t||t.addTags(e)}removeTag(e){var t;null===(t=this._currentUser)||void 0===t||t.removeTag(e)}removeTags(e){var t;null===(t=this._currentUser)||void 0===t||t.removeTags(e)}getTags(){var e;return(null===(e=this._currentUser)||void 0===e?void 0:e.getTags())||{}}addEventListener(e,t){eX.emitter.on(e,t)}removeEventListener(e,t){eX.emitter.off(e,t)}}eX.emitter=new G;class eZ{static onNotificationPermissionChange(){eZ.checkAndTriggerSubscriptionChanged()}static onInternalSubscriptionSet(t){return e(this,void 0,void 0,function*(){eH.put("subscription.optedOut",t)})}static checkAndTriggerSubscriptionChanged(){return e(this,void 0,void 0,function*(){var e;eh.logMethodCall("checkAndTriggerSubscriptionChanged");let t=OneSignal.context,i=yield OneSignal.context.subscriptionManager.isPushNotificationsEnabled(),n=yield OneSignal.context.subscriptionManager.isOptedIn(),o=yield ed.getAppState(),{lastKnownPushEnabled:s,lastKnownPushId:r,lastKnownPushToken:a,lastKnownOptedIn:c}=o,d=yield ez.getCurrentPushToken(),l=yield OneSignal.coreDirector.getPushSubscriptionModel(),u=null===(e=null==l?void 0:l.data)||void 0===e?void 0:e.id;if(!(null===s||i!==s||d!==a||u!==r))return;yield t.subscriptionManager.updateNotificationTypes(),o.lastKnownPushEnabled=i,o.lastKnownPushToken=d,o.lastKnownPushId=u,o.lastKnownOptedIn=n,yield ed.setAppState(o);let p={previous:{id:r,token:a,optedIn:null==c||c},current:{id:u,token:d,optedIn:n}};R.info("Push Subscription state changed: ",p),eZ.triggerSubscriptionChanged(p)})}static _onSubscriptionChanged(t){return e(this,void 0,void 0,function*(){var e,i,n;eZ.onSubscriptionChanged_showWelcomeNotification(null===(e=null==t?void 0:t.current)||void 0===e?void 0:e.optedIn,null===(i=null==t?void 0:t.current)||void 0===i?void 0:i.id),eZ.onSubscriptionChanged_sendCategorySlidedownTags(null===(n=null==t?void 0:t.current)||void 0===n?void 0:n.optedIn),eZ.onSubscriptionChanged_evaluateNotifyButtonDisplayPredicate(),eZ.onSubscriptionChanged_updateCustomLink()})}static onSubscriptionChanged_sendCategorySlidedownTags(t){return e(this,void 0,void 0,function*(){var e,i;if(!0!==t)return;let n=null===(i=null===(e=OneSignal.context.appConfig.userConfig.promptOptions)||void 0===e?void 0:e.slidedown)||void 0===i?void 0:i.prompts;ej.isCategorySlidedownConfigured(n)&&(yield OneSignal.context.tagManager.sendTags())})}static onSubscriptionChanged_showWelcomeNotification(t,i){return e(this,void 0,void 0,function*(){var e;if(OneSignal.__doNotShowWelcomeNotification)return void R.debug("Not showing welcome notification because user has previously subscribed.");let n=null===(e=OneSignal.config)||void 0===e?void 0:e.userConfig.welcomeNotification;if(void 0!==n&&!0===n.disable||!0!==t||!i)return;let{appId:o}=yield ed.getAppConfig(),s=void 0!==n&&void 0!==n.title&&null!==n.title?n.title:"",r=void 0!==n&&void 0!==n.message&&null!==n.message&&n.message.length>0?n.message:"Thanks for subscribing!",a=new URL(location.href).origin+"?_osp=do_not_open",c=n&&n.url&&n.url.length>0?n.url:a;s=eK.decodeHtmlEntities(s),r=eK.decodeHtmlEntities(r),R.debug("Sending welcome notification."),e8.sendNotification(o,[i],{en:s},{en:r},c,null,{__isOneSignalWelcomeNotification:!0},void 0),eu.trigger(OneSignal.EVENTS.WELCOME_NOTIFICATION_SENT,{title:s,message:r,url:c})})}static onSubscriptionChanged_evaluateNotifyButtonDisplayPredicate(){return e(this,void 0,void 0,function*(){if(!OneSignal.config.userConfig.notifyButton)return;let e=OneSignal.config.userConfig.notifyButton.displayPredicate;e&&"function"==typeof e&&OneSignal.notifyButton&&(!1!==(yield e())?(R.debug("Showing notify button because display predicate returned true."),OneSignal.notifyButton.launcher.show()):(R.debug("Hiding notify button because display predicate returned false."),OneSignal.notifyButton.launcher.hide()))})}static onSubscriptionChanged_updateCustomLink(){return e(this,void 0,void 0,function*(){OneSignal.config.userConfig.promptOptions&&new e0(OneSignal.config.userConfig.promptOptions.customlink).initialize()})}static triggerSubscriptionChanged(e){eu.trigger(OneSignal.EVENTS.SUBSCRIPTION_CHANGED,e)}static triggerUserChanged(e){eu.trigger(OneSignal.EVENTS.SUBSCRIPTION_CHANGED,e,eX.emitter)}static triggerNotificationClick(e){let t={notification:e.notification,result:e.result};return eu.trigger(OneSignal.EVENTS.NOTIFICATION_CLICKED,t)}static fireStoredNotificationClicks(){return e(this,void 0,void 0,function*(){yield e$();let t=OneSignal.config.pageUrl||OneSignal.config.userConfig.pageUrl||document.URL;function i(t){return e(this,void 0,void 0,function*(){let e=yield ed.getAppState();e.pendingNotificationClickEvents[t.result.url]=null,yield ed.setAppState(e);let i=t.timestamp;i&&(Date.now()-i)/1e3/60>5||eZ.triggerNotificationClick(t)})}let n=yield ed.getAppState();if("origin"===(yield ed.get("Options","notificationClickHandlerMatch")))for(let o of Object.keys(n.pendingNotificationClickEvents))new URL(o).origin===location.origin&&(yield i(n.pendingNotificationClickEvents[o]));else{let s=n.pendingNotificationClickEvents[t];if(s)yield i(s);else if(!s&&t.endsWith("/")){let r=t.substring(0,t.length-1);(s=n.pendingNotificationClickEvents[r])&&(yield i(s))}}})}static checkAndTriggerUserChanged(){return e(this,void 0,void 0,function*(){var e;eh.logMethodCall("checkAndTriggerUserChanged");let t=yield ed.getUserState(),{previousOneSignalId:i,previousExternalId:n}=t,o=yield OneSignal.coreDirector.getIdentityModel(),s=null==o?void 0:o.onesignalId,r=null===(e=null==o?void 0:o.data)||void 0===e?void 0:e.external_id;if(!(s!==i||r!==n))return;t.previousOneSignalId=s,t.previousExternalId=r,yield ed.setUserState(t);let a={current:{onesignalId:s,externalId:r}};R.info("User state changed: ",a),eZ.triggerUserChanged(a)})}}class te{constructor(e,t){this.context=e,this.config=t}getRegistration(){return e(this,void 0,void 0,function*(){return yield V.getRegistration(this.config.registrationOptions.scope)})}getActiveState(){return e(this,void 0,void 0,function*(){let e=yield this.context.serviceWorkerManager.getRegistration();if(!e)return m.None;let t=te.activeSwFileName(e);return this.swActiveStateByFileName(t)})}static activeSwFileName(e){let t=V.getAvailableServiceWorker(e);if(!t)return null;let i=new URL(t.scriptURL).pathname,n=new q(i).getFileName();if("akam-sw.js"==n){let o=new URLSearchParams(new URL(t.scriptURL).search).get("othersw");if(o)return R.debug("Found a ServiceWorker under Akamai's akam-sw.js?othersw=",o),new q(new URL(o).pathname).getFileName()}return n}swActiveStateByFileName(e){return e?e==this.config.workerPath.getFileName()||"OneSignalSDK.sw.js"==e?m.OneSignalWorker:m.ThirdParty:m.None}getWorkerVersion(){return e(this,void 0,void 0,function*(){return new Promise(t=>e(this,void 0,void 0,function*(){this.context.workerMessenger.once(E.WorkerVersion,e=>{t(e)}),yield this.context.workerMessenger.unicast(E.WorkerVersion)}))})}shouldInstallWorker(){return e(this,void 0,void 0,function*(){if(!_.supportsServiceWorkers()||!OneSignal.config)return!1;let e=yield this.getActiveState();if(R.debug("[shouldInstallWorker] workerState",e),e===m.None||e===m.ThirdParty){let t="granted"===(yield OneSignal.context.permissionManager.getNotificationPermission(OneSignal.config.safariWebId));return t&&R.info("[shouldInstallWorker] Notification Permissions enabled, will install ServiceWorker"),t}return!!(yield this.haveParamsChanged())||this.workerNeedsUpdate()})}haveParamsChanged(){return e(this,void 0,void 0,function*(){let e=yield this.context.serviceWorkerManager.getRegistration();if(!e)return R.info("[changedServiceWorkerParams] workerRegistration not found at scope",this.config.registrationOptions.scope),!0;let t=new URL(e.scope).pathname,i=this.config.registrationOptions.scope;if(t!=i)return R.info("[changedServiceWorkerParams] ServiceWorker scope changing",{a_old:t,b_new:i}),!0;let n=V.getAvailableServiceWorker(e),o=eR.getServiceWorkerHref(this.config,this.context.appConfig.appId,_.version());return!(null==n?void 0:n.scriptURL)||o!==n.scriptURL&&(R.info("[changedServiceWorkerParams] ServiceWorker href changing:",{a_old:null==n?void 0:n.scriptURL,b_new:o}),!0)})}workerNeedsUpdate(){return e(this,void 0,void 0,function*(){let e;R.info("[Service Worker Update] Checking service worker version...");try{e=yield J.timeoutPromise(this.getWorkerVersion(),2e3)}catch(t){return R.info("[Service Worker Update] Worker did not reply to version query; assuming older version and updating."),!0}return e!==_.version()?(R.info(`[Service Worker Update] Updating service worker from ${e} --> ${_.version()}.`),!0):(R.info(`[Service Worker Update] Service worker version is current at ${e} (no update required).`),!1)})}establishServiceWorkerChannel(){return e(this,void 0,void 0,function*(){R.debug("establishServiceWorkerChannel");let t=this.context.workerMessenger;t.off(),t.on(E.NotificationWillDisplay,t=>e(this,void 0,void 0,function*(){R.debug(location.origin,"Received notification display event from service worker.");yield eu.trigger(OneSignal.EVENTS.NOTIFICATION_WILL_DISPLAY,{notification:t.notification,preventDefault:function(){throw Error("Browser does not support preventing display.")}})})),t.on(E.NotificationClicked,t=>e(this,void 0,void 0,function*(){if(0===OneSignal.emitter.numberOfListeners(OneSignal.EVENTS.NOTIFICATION_CLICKED)){R.debug("notification.clicked event received, but no event listeners; storing event in IndexedDb for later retrieval.");let e=t.result.url;e||(e=location.href),yield ed.putNotificationClickedEventPendingUrlOpening(t)}else yield eZ.triggerNotificationClick(t)})),t.on(E.NotificationDismissed,t=>e(this,void 0,void 0,function*(){yield eu.trigger(OneSignal.EVENTS.NOTIFICATION_DISMISSED,t)}));let i=eh.isSafari();t.on(E.AreYouVisible,n=>e(this,void 0,void 0,function*(){i&&(yield t.directPostMessageToSW(E.AreYouVisibleResponse,{timestamp:n.timestamp,focused:document.hasFocus()}))}))})}installWorker(){return e(this,void 0,void 0,function*(){if(!(yield this.shouldInstallWorker()))return this.getRegistration();R.info("Installing worker..."),(yield this.getActiveState())===m.ThirdParty&&R.info("[Service Worker Installation] 3rd party service worker detected.");let e=eR.getServiceWorkerHref(this.config,this.context.appConfig.appId,_.version()),t=`${eh.getBaseUrl()}${this.config.registrationOptions.scope}`,i;R.info(`[Service Worker Installation] Installing service worker ${e} ${t}.`);try{i=yield navigator.serviceWorker.register(e,{scope:t})}catch(n){R.error(`[Service Worker Installation] Installing service worker failed ${n}`),i=yield this.fallbackToUserModelBetaWorker()}return R.debug("[Service Worker Installation] Service worker installed. Waiting for activation"),yield V.waitUntilActive(i),R.debug("[Service Worker Installation] Service worker active"),yield this.establishServiceWorkerChannel(),i})}fallbackToUserModelBetaWorker(){return e(this,void 0,void 0,function*(){let e="OneSignalSDK.sw.js",t={workerPath:new q(`/${e}`),registrationOptions:this.config.registrationOptions},i=eR.getServiceWorkerHref(t,this.context.appConfig.appId,_.version()),n=`${eh.getBaseUrl()}${this.config.registrationOptions.scope}`;R.info(`[Service Worker Installation] Attempting to install v16 Beta Worker ${i} ${n}.`);try{let o=yield navigator.serviceWorker.register(i,{scope:n}),s=`
        [Service Worker Installation] Successfully installed v16 Beta Worker.
        Deprecation warning: support for the v16 beta worker name of ${e}
        will be removed May 5 2024. We have decided to keep the v15 name.
        To avoid breaking changes for your users, please host both worker files:
        OneSignalSDK.sw.js & OneSignalSDKWorker.js.
      `;return R.error(s),o}catch(r){let a=yield fetch(i);if(403===a.status||404===a.status)throw new eg(a.status,a.statusText);throw r}})}}!function(e){e.Default="default",e.Granted="granted",e.Denied="denied"}(w||(w={}));class tt extends t{constructor(){super("This code is not implemented yet."),Object.setPrototypeOf(this,tt.prototype)}}!function(e){e[e.Blocked=0]="Blocked",e[e.Dismissed=1]="Dismissed",e[e.Default=2]="Default"}(I||(I={}));class ti extends t{constructor(e){let t;switch(e){case I.Dismissed:t="The user dismissed the permission prompt.";break;case I.Blocked:t="Notification permissions are blocked.";break;case I.Default:t="Notification permissions have not been granted yet."}super(t),this.reason=e,Object.setPrototypeOf(this,ti.prototype)}}!function(e){e[e.InvalidSafariSetup=0]="InvalidSafariSetup",e[e.Blocked=1]="Blocked",e[e.Dismissed=2]="Dismissed"}(k||(k={}));class tn extends t{constructor(e){let t;switch(e){case k.InvalidSafariSetup:t="The Safari site URL, icon size, or push certificate is invalid, or Safari is in a private session.";break;case k.Blocked:t="Notification permissions are blocked.";break;case k.Dismissed:t="The notification permission prompt was dismissed."}super(t),Object.setPrototypeOf(this,tn.prototype)}}class to{static setFromW3cSubscription(e){let t=new to;if(e&&(t.w3cEndpoint=new URL(e.endpoint),e.getKey)){let i=null;try{i=e.getKey("p256dh")}catch(n){}let o=null;try{o=e.getKey("auth")}catch(s){}if(i){let r=btoa(String.fromCharCode.apply(null,new Uint8Array(i)));t.w3cP256dh=r}if(o){let a=btoa(String.fromCharCode.apply(null,new Uint8Array(o)));t.w3cAuth=a}}return t}setFromSafariSubscription(e){e&&(this.safariDeviceToken=e)}serialize(){return{w3cEndpoint:this.w3cEndpoint?this.w3cEndpoint.toString():null,w3cP256dh:this.w3cP256dh,w3cAuth:this.w3cAuth,safariDeviceToken:this.safariDeviceToken}}static deserialize(e){let t=new to;if(!e)return t;try{t.w3cEndpoint=new URL(e.w3cEndpoint)}catch(i){}return t.w3cP256dh=e.w3cP256dh,t.w3cAuth=e.w3cAuth,t.safariDeviceToken=e.safariDeviceToken,t}}class ts{constructor(e,t){this.safariPermissionPromptFailed=!1,this.context=e,this.config=t}isPushNotificationsEnabled(){return e(this,void 0,void 0,function*(){let e=yield this.getSubscriptionState();return e.subscribed&&!e.optedOut})}isOptedIn(){return e(this,void 0,void 0,function*(){let e=yield this.getSubscriptionState();return"granted"===(yield OneSignal.context.permissionManager.getPermissionStatus())&&!e.optedOut})}isOptedOut(t){return e(this,void 0,void 0,function*(){eS("isOptedOut",t);let{optedOut:e}=yield ed.getSubscription();return function(e,...t){e&&e.apply(null,t)}(t,e),e})}subscribe(t){return e(this,void 0,void 0,function*(){let e;switch(s.getWindowEnv()){case T.ServiceWorker:e=yield this.subscribeFcmFromWorker(t);break;case T.Host:if((yield OneSignal.context.permissionManager.getPermissionStatus())===w.Denied)throw new ti(I.Blocked);if(_.useSafariLegacyPush()){yield this._updatePushSubscriptionModelWithRawSubscription(e=yield this.subscribeSafari()),R.info("Installing SW on Safari");try{yield this.context.serviceWorkerManager.installWorker(),R.info("SW on Safari successfully installed")}catch(i){R.error("SW on Safari failed to install.")}}else yield this._updatePushSubscriptionModelWithRawSubscription(e=yield this.subscribeFcmFromPage(t));break;default:throw new e1($.UnsupportedEnvironment)}return e})}_updatePushSubscriptionModelWithRawSubscription(t){return e(this,void 0,void 0,function*(){let e=yield OneSignal.coreDirector.getPushSubscriptionModel();if(!e)return OneSignal.coreDirector.generatePushSubscriptionModel(t),void(yield eY.createAndHydrateUser());{let i=new ex(t).serialize(),n=Object.keys(i);for(let o=0;o<n.length;o++){let s=n[o];i[s]&&e.set(s,i[s])}}})}updateNotificationTypes(){return e(this,void 0,void 0,function*(){yield this.updatePushSubscriptionNotificationTypes((yield this.getNotificationTypes()))})}getNotificationTypes(){return e(this,void 0,void 0,function*(){let{optedOut:e}=yield ed.getSubscription();return e?g.UserOptedOut:"granted"===(yield OneSignal.context.permissionManager.getPermissionStatus())?g.Subscribed:g.NoNativePermission})}updatePushSubscriptionNotificationTypes(t){return e(this,void 0,void 0,function*(){let e=yield OneSignal.coreDirector.getPushSubscriptionModel();e?(e.set("notification_types",t),e.set("enabled",t===g.Subscribed)):R.info("No Push Subscription yet to update notification_types.")})}registerSubscription(t,i){return e(this,void 0,void 0,function*(){t&&(t=to.deserialize(t)),(yield this.isAlreadyRegisteredWithOneSignal())?yield this.context.updateManager.sendPushDeviceRecordUpdate():this.context.sessionManager.upsertSession(c.UserCreate);let e=yield ed.getSubscription();return e.deviceId="99999999-9999-9999-9999-999999999999",e.optedOut=!1,t?_.useSafariLegacyPush()?e.subscriptionToken=t.safariDeviceToken:e.subscriptionToken=t.w3cEndpoint?t.w3cEndpoint.toString():null:e.subscriptionToken=null,yield ed.setSubscription(e),s.getWindowEnv()!==T.ServiceWorker&&eu.trigger(OneSignal.EVENTS.REGISTERED),"undefined"!=typeof OneSignal&&(OneSignal._sessionInitAlreadyRunning=!1),e})}static requestPresubscribeNotificationPermission(){return e(this,void 0,void 0,function*(){return yield ts.requestNotificationPermission()})}unsubscribe(t){return e(this,void 0,void 0,function*(){if(0===t||1!==t||s.getWindowEnv()!==T.ServiceWorker)throw new tt;yield ed.put("Options",{key:"optedOut",value:!0})})}static requestNotificationPermission(){return e(this,void 0,void 0,function*(){return w[yield window.Notification.requestPermission()]})}isAlreadyRegisteredWithOneSignal(){return e(this,void 0,void 0,function*(){let{deviceId:e}=yield ed.getSubscription();return!!e})}subscribeSafariPromptPermission(){return e(this,void 0,void 0,function*(){var e;return e=this.safariPermissionPromptFailed?`${s.getOneSignalApiUrl().toString()}/safari`:`${s.getOneSignalApiUrl().toString()}/safari/apps/${this.config.appId}`,new Promise(t=>{window.safari.pushNotification.requestPermission(e,this.config.safariWebId,{app_id:this.config.appId},e=>{e&&e.deviceToken?t(e.deviceToken.toLowerCase()):t(null)})})})}subscribeSafari(){return e(this,void 0,void 0,function*(){let e=new to;if(!this.config.safariWebId)throw new eU(v.MissingSafariWebId);let{deviceToken:t}=window.safari.pushNotification.permission(this.config.safariWebId);if(t)return e.setFromSafariSubscription(t.toLowerCase()),e;eu.trigger(OneSignal.EVENTS.PERMISSION_PROMPT_DISPLAYED);let i=yield this.subscribeSafariPromptPermission();if(ey.triggerNotificationPermissionChanged(),!i)throw this.safariPermissionPromptFailed=!0,new tn(k.InvalidSafariSetup);return e.setFromSafariSubscription(i),e})}subscribeFcmFromPage(t){return e(this,void 0,void 0,function*(){if(s.getWindowEnv()===T.Host&&Notification.permission===w.Default){yield eu.trigger(OneSignal.EVENTS.PERMISSION_PROMPT_DISPLAYED);let e=yield ts.requestPresubscribeNotificationPermission(),i=e===w.Default;switch(yield ey.triggerNotificationPermissionChanged(i),e){case w.Default:throw R.debug("Exiting subscription and not registering worker because the permission was dismissed."),OneSignal._sessionInitAlreadyRunning=!1,new ti(I.Dismissed);case w.Denied:throw R.debug("Exiting subscription and not registering worker because the permission was blocked."),OneSignal._sessionInitAlreadyRunning=!1,new ti(I.Blocked)}}let n;try{n=yield this.context.serviceWorkerManager.installWorker()}catch(o){throw o instanceof eg&&(403===o.status?yield this.context.subscriptionManager.registerFailedSubscription(g.ServiceWorkerStatus403,this.context):404===o.status&&(yield this.context.subscriptionManager.registerFailedSubscription(g.ServiceWorkerStatus404,this.context))),o}if(!n)throw Error("OneSignal service worker not found!");return R.debug("[Subscription Manager] Service worker is ready to continue subscribing."),yield this.subscribeWithVapidKey(n.pushManager,t)})}subscribeFcmFromWorker(t){return e(this,void 0,void 0,function*(){let e=self.registration;if(!e.active&&"firefox"!==U().name)throw new e1($.ServiceWorkerNotActivated);let i=yield e.pushManager.permissionState({userVisibleOnly:!0});if("denied"===i)throw new ti(I.Blocked);if("prompt"===i)throw new ti(I.Default);return yield this.subscribeWithVapidKey(e.pushManager,t)})}getVapidKeyForBrowser(){let e;return(e="firefox"===U().name?this.config.onesignalVapidPublicKey:this.config.vapidPublicKey)?function(e){let t=atob((e+"=".repeat((4-e.length%4)%4)).replace(/-/g,"+").replace(/_/g,"/")),i=new Uint8Array(t.length);for(let n=0;n<t.length;++n)i[n]=t.charCodeAt(n);return i}(e).buffer:void 0}subscribeWithVapidKey(t,i){return e(this,void 0,void 0,function*(){let e=yield t.getSubscription();switch(i){case 0:if(!e)break;e.options?R.debug("[Subscription Manager] An existing push subscription exists and it's options is not null."):(R.debug("[Subscription Manager] An existing push subscription exists and options is null. Unsubscribing from push first now."),yield ts.doPushUnsubscribe(e));break;case 1:e&&(yield ts.doPushUnsubscribe(e))}let[n,o]=yield ts.doPushSubscribe(t,this.getVapidKeyForBrowser());return yield ts.updateSubscriptionTime(o,n.expirationTime),to.setFromW3cSubscription(n)})}static updateSubscriptionTime(t,i){return e(this,void 0,void 0,function*(){let e=yield ed.getSubscription();t&&(e.createdAt=(new Date).getTime()),e.expirationTime=i,yield ed.setSubscription(e)})}static doPushUnsubscribe(t){return e(this,void 0,void 0,function*(){R.debug("[Subscription Manager] Unsubscribing existing push subscription.");let e=yield t.unsubscribe();return R.debug(`[Subscription Manager] Unsubscribing existing push subscription result: ${e}`),e})}static doPushSubscribe(t,i){return e(this,void 0,void 0,function*(){if(!i)throw Error("Missing required 'applicationServerKey' to subscribe for push notifications!");let e={userVisibleOnly:!0,applicationServerKey:i};R.debug("[Subscription Manager] Subscribing to web push with these options:",e);try{let n=yield t.getSubscription();return[(yield t.subscribe(e)),!n]}catch(o){if("InvalidStateError"==o.name){R.warn("[Subscription Manager] Couldn't re-subscribe due to applicationServerKey changing, unsubscribe and attempting to subscribe with new key.",o);let s=yield t.getSubscription();return s&&(yield ts.doPushUnsubscribe(s)),[(yield t.subscribe(e)),!0]}throw o}})}isSubscriptionExpiring(){return e(this,void 0,void 0,function*(){if((yield this.context.serviceWorkerManager.getActiveState())!==m.OneSignalWorker)return!1;let e=yield this.context.serviceWorkerManager.getRegistration();if(!e||!e.pushManager)return!1;let t=yield e.pushManager.getSubscription();if(!t||!t.expirationTime)return!1;let{createdAt:i}=yield ed.getSubscription();i||(i=(new Date).getTime()+31536e6);let n=i+(t.expirationTime-i)/2;return!!t.expirationTime&&((new Date).getTime()>=t.expirationTime||(new Date).getTime()>=n)})}getSubscriptionState(){return e(this,void 0,void 0,function*(){if(s.getWindowEnv()===T.ServiceWorker){let e=yield self.registration.pushManager.getSubscription(),{optedOut:t}=yield ed.getSubscription();return{subscribed:!!e,optedOut:!!t}}return this.getSubscriptionStateFromBrowserContext()})}getSubscriptionStateFromBrowserContext(){return e(this,void 0,void 0,function*(){var e,t;let{optedOut:i,subscriptionToken:n}=yield ed.getSubscription(),o=yield OneSignal.coreDirector.getPushSubscriptionModel(),s=e4(null==o?void 0:o.data)&&!!(null==o?void 0:o.onesignalId);if(_.useSafariLegacyPush()){let r=null===(t=null===(e=window.safari)||void 0===e?void 0:e.pushNotification)||void 0===t?void 0:t.permission(this.config.safariWebId);return{subscribed:!!(s&&n&&"granted"===(null==r?void 0:r.permission)&&(null==r?void 0:r.deviceToken)),optedOut:!!i}}let a=yield this.context.serviceWorkerManager.getActiveState(),c=yield this.context.serviceWorkerManager.getRegistration(),d=yield this.context.permissionManager.getNotificationPermission(this.context.appConfig.safariWebId),l=a===m.OneSignalWorker;return c?{subscribed:!!(s&&n&&d===w.Granted&&l),optedOut:!!i}:{subscribed:!1,optedOut:!!i}})}registerFailedSubscription(t,i){return e(this,void 0,void 0,function*(){i.pageViewManager.isFirstPageView()&&(i.subscriptionManager.registerSubscription(new to,t),i.pageViewManager.incrementPageViewCount())})}}let tr=class{static getServiceWorkerManager(e){let t=e.appConfig,i={workerPath:new q("OneSignalSDKWorker.js"),registrationOptions:{scope:"/"}};return t.userConfig&&(t.userConfig.path&&(i.workerPath=new q(`${t.userConfig.path}${t.userConfig.serviceWorkerPath}`)),t.userConfig.serviceWorkerParam&&(i.registrationOptions=t.userConfig.serviceWorkerParam)),new te(e,i)}static getSubscriptionManager(e){let t=e.appConfig,i={safariWebId:t.safariWebId,appId:t.appId,vapidPublicKey:t.vapidPublicKey,onesignalVapidPublicKey:t.onesignalVapidPublicKey};return new ts(e,i)}};class ta{constructor(e){this.context=e,this.onSessionSent=e.pageViewManager.getPageViewCount()>1}sendPushDeviceRecordUpdate(){return e(this,void 0,void 0,function*(){var e;(null===(e=e6.singletonInstance)||void 0===e?void 0:e.hasOneSignalId)?this.onSessionSent||(yield this.sendOnSessionUpdate()):R.debug("Not sending the update because user is not registered with OneSignal (no onesignal_id)")})}sendOnSessionUpdate(){return e(this,void 0,void 0,function*(){var e;if(this.onSessionSent||!this.context.pageViewManager.isFirstPageView())return;if(!(yield this.context.subscriptionManager.isAlreadyRegisteredWithOneSignal()))return void R.debug("Not sending the on session because user is not registered with OneSignal (no device id)");let t=yield OneSignal.coreDirector.getPushSubscriptionModel();if((null==t?void 0:t.data.notification_types)===g.Subscribed||!0===(null===(e=OneSignal.config)||void 0===e?void 0:e.enableOnSession))try{this.context.sessionManager.upsertSession(c.UserNewSession),this.onSessionSent=!0}catch(i){R.error(`Failed to update user session. Error "${i.message}" ${i.stack}`)}})}sendOutcomeDirect(t,i,n,o){return e(this,void 0,void 0,function*(){eS("sendOutcomeDirect");let e=yield OneSignal.coreDirector.getPushSubscriptionModel();if(e&&e4(null==e?void 0:e.data)){let s={id:n,app_id:t,notification_ids:i,direct:!0,subscription:{id:e.data.id,type:ex.getSubscriptionType()}};return void 0!==o&&(s.weight=o),void(yield e8.sendOutcome(s))}R.warn("Send outcome aborted because pushSubscriptionModel is not available.")})}sendOutcomeInfluenced(t,i,n,o){return e(this,void 0,void 0,function*(){eS("sendOutcomeInfluenced");let e=yield OneSignal.coreDirector.getPushSubscriptionModel();if(e&&e4(null==e?void 0:e.data)){let s={id:n,app_id:t,notification_ids:i,direct:!1,subscription:{id:e.data.id,type:ex.getSubscriptionType()}};return void 0!==o&&(s.weight=o),void(yield e8.sendOutcome(s))}R.warn("Send outcome aborted because pushSubscriptionModel is not available.")})}sendOutcomeUnattributed(t,i,n){return e(this,void 0,void 0,function*(){eS("sendOutcomeUnattributed");let e=yield OneSignal.coreDirector.getPushSubscriptionModel();if(e&&e4(null==e?void 0:e.data)){let o={id:i,app_id:t,subscription:{id:e.data.id,type:ex.getSubscriptionType()}};return void 0!==n&&(o.weight=n),void(yield e8.sendOutcome(o))}R.warn("Send outcome aborted because pushSubscriptionModel is not available.")})}}class tc{upsertSession(t){return e(this,void 0,void 0,function*(){})}setupSessionEventListeners(){return e(this,void 0,void 0,function*(){})}}class td{constructor(e){this.appConfig=e,this.subscriptionManager=tr.getSubscriptionManager(this),this.serviceWorkerManager=tr.getServiceWorkerManager(this),this.pageViewManager=new j,this.sessionManager=new tc,this.permissionManager=new z,this.workerMessenger=new L(this),this.updateManager=new ta(this)}}let tl={pageViews:1,timeDelay:0},tu="We'd like to show you notifications for the latest news and updates.",tp="Allow",tg="Cancel",tf={updateMessage:"Update your push notification subscription preferences.",positiveUpdateButton:"Save Preferences",negativeUpdateButton:"Cancel"},th={type:b.Push,text:{actionMessage:tu,acceptButton:tp,cancelButton:tg},autoPrompt:!1,delay:tl};class tv{static convertTagsApiToBooleans(e){let t={};return Object.keys(e).forEach(i=>{t[i]="1"===e[i]}),t}static convertTagsBooleansToApi(e){let t={};return Object.keys(e).forEach(i=>{t[i]=!0===e[i]?"1":"0"}),t}static getObjectDifference(e,t){let i={};return Object.keys(e).forEach(n=>{t[n]!==e[n]&&(i[n]=e[n])}),i}static markAllTagsAsSpecified(e,t){e.forEach(e=>{e.checked=t})}static isTagObjectEmpty(e){return 0===Object.keys(e).length}static getCheckedTagCategories(e,t){if(!t)return e;if(tv.isTagObjectEmpty(t)){let i=ek(e);return tv.markAllTagsAsSpecified(i,!0),i}return ek(e).map(e=>{let i=t[e.tag];return e.checked=tv.getCheckedStatusForTagValue(i),e})}static getCheckedStatusForTagValue(e){return void 0===e||e}static limitCategoriesToMaxCount(e,t){return ek(e),e.slice(0,t)}}class tm{static upgradeConfigToVersionTwo(e){var t,i,n;tm.isPromptOptionsVersion0(e.promptOptions)&&(e.promptOptions=tm.convertConfigToVersionOne(e.promptOptions)),tm.isSlidedownConfigVersion1(null===(t=e.promptOptions)||void 0===t?void 0:t.slidedown)&&(null===(i=e.promptOptions)||void 0===i?void 0:i.slidedown)&&(e.promptOptions.slidedown=tm.convertConfigToVersionTwo(null===(n=e.promptOptions)||void 0===n?void 0:n.slidedown))}static convertConfigToVersionOne(e){e.slidedown||(e.slidedown={});let{acceptButtonText:t,cancelButtonText:i,actionMessage:n}=e.slidedown,o=e.acceptButtonText||e.acceptButton,s=e.cancelButtonText||e.cancelButton;return e.slidedown.acceptButtonText=t||o,e.slidedown.cancelButtonText=i||s,e.slidedown.actionMessage=n||e.actionMessage,e}static convertConfigToVersionTwo(e){var t,i,n,o;let s=ej.isCategorySlidedownConfiguredVersion1(e)?b.Category:b.Push,r,a;return s===b.Category&&(r=null===(t=e.categories)||void 0===t?void 0:t.positiveUpdateButton,a=null===(i=e.categories)||void 0===i?void 0:i.negativeUpdateButton),{prompts:[...e.prompts||[],{type:s,autoPrompt:e.autoPrompt,text:{actionMessage:e.actionMessage,acceptButton:e.acceptButton||e.acceptButtonText,cancelButton:e.cancelButton||e.cancelButtonText,positiveUpdateButton:r,negativeUpdateButton:a,updateMessage:null===(n=null==e?void 0:e.categories)||void 0===n?void 0:n.updateMessage},delay:{pageViews:e.pageViews,timeDelay:e.timeDelay},categories:null===(o=null==e?void 0:e.categories)||void 0===o?void 0:o.tags}]}}static isPromptOptionsVersion0(e){if(e){let t=["acceptButtonText","cancelButtonText","actionMessage"];for(let i=0;i<t.length;i++)if(e.hasOwnProperty(t[i]))return!0}return!1}static isSlidedownConfigVersion1(e){if(e){let t=["enabled","autoPrompt","pageViews","timeDelay","acceptButton","acceptButtonText","cancelButton","cancelButtonText","actionMessage","customizeTextEnabled","categories"];for(let i=0;i<t.length;i++)if(e.hasOwnProperty(t[i]))return!0}return!1}}(function(e){e.TypicalSite="typical",e.WordPress="wordpress",e.Shopify="shopify",e.Blogger="blogger",e.Magento="magento",e.Drupal="drupal",e.SquareSpace="squarespace",e.Joomla="joomla",e.Weebly="weebly",e.Wix="wix",e.Custom="custom"})(O||(O={})),function(e){e.Exact="exact",e.Origin="origin"}(P||(P={})),function(e){e.Navigate="navigate",e.Focus="focus"}(x||(x={})),function(e){e[e.Dashboard=0]="Dashboard",e[e.JavaScript=1]="JavaScript"}(C||(C={}));class tb{static getAppConfig(t,i){return e(this,void 0,void 0,function*(){try{if(!t||!t.appId||!eh.isValidUuid(t.appId))throw new eU(v.InvalidAppId);let e=yield i(t.appId);tm.upgradeConfigToVersionTwo(t);let n=this.getMergedConfig(t,e);return this.checkUnsupportedSubdomain(n),this.checkRestrictedOrigin(n),n}catch(o){if(o){if(1===o.code)throw new eU(v.InvalidAppId);if(2===o.code)throw new eU(v.AppNotConfiguredForWebPush)}throw o}})}static checkUnsupportedSubdomain(e){let t=!window.isSecureContext;if(e.hasUnsupportedSubdomain||t)throw t?Error("OneSignalSDK: HTTP sites are no longer supported starting with version 16 (User Model), your public site must start with https://. Please visit the OneSignal dashboard's Settings > Web Configuration to find this option."):Error('OneSignalSDK: The "My site is not fully HTTPS" option is no longer supported starting with version 16 (User Model) of the OneSignal SDK. Please visit the OneSignal dashboard\'s Settings > Web Configuration to find this option.')}static checkRestrictedOrigin(e){if(e.restrictedOriginEnabled&&s.getWindowEnv()===T.Host&&!this.doesCurrentOriginMatchConfigOrigin(e.origin))throw new eU(v.WrongSiteUrl,{siteUrl:e.origin})}static doesCurrentOriginMatchConfigOrigin(e){try{return location.origin===new URL(e).origin}catch(t){return!1}}static getIntegrationCapabilities(e){switch(e){case O.Custom:case O.WordPress:return{configuration:C.JavaScript};default:return{configuration:C.Dashboard}}}static getMergedConfig(e,t){let i=this.getConfigIntegrationKind(t),n=this.hasUnsupportedSubdomainForConfigIntegrationKind(i,e,t),o=this.getUserConfigForConfigIntegrationKind(i,e,t);return{appId:t.app_id,hasUnsupportedSubdomain:n,siteName:t.config.siteInfo.name,origin:t.config.origin,restrictedOriginEnabled:t.features.restrict_origin&&t.features.restrict_origin.enable,safariWebId:t.config.safari_web_id,vapidPublicKey:t.config.vapid_public_key,onesignalVapidPublicKey:t.config.onesignal_vapid_public_key,userConfig:o,enableOnSession:Q.valueOrDefault(t.features.enable_on_session,!1),sessionThreshold:Q.valueOrDefault(t.features.session_threshold,30),enableSessionDuration:Q.valueOrDefault(t.features.web_on_focus_enabled,!0)}}static getConfigIntegrationKind(e){return e.config.integration?e.config.integration.kind:O.Custom}static getCustomLinkConfig(e){let t={enabled:!1,style:"button",size:"medium",unsubscribeEnabled:!1,text:{explanation:"",subscribe:"",unsubscribe:""},color:{button:"",text:""}};if(!(e&&e.config&&e.config.staticPrompts&&e.config.staticPrompts.customlink&&e.config.staticPrompts.customlink.enabled))return t;let i=e.config.staticPrompts.customlink;return{enabled:i.enabled,style:i.style,size:i.size,unsubscribeEnabled:i.unsubscribeEnabled,text:i.text?{subscribe:i.text.subscribe,unsubscribe:i.text.unsubscribe,explanation:i.text.explanation}:t.text,color:i.color?{button:i.color.button,text:i.color.text}:t.color}}static injectDefaultsIntoPromptOptions(e,t,i){var n,o;let s={enabled:!1};e&&e.customlink&&(s=e.customlink);let r=t.customlink,a=Object.assign(Object.assign({},e),{customlink:{enabled:Q.getValueOrDefault(s.enabled,r.enabled),style:Q.getValueOrDefault(s.style,r.style),size:Q.getValueOrDefault(s.size,r.size),unsubscribeEnabled:Q.getValueOrDefault(s.unsubscribeEnabled,r.unsubscribeEnabled),text:{subscribe:Q.getValueOrDefault(s.text?s.text.subscribe:void 0,r.text.subscribe),unsubscribe:Q.getValueOrDefault(s.text?s.text.unsubscribe:void 0,r.text.unsubscribe),explanation:Q.getValueOrDefault(s.text?s.text.explanation:void 0,r.text.explanation)},color:{button:Q.getValueOrDefault(s.color?s.color.button:void 0,r.color.button),text:Q.getValueOrDefault(s.color?s.color.text:void 0,r.color.text)}}});return a.slidedown?a.slidedown.prompts=null===(o=null===(n=a.slidedown)||void 0===n?void 0:n.prompts)||void 0===o?void 0:o.map(e=>{var t,i,n,o,s,r,a,c,d;if(e.type=Q.getValueOrDefault(e.type,b.Push),e.type===b.Category&&(e.text=Object.assign(Object.assign({},e.text),{positiveUpdateButton:Q.getValueOrDefault(null===(t=e.text)||void 0===t?void 0:t.positiveUpdateButton,tf.positiveUpdateButton),negativeUpdateButton:Q.getValueOrDefault(null===(i=e.text)||void 0===i?void 0:i.negativeUpdateButton,tf.negativeUpdateButton),updateMessage:Q.getValueOrDefault(null===(n=e.text)||void 0===n?void 0:n.updateMessage,tf.updateMessage)})),e.text=Object.assign(Object.assign({},e.text),{actionMessage:Q.getValueOrDefault(null===(o=e.text)||void 0===o?void 0:o.actionMessage,tu),acceptButton:Q.getValueOrDefault(null===(s=e.text)||void 0===s?void 0:s.acceptButton,tp),cancelButton:Q.getValueOrDefault(null===(r=e.text)||void 0===r?void 0:r.cancelButton,tg),confirmMessage:Q.getValueOrDefault(null===(a=e.text)||void 0===a?void 0:a.confirmMessage,"Thank You!")}),e.autoPrompt=Q.getValueOrDefault(e.autoPrompt,!0),e.delay={pageViews:Q.getValueOrDefault(null===(c=e.delay)||void 0===c?void 0:c.pageViews,tl.pageViews),timeDelay:Q.getValueOrDefault(null===(d=e.delay)||void 0===d?void 0:d.timeDelay,tl.timeDelay)},e.categories){let{categories:l}=e;e.categories=tv.limitCategoriesToMaxCount(l,10)}return e}):(a.slidedown={prompts:[]},a.slidedown.prompts=[th]),a.native?(a.native.enabled=!!a.native.enabled,a.native.autoPrompt=a.native.hasOwnProperty("autoPrompt")?!!a.native.enabled&&!!a.native.autoPrompt:!!a.native.enabled,a.native.pageViews=Q.getValueOrDefault(a.native.pageViews,tl.pageViews),a.native.timeDelay=Q.getValueOrDefault(a.native.timeDelay,tl.timeDelay)):a.native={enabled:!1,autoPrompt:!1,pageViews:tl.pageViews,timeDelay:tl.timeDelay},!0===i.autoRegister&&(a.native.enabled=!0,a.native.autoPrompt=!0),a.autoPrompt=a.native.autoPrompt||ej.isSlidedownAutoPromptConfigured(a.slidedown.prompts),a}static getPromptOptionsForDashboardConfiguration(e){let t=e.config.staticPrompts,i=t.native?{enabled:t.native.enabled,autoPrompt:t.native.enabled&&!1!==t.native.autoPrompt,pageViews:Q.getValueOrDefault(t.native.pageViews,tl.pageViews),timeDelay:Q.getValueOrDefault(t.native.timeDelay,tl.timeDelay)}:{enabled:!1,autoPrompt:!1,pageViews:tl.pageViews,timeDelay:tl.timeDelay},{prompts:n}=t.slidedown;return{autoPrompt:i.autoPrompt||ej.isSlidedownAutoPromptConfigured(n),native:i,slidedown:{prompts:n},fullscreen:{enabled:t.fullscreen.enabled,actionMessage:t.fullscreen.actionMessage,acceptButton:t.fullscreen.acceptButton,cancelButton:t.fullscreen.cancelButton,title:t.fullscreen.title,message:t.fullscreen.message,caption:t.fullscreen.caption,autoAcceptTitle:t.fullscreen.autoAcceptTitle},customlink:this.getCustomLinkConfig(e)}}static getUserConfigForConfigIntegrationKind(e,t,i){switch(this.getIntegrationCapabilities(e).configuration){case C.Dashboard:return{appId:i.app_id,autoRegister:!1,autoResubscribe:i.config.autoResubscribe,path:i.config.serviceWorker.path,serviceWorkerPath:i.config.serviceWorker.workerName,serviceWorkerParam:{scope:i.config.serviceWorker.registrationScope},subdomainName:i.config.siteInfo.proxyOrigin,promptOptions:this.getPromptOptionsForDashboardConfiguration(i),welcomeNotification:{disable:!i.config.welcomeNotification.enable,title:i.config.welcomeNotification.title,message:i.config.welcomeNotification.message,url:i.config.welcomeNotification.url},notifyButton:{enable:i.config.staticPrompts.bell.enabled,displayPredicate:i.config.staticPrompts.bell.hideWhenSubscribed?()=>!OneSignal.User.PushSubscription.optedIn:null,size:i.config.staticPrompts.bell.size,position:i.config.staticPrompts.bell.location,showCredit:!1,offset:{bottom:`${i.config.staticPrompts.bell.offset.bottom}px`,left:`${i.config.staticPrompts.bell.offset.left}px`,right:`${i.config.staticPrompts.bell.offset.right}px`},colors:{"circle.background":i.config.staticPrompts.bell.color.main,"circle.foreground":i.config.staticPrompts.bell.color.accent,"badge.background":"black","badge.foreground":"white","badge.bordercolor":"black","pulse.color":i.config.staticPrompts.bell.color.accent,"dialog.button.background.hovering":i.config.staticPrompts.bell.color.main,"dialog.button.background.active":i.config.staticPrompts.bell.color.main,"dialog.button.background":i.config.staticPrompts.bell.color.main,"dialog.button.foreground":"white"},text:{"tip.state.unsubscribed":i.config.staticPrompts.bell.tooltip.unsubscribed,"tip.state.subscribed":i.config.staticPrompts.bell.tooltip.subscribed,"tip.state.blocked":i.config.staticPrompts.bell.tooltip.blocked,"message.prenotify":i.config.staticPrompts.bell.tooltip.unsubscribed,"message.action.subscribing":i.config.staticPrompts.bell.message.subscribing,"message.action.subscribed":i.config.staticPrompts.bell.message.subscribing,"message.action.resubscribed":i.config.staticPrompts.bell.message.subscribing,"message.action.unsubscribed":i.config.staticPrompts.bell.message.unsubscribing,"dialog.main.title":i.config.staticPrompts.bell.dialog.main.title,"dialog.main.button.subscribe":i.config.staticPrompts.bell.dialog.main.subscribeButton,"dialog.main.button.unsubscribe":i.config.staticPrompts.bell.dialog.main.unsubscribeButton,"dialog.blocked.title":i.config.staticPrompts.bell.dialog.blocked.title,"dialog.blocked.message":i.config.staticPrompts.bell.dialog.blocked.message}},persistNotification:i.config.notificationBehavior?i.config.notificationBehavior.display.persist:void 0,webhooks:{cors:i.config.webhooks.corsEnable,"notification.willDisplay":i.config.webhooks.notificationDisplayedHook,"notification.clicked":i.config.webhooks.notificationClickedHook,"notification.dismissed":i.config.webhooks.notificationDismissedHook},notificationClickHandlerMatch:i.config.notificationBehavior?i.config.notificationBehavior.click.match:void 0,notificationClickHandlerAction:i.config.notificationBehavior?i.config.notificationBehavior.click.action:void 0,allowLocalhostAsSecureOrigin:i.config.setupBehavior?i.config.setupBehavior.allowLocalhostAsSecureOrigin:void 0,outcomes:{direct:i.config.outcomes.direct,indirect:{enabled:i.config.outcomes.indirect.enabled,influencedTimePeriodMin:i.config.outcomes.indirect.notification_attribution.minutes_since_displayed,influencedNotificationsLimit:i.config.outcomes.indirect.notification_attribution.limit},unattributed:i.config.outcomes.unattributed}};case C.JavaScript:{let n=Object.assign(Object.assign(Object.assign(Object.assign({},t),{promptOptions:this.injectDefaultsIntoPromptOptions(t.promptOptions,i.config.staticPrompts,t)}),{serviceWorkerParam:t.serviceWorkerParam?t.serviceWorkerParam:{scope:"/"},serviceWorkerPath:t.serviceWorkerPath?t.serviceWorkerPath:"OneSignalSDKWorker.js",path:t.path?t.path:"/"}),{outcomes:{direct:i.config.outcomes.direct,indirect:{enabled:i.config.outcomes.indirect.enabled,influencedTimePeriodMin:i.config.outcomes.indirect.notification_attribution.minutes_since_displayed,influencedNotificationsLimit:i.config.outcomes.indirect.notification_attribution.limit},unattributed:i.config.outcomes.unattributed}});return t.hasOwnProperty("autoResubscribe")?n.autoResubscribe=!!t.autoResubscribe:t.hasOwnProperty("autoRegister")?n.autoResubscribe=!!t.autoRegister:n.autoResubscribe=!!i.config.autoResubscribe,n}}}static hasUnsupportedSubdomainForConfigIntegrationKind(e,t,i){switch(this.getIntegrationCapabilities(e).configuration){case C.Dashboard:return i.config.siteInfo.proxyOriginEnabled;case C.JavaScript:return!!t.subdomainName}}}class ty{static toOSNotification(e){return{notificationId:e.custom.i,title:e.title,body:e.alert,additionalData:e.custom.a,launchURL:e.custom.u,confirmDelivery:"y"===e.custom.rr,icon:e.icon,image:e.image,actionButtons:this.convertButtons(e.o),topic:e.tag,badgeIcon:e.badge}}static convertButtons(e){return null==e?void 0:e.map(e=>({actionId:e.i,text:e.n,icon:e.p,launchURL:e.u}))}static isValid(e){var t;return"string"==typeof(null===(t=null==e?void 0:e.custom)||void 0===t?void 0:t.i)}}class t${send(t){return e(this,void 0,void 0,function*(){let e=yield ed.get("Options",`webhooks.${t.event}`);if(!e)return;let i=yield ed.get("Options","webhooks.cors"),n={method:"post",mode:"no-cors",body:JSON.stringify(t)};i&&(n.mode="cors",n.headers={"X-OneSignal-Event":t.event,"Content-Type":"application/json"}),ev.debug(`Executing ${t.event} webhook ${i?"with":"without"} CORS POST ${e}`,t),yield fetch(e,n)})}}class tS{constructor(e,t){this.event="notification.clicked";let i=e.notification;this.notificationId=i.notificationId,this.heading=i.title,this.content=i.body,this.additionalData=i.additionalData,this.actionId=e.result.actionId,this.url=e.result.url,this.subscriptionId=t}}class tw{constructor(e,t){this.event="notification.willDisplay",this.notificationId=e.notificationId,this.heading=e.title,this.content=e.body,this.additionalData=e.additionalData,this.url=e.launchURL,this.subscriptionId=t}}class tI{constructor(e,t){this.event="notification.dismissed",this.notificationId=e.notificationId,this.heading=e.title,this.content=e.body,this.additionalData=e.additionalData,this.url=e.launchURL,this.subscriptionId=t}}class tk{constructor(e=new t$){this.sender=e}click(t,i){return e(this,void 0,void 0,function*(){return yield this.sender.send(new tS(t,i))})}willDisplay(t,i){return e(this,void 0,void 0,function*(){return yield this.sender.send(new tw(t,i))})}dismiss(t,i){return e(this,void 0,void 0,function*(){return yield this.sender.send(new tI(t,i))})}}class tO{static toNative(e){return null==e?void 0:e.map(e=>({action:e.actionId,title:e.text,icon:e.icon}))}}class tP{static getPushSubscriptionIdByToken(t){return e(this,void 0,void 0,function*(){let e=yield ed.getAll(r.PushSubscriptions);for(let i of e)if(i.token===t)return i.id})}}class tx{static get VERSION(){return _.version()}static get environment(){return _}static get log(){return ev}static get database(){return ed}static get webhookNotificationEventSender(){return new tk}static getPushSubscriptionId(){return e(this,void 0,void 0,function*(){let e=yield self.registration.pushManager.getSubscription(),t=null==e?void 0:e.endpoint;if(t)return tP.getPushSubscriptionIdByToken(t)})}static get workerMessenger(){return self.workerMessenger||(self.workerMessenger=new L),self.workerMessenger}static run(){self.addEventListener("activate",tx.onServiceWorkerActivated),self.addEventListener("push",tx.onPushReceived),self.addEventListener("notificationclose",e=>e.waitUntil(tx.onNotificationClosed(e))),self.addEventListener("notificationclick",e=>e.waitUntil(tx.onNotificationClicked(e))),self.addEventListener("pushsubscriptionchange",e=>{e.waitUntil(tx.onPushSubscriptionChange(e))}),self.addEventListener("message",e=>{let t=e.data;if(!t||!t.command)return;let i=t.payload;switch(t.command){case E.SessionUpsert:ev.debug("[Service Worker] Received SessionUpsert",i),tx.debounceRefreshSession(e,i);break;case E.SessionDeactivate:ev.debug("[Service Worker] Received SessionDeactivate",i),tx.debounceRefreshSession(e,i);break;default:return}}),ev.debug("Setting up message listeners."),tx.workerMessenger.listen(),tx.setupMessageListeners()}static getAppId(){return e(this,void 0,void 0,function*(){if(self.location.search){let e=self.location.search.match(/appId=([0-9a-z-]+)&?/i);if(e&&e.length>1)return e[1]}let{appId:t}=yield ed.getAppConfig();return t})}static setupMessageListeners(){tx.workerMessenger.on(E.WorkerVersion,e=>{ev.debug("[Service Worker] Received worker version message."),tx.workerMessenger.broadcast(E.WorkerVersion,_.version())}),tx.workerMessenger.on(E.Subscribe,t=>e(this,void 0,void 0,function*(){ev.debug("[Service Worker] Received subscribe message.");let e=new td(t),i=yield e.subscriptionManager.registerSubscription((yield e.subscriptionManager.subscribe(0)));tx.workerMessenger.broadcast(E.Subscribe,i.serialize())})),tx.workerMessenger.on(E.SubscribeNew,t=>e(this,void 0,void 0,function*(){ev.debug("[Service Worker] Received subscribe new message.");let e=new td(t),i=yield e.subscriptionManager.registerSubscription((yield e.subscriptionManager.subscribe(1)));tx.workerMessenger.broadcast(E.SubscribeNew,i.serialize())})),tx.workerMessenger.on(E.AmpSubscriptionState,t=>e(this,void 0,void 0,function*(){ev.debug("[Service Worker] Received AMP subscription state message.");let e=yield self.registration.pushManager.getSubscription();if(e){let t=yield self.registration.pushManager.permissionState(e.options),{optedOut:i}=yield ed.getSubscription();yield tx.workerMessenger.broadcast(E.AmpSubscriptionState,!!e&&"granted"===t&&!0!==i)}else yield tx.workerMessenger.broadcast(E.AmpSubscriptionState,!1)})),tx.workerMessenger.on(E.AmpSubscribe,()=>e(this,void 0,void 0,function*(){ev.debug("[Service Worker] Received AMP subscribe message.");let e=yield tx.getAppId(),t=yield tb.getAppConfig({appId:e},e_.downloadServerAppConfig),i=new td(t),n=yield i.subscriptionManager.registerSubscription((yield i.subscriptionManager.subscribe(0)));yield ed.put("Ids",{type:"appId",id:e}),tx.workerMessenger.broadcast(E.AmpSubscribe,n.deviceId)})),tx.workerMessenger.on(E.AmpUnsubscribe,()=>e(this,void 0,void 0,function*(){ev.debug("[Service Worker] Received AMP unsubscribe message.");let e;yield new td((yield tb.getAppConfig({appId:yield tx.getAppId()},e_.downloadServerAppConfig))).subscriptionManager.unsubscribe(1),tx.workerMessenger.broadcast(E.AmpUnsubscribe,null)})),tx.workerMessenger.on(E.AreYouVisibleResponse,t=>e(this,void 0,void 0,function*(){if(ev.debug("[Service Worker] Received response for AreYouVisible",t),!self.clientsStatus)return;let e=t.timestamp;self.clientsStatus.timestamp===e&&(self.clientsStatus.receivedResponsesCount++,t.focused&&(self.clientsStatus.hasAnyActiveSessions=!0))})),tx.workerMessenger.on(E.SetLogging,t=>e(this,void 0,void 0,function*(){t.shouldLog?self.shouldLog=!0:self.shouldLog=void 0}))}static onPushReceived(t){t.waitUntil(clients.matchAll({type:"window",includeUncontrolled:!0}).then(i=>{ev.debug(`Called onPushReceived(${JSON.stringify(t,null,4)}):`,t),t.waitUntil(tx.parseOrFetchNotifications(t).then(t=>e(this,void 0,void 0,function*(){let n=[],o=[],s=yield tx.getAppId();for(let r of t){ev.debug("Raw Notification from OneSignal:",r);let a=ty.toOSNotification(r);o.push(ed.putNotificationReceivedForOutcomes(s,a)),n.push((t=>e(this,void 0,void 0,function*(){yield tx.workerMessenger.broadcast(E.NotificationWillDisplay,{notification:t}).catch(e=>ev.error(e));let e=yield tx.getPushSubscriptionId();return tx.webhookNotificationEventSender.willDisplay(t,e),tx.displayNotification(t).then(()=>tx.sendConfirmedDelivery(t,i)).catch(e=>ev.error(e))})).bind(null,a))}return n.reduce((e,t)=>e.then(t),Promise.resolve())})).catch(e=>{ev.debug("Failed to display a notification:",e),tx.UNSUBSCRIBED_FROM_NOTIFICATIONS&&ev.debug("Because we have just unsubscribed from notifications, we will not show anything.")}))}))}static sendConfirmedDelivery(t,i){for(var n=0;n<i.length;n++)i[n].url.split("?")[0]==t.launchURL.split("?")[0]&&"visible"===i[n].visibilityState&&self.registration.getNotifications().then(e=>{e.forEach(e=>{setTimeout(()=>{e.close()},100)})});return e(this,void 0,void 0,function*(){if(!t)return;if(!tx.browserSupportsConfirmedDelivery())return null;if(!t.confirmDelivery)return;let e=yield tx.getAppId(),i=yield this.getPushSubscriptionId();if(!e||!t.notificationId)return;let n={player_id:i,app_id:e,device_type:ex.getDeviceType()};ev.debug(`Called sendConfirmedDelivery(${JSON.stringify(t,null,4)})`),yield eT(Math.floor(25*Math.random()*1e3)),yield eD.put(`notifications/${t.notificationId}/report_received`,n)})}static browserSupportsConfirmedDelivery(){return"safari"!==U().name}static getWindowClients(){return e(this,void 0,void 0,function*(){return yield self.clients.matchAll({type:"window",includeUncontrolled:!0})})}static updateSessionBasedOnHasActive(t,i,n){return e(this,void 0,void 0,function*(){if(i)yield eR.upsertSession(n.appId,n.onesignalId,n.subscriptionId,n.sessionThreshold,n.enableSessionDuration,n.sessionOrigin,n.outcomesConfig);else{let e=yield eR.deactivateSession(n.appId,n.onesignalId,n.subscriptionId,n.sessionThreshold,n.enableSessionDuration,n.outcomesConfig);e&&(self.cancel=e.cancel,t.waitUntil(e.promise))}})}static refreshSession(t,i){return e(this,void 0,void 0,function*(){ev.debug("[Service Worker] refreshSession");let e=yield this.getWindowClients();if(i.isSafari)yield tx.checkIfAnyClientsFocusedAndUpdateSession(t,e,i);else{let n=e.some(e=>e.focused);ev.debug("[Service Worker] hasAnyActiveSessions",n),yield tx.updateSessionBasedOnHasActive(t,n,i)}})}static checkIfAnyClientsFocusedAndUpdateSession(t,i,n){return e(this,void 0,void 0,function*(){let o=(new Date).getTime();self.clientsStatus={timestamp:o,sentRequestsCount:0,receivedResponsesCount:0,hasAnyActiveSessions:!1};let s={timestamp:o};i.forEach(e=>{self.clientsStatus&&self.clientsStatus.sentRequestsCount++,e.postMessage({command:E.AreYouVisible,payload:s})});let r=eb(()=>e(this,void 0,void 0,function*(){self.clientsStatus&&self.clientsStatus.timestamp===o&&(ev.debug("updateSessionBasedOnHasActive",self.clientsStatus),yield tx.updateSessionBasedOnHasActive(t,self.clientsStatus.hasAnyActiveSessions,n),self.clientsStatus=void 0)}),.5);self.cancel=r.cancel,t.waitUntil(r.promise)})}static debounceRefreshSession(t,i){ev.debug("[Service Worker] debounceRefreshSession",i),self.cancel&&(self.cancel(),self.cancel=void 0);let n=eb(()=>e(this,void 0,void 0,function*(){yield tx.refreshSession(t,i)}),1);self.cancel=n.cancel,t.waitUntil(n.promise)}static ensureImageResourceHttps(e){if(!e)return null;try{let t=new URL(e);if("localhost"===t.hostname||-1!==t.hostname.indexOf("192.168")||"127.0.0.1"===t.hostname||"https:"===t.protocol)return e;if("i0.wp.com"===t.hostname||"i1.wp.com"===t.hostname||"i2.wp.com"===t.hostname||"i3.wp.com"===t.hostname)return`https://${t.hostname}${t.pathname}`;return`https://i0.wp.com/${t.host+t.pathname}`}catch(i){ev.error("ensureImageResourceHttps: ",i)}}static ensureNotificationResourcesHttps(e){if(e&&(e.icon&&(e.icon=tx.ensureImageResourceHttps(e.icon)),e.image&&(e.image=tx.ensureImageResourceHttps(e.image)),e.actionButtons&&e.actionButtons.length>0))for(let t of e.actionButtons)t.icon&&(t.icon=tx.ensureImageResourceHttps(t.icon))}static displayNotification(t){return e(this,void 0,void 0,function*(){ev.debug(`Called displayNotification(${JSON.stringify(t,null,4)}):`,t);let e=yield tx._getTitle(),i=yield ed.get("Options","defaultIcon"),n=yield ed.get("Options","persistNotification"),o=yield tx.getAppId();t.title=t.title?t.title:e,t.icon=t.icon?t.icon:i||void 0,tx.ensureNotificationResourcesHttps(t);let s={body:t.body,icon:t.icon,image:t.image,data:t,actions:tO.toNative(t.actionButtons),tag:t.topic||o,requireInteraction:!1!==n,renotify:!0,badge:t.badgeIcon};return self.registration.showNotification(t.title,s)})}static shouldOpenNotificationUrl(e){return"javascript:void(0);"!==e&&"do_not_open"!==e&&!J.contains(e,"_osp=do_not_open")}static onNotificationClosed(t){return e(this,void 0,void 0,function*(){ev.debug(`Called onNotificationClosed(${JSON.stringify(t,null,4)}):`,t);let e=t.notification.data;tx.workerMessenger.broadcast(E.NotificationDismissed,e).catch(e=>ev.error(e));let i=yield tx.getPushSubscriptionId();tx.webhookNotificationEventSender.dismiss(e,i)})}static getNotificationUrlToOpen(t,i){return e(this,void 0,void 0,function*(){var e;if(i){let n=null===(e=null==t?void 0:t.actionButtons)||void 0===e?void 0:e.find(e=>e.actionId===i);if((null==n?void 0:n.launchURL)&&""!==n.launchURL)return n.launchURL}if(t.launchURL&&""!==t.launchURL)return t.launchURL;let{defaultNotificationUrl:o}=yield ed.getAppState();return o||location.origin})}static onNotificationClicked(t){return e(this,void 0,void 0,function*(){var i;ev.debug(`Called onNotificationClicked(${JSON.stringify(t,null,4)}):`,t),t.notification.close();let n=t.notification.data,o="exact",s="navigate",r=yield ed.get("Options","notificationClickHandlerMatch");r&&(o=r);let c=yield this.database.get("Options","notificationClickHandlerAction");c&&(s=c);let d=yield tx.getNotificationUrlToOpen(n,t.action),l=tx.shouldOpenNotificationUrl(d),u=yield tx.getAppId(),p=ex.getDeviceType(),g={notification:n,result:{actionId:t.action,url:d},timestamp:(new Date).getTime()};ev.info("NotificationClicked",g);let f=(i=g,e(this,void 0,void 0,function*(){try{let e=yield ed.getCurrentSession();if(e&&e.status===a.Active)return;yield ed.putNotificationClickedForOutcomes(u,i),e&&(e.notificationId=i.notification.notificationId,yield ed.upsertSession(e))}catch(t){ev.error("Failed to save clicked notification.",t)}})),h=yield this.getPushSubscriptionId(),v=tx.sendConvertedAPIRequests(u,h,g,p),m=yield tx.getWindowClients(),b=!1;for(let y of m){let $=y.url,S="";try{S=new URL($).origin}catch(w){ev.error("Failed to get the HTTP site's actual origin:",w)}let I=null;try{I=new URL(d).origin}catch(k){ev.error("Failed parse launchUrl:",k)}if("exact"===o&&$===d||"origin"===o&&S===I){if(y.url===d||"focus"===s&&S===I){tx.workerMessenger.unicast(E.NotificationClicked,g,y);try{y instanceof WindowClient&&(yield y.focus())}catch(O){ev.error("Failed to focus:",y,O)}}else if(y instanceof WindowClient&&y.navigate){try{ev.debug("Client is standard HTTPS site. Attempting to focus() client."),y instanceof WindowClient&&(yield y.focus())}catch(P){ev.error("Failed to focus:",y,P)}try{l?(ev.debug(`Redirecting HTTPS site to (${d}).`),yield ed.putNotificationClickedEventPendingUrlOpening(g),yield y.navigate(d)):ev.debug("Not navigating because link is special.")}catch(x){ev.error("Failed to navigate:",y,d,x)}}else yield ed.putNotificationClickedEventPendingUrlOpening(g),yield tx.openUrl(d);b=!0;break}}return l&&!b&&(yield ed.putNotificationClickedEventPendingUrlOpening(g),yield tx.openUrl(d)),f&&(yield f),yield v})}static sendConvertedAPIRequests(t,i,n,o){return e(this,void 0,void 0,function*(){let e=n.notification;if(!e.notificationId)return void console.error("No notification id, skipping networks calls to report open!");let s;t?s=eD.put(`notifications/${e.notificationId}`,{app_id:t,player_id:i,opened:!0,device_type:o}):console.error("No app Id, skipping OneSignal API call for notification open!"),yield tx.webhookNotificationEventSender.click(n,i),s&&(yield s)})}static openUrl(t){return e(this,void 0,void 0,function*(){ev.debug("Opening notification URL:",t);try{return yield self.clients.openWindow(t)}catch(e){return ev.warn(`Failed to open the URL '${t}':`,e),null}})}static onServiceWorkerActivated(e){ev.info(`OneSignal Service Worker activated (version ${_.version()})`),e.waitUntil(self.clients.claim())}static onPushSubscriptionChange(t){return e(this,void 0,void 0,function*(){ev.debug(`Called onPushSubscriptionChange(${JSON.stringify(t,null,4)}):`,t);let e=yield tx.getAppId();if(!e)return;let i=yield tb.getAppConfig({appId:e},e_.downloadServerAppConfig);if(!i)return;let n=new td(i),o,s;{let{deviceId:r}=yield ed.getSubscription();if(!(o=!!r)&&t.oldSubscription){r=yield e_.getUserIdFromSubscriptionIdentifier(e,ex.getDeviceType(),t.oldSubscription.endpoint);let a=yield ed.getSubscription();a.deviceId=r,yield ed.setSubscription(a)}o=!!r}let c=t.newSubscription;if(c)s=to.setFromW3cSubscription(c);else try{s=yield n.subscriptionManager.subscribe(1)}catch(d){}if(o||s){let l=null;"granted"!==Notification.permission?l=g.PermissionRevoked:s||(l=g.PushSubscriptionRevoked),yield n.subscriptionManager.registerSubscription(s,l)}else yield ed.remove("Ids","userId"),yield ed.remove("Ids","registrationId")})}static _getTitle(){return new Promise(e=>{Promise.all([ed.get("Options","defaultTitle"),ed.get("Options","pageTitle")]).then(([t,i])=>{e(null!==t?t:null!=i?i:"")})})}static parseOrFetchNotifications(e){if(!e||!e.data)return Promise.reject("Missing event.data on push payload!");if(tx.isValidPushPayload(e.data))return ev.debug("Received a valid encrypted push payload."),Promise.resolve([e.data.json()]);return Promise.reject(`Unexpected push message payload received: ${e.data}`)}static isValidPushPayload(e){try{let t=e.json();return!!ty.isValid(t)||(ev.debug("isValidPushPayload: Valid JSON but missing notification UUID:",t),!1)}catch(i){return ev.debug("isValidPushPayload: Parsing to JSON failed with:",i),!1}}}"undefined"==typeof self&&void 0!==i.g?i.g.OneSignalWorker=tx:self.OneSignalWorker=tx,"undefined"!=typeof self&&tx.run(),self.OneSignal=tx})()})();