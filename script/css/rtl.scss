
/*
* 
* ==========================================================
* RTL.SCSS
* ==========================================================
*
* Front-end RTL CSS. This file is imported only.
*
*/

.sb-rtl {
    direction: rtl;
    text-align: right;

    textarea, .sb-panel, ul, ul li {
        direction: rtl;
        text-align: right;
    }

    .sb-input.sb-input-btn > div {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
        margin-left: 0;
        margin-right: -3px;
    }

    .sb-input-select-input {
        > div {
            left: auto;
            right: 6px;
            padding-left: 0;
            padding-right: 5px;
        }
    }

    .sb-list .sb-input-select-input input {
        padding: 5px 60px 0 0;
    }

    .sb-btn-text i {
        margin: 0 0 0 15px;
    }

    .sb-search-btn {
        > i,
        > input {
            right: auto;
            left: 0;
        }

        > input {
            padding: 0 15px 0 50px !important;
        }

        &.sb-active > i {
            right: auto;
            left: 15px;
        }
    }

    div ul.sb-menu li,
    .sb-select ul li {
        padding: 6px 12px 6px 25px;
    }

    .sb-select p {
        padding: 0 0 0 20px;

        &:after {
            right: auto;
            left: 0;
        }
    }

    > div {
        .sb-header-agent {
            .sb-profile {
                text-align: right;

                img {
                    margin-right: 0;
                    margin-left: 15px;
                }

                .sb-status {
                    padding-right: 15px;
                    padding-left: 0;

                    &:before {
                        right: 0;
                        left: auto;
                    }
                }

                .sb-status-typing {
                    float: right;
                    padding-right: 0;

                    &:after {
                        right: calc(100% + 5px);
                        left: auto;
                    }
                }
            }

            &:hover .sb-profile {
                margin-right: 60px;
                margin-left: 0;
            }
        }

        .sb-header {
            .sb-dashboard-btn {
                right: -60px !important;
                left: auto;
            }

            &:hover .sb-dashboard-btn {
                right: 20px !important;
                left: auto;
            }
        }

        .sb-scroll-area .sb-header.sb-header-panel {
            text-align: right;
            padding: 5px 30px 5px 60px;

            .sb-dashboard-btn {
                left: 10px;
                right: auto !important;
            }
        }

        .sb-list {
            text-align: right;

            > .sb-right {
                float: left;
                margin: 10px 10px 30px 20px;

                &.sb-thumb-active {
                    margin: 10px 10px 30px 55px;
                }

                .sb-thumb {
                    left: -35px;
                    right: auto;
                }

                .sb-time {
                    left: 0;
                    right: auto;

                    i {
                        padding-left: 0;
                        padding-right: 10px;
                    }
                }
            }

            > div:not(.sb-right) {
                float: right;
                margin: 10px 20px 30px 10px;

                &.sb-thumb-active {
                    margin: 10px 55px 30px 10px;
                }

                .sb-thumb {
                    right: -35px;
                    left: auto;
                }
            }

            > div:first-child {
                margin-top: 20px;
            }

            .sb-time {
                right: 0;
                left: auto;
                flex-direction: row-reverse;

                i {
                    display: none;
                }
            }

            [data-id="sending"] .sb-time {
                padding-left: 20px;

                > i {
                    transform: translateX(-25px);
                }
            }
        }

        .sb-popup.sb-emoji {
            margin-left: -30px;

            &:after {
                left: 33px;
                right: auto;
            }
        }
    }

    .sb-editor {
        .sb-textarea {
            padding: 15px 15px 15px 120px;
        }

        &.sb-disabled-1 .sb-textarea {
            padding-left: 80px;
            padding-right: 15px;
        }

        &.sb-disabled-2 .sb-textarea {
            padding-left: 50px;
            padding-right: 15px;
        }

        &.sb-active {

            &.sb-disabled-2 .sb-textarea {
                padding-right: 15px;
            }

            .sb-textarea {
                padding: 15px 15px 15px 80px
            }
        }

        .sb-submit {
            padding-left: 0;
            padding-right: 13px;
        }

        .sb-loader {
            right: auto;
            left: 15px;
        }

        .sb-bar {
            padding: 15px 0 15px 15px;
            right: auto;
            left: 0;
        }

        .sb-bar-icons > div {

            &:before {
                left: 0px;
                right: 7px;
            }

            &:last-child {
                margin-right: 7px;
                margin-left: 0;
            }
        }

        .sb-attachments > div {
            margin: 5px 0 5px 5px;
            padding-right: 0;
            padding-left: 15px;

            i {
                left: 0px;
                right: 5px;
            }
        }
    }

    .sb-user-conversations > li > div {
        padding-left: 0;
        padding-right: 55px;
        text-align: right;

        img {
            left: auto;
            right: 0;
        }

        div:not(.sb-message) > span:first-child {
            margin: 0 0 0 15px;
        }
    }

    .sb-popup-message {
        .sb-icon-close {
            right: auto;
            left: 15px;
        }
    }

    .sb-articles {
        text-align: right;

        > div > div {
            text-align: right;
        }
    }

    .sb-rich-message {
        text-align: right;

        .sb-input > span {
            left: auto;
            right: 0;
            text-align: right;

            &.sb-active {
                right: 5px;
            }
        }

        .sb-rating .sb-submit + div {
            margin: 0 30px 0 0;
        }

        .sb-text-list-single > div {
            padding-left: 0;
            padding-right: 15px;

            &:before {
                left: auto;
                right: 0;
            }
        }

        .sb-slider {
            > div {
                direction: ltr;
            }

            .sb-card-img + div + .sb-card-extra {
                left: auto;
                right: 15px;
            }
        }



        &.sb-rich-select p:after {
            left: 8px;
        }
    }

    .sb-timetable > span {
        padding: 0 20px 0 0;

        i {
            left: auto;
            right: 0;
        }
    }

    .sb-list table th, .sb-list table td, .sb-init-form .sb-text, .sb-privacy .sb-title, .sb-privacy .sb-buttons {
        text-align: right;
    }

    .sb-lightbox-overlay i {
        left: 10px;
        right: auto;
    }

    .sb-rating > div {
        padding: 0 10px 0 0;
    }

    &.sb-chat {

        .sb-responsive-close-btn {
            left: 0;
            right: auto;
            text-align: left;

            &:before {
                left: 10px;
                right: auto;
            }
        }
    }

    .sb-article-category-links > span {
        margin-right: 0;
        margin-left: 20px;

        & + span:before {
            left: auto;
            right: -10px;
        }
    }

    .sb-label-date-top {
        left: 5px;
        right: 0;
    }

    .sb-select-phone img {
        margin: 0 0 0 10px;
    }

    .sb-select-search input {
        margin: 0 !important;
        padding: 0 10px !important;
    }

    .sb-input-select-input > div.sb-select-phone + input {
        padding: 5px 100px 0 0;
    }

    .sb-close-chat {
        right: auto;
        left: 20px;
    }

    #sb-audio-clip {
        right: 0;
        left: 40px;

        .sb-btn-clip-player.sb-active {
            margin-right: 0;
            margin-left: -15px;
        }
    }

    .sb-departments-list > div span, .sb-agents-list > div span, .sb-channels-list > div span {
        padding: 0 15px 0 0;
    }
}
