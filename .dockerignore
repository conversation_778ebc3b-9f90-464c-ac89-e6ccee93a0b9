# Docker ignore file for Support Board SaaS

# Git
.git
.gitignore
.gitattributes

# Docker files
Dockerfile
docker-compose.yml
.dockerignore

# Environment files
.env
.env.example
.env.local

# Documentation
README.md
*.md
installation-saas.pdf

# Logs
*.log
logs/
docker/logs/

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Node modules (if any)
node_modules/
npm-debug.log

# Backup files
*.backup
*.bak
script/config.php.backup

# Cache directories
cache/
tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
