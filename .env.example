# Support Board SaaS - Local Development Environment
# Copy this file to .env and modify as needed

# Application Settings
APP_NAME="Support Board Local"
APP_URL="http://localhost:8080"
APP_DEBUG=true

# Database Configuration
DB_HOST=db
DB_NAME=supportboard_cloud
DB_USER=supportboard
DB_PASSWORD=supportboard123
DB_ROOT_PASSWORD=root123

# Admin User (Default login)
ADMIN_EMAIL=admin@localhost
ADMIN_PASSWORD=password

# Docker Ports
WEB_PORT=8080
DB_PORT=3307
PHPMYADMIN_PORT=8081

# AMWAL Payment Gateway (UAT/Test)
AMWAL_MID=190621
AMWAL_TID=513651
AMWAL_SECURE_HASH_KEY=AA988491A63BEFD5368D41A5BA26D745BC73E24B0F67FC38F8A1FDAA589B4E72
AMWAL_CURRENCY_ID=512
AMWAL_TEST_MODE=true

# Email Configuration (Optional - for testing)
SMTP_HOST=localhost
SMTP_USERNAME=test@localhost
SMTP_PASSWORD=password
SMTP_PORT=587

# External Services (Optional)
PUSHER_ID=0000
PUSHER_KEY=local_key
PUSHER_SECRET=local_secret
PUSHER_CLUSTER=mt1

# Development Settings
DEBUG_MODE=true
LOG_ERRORS=true
MAINTENANCE_MODE=false
