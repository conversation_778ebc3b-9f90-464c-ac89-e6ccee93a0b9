# Support Board SaaS - Local Development Makefile

.PHONY: help setup start stop restart logs clean build shell db-shell

# Default target
help:
	@echo "Support Board SaaS - Local Development Commands"
	@echo ""
	@echo "Available commands:"
	@echo "  setup     - Initial setup (build and start containers)"
	@echo "  start     - Start all containers"
	@echo "  stop      - Stop all containers"
	@echo "  restart   - Restart all containers"
	@echo "  logs      - View container logs"
	@echo "  build     - Rebuild containers"
	@echo "  shell     - Access web container shell"
	@echo "  db-shell  - Access database shell"
	@echo "  clean     - Clean up containers and volumes"
	@echo "  status    - Show container status"
	@echo ""
	@echo "URLs:"
	@echo "  Application: http://localhost:8080"
	@echo "  phpMyAdmin:  http://localhost:8081"

# Initial setup
setup:
	@echo "🚀 Setting up Support Board SaaS..."
	@./docker/setup.sh

# Start containers
start:
	@echo "▶️ Starting containers..."
	@docker-compose up -d

# Stop containers
stop:
	@echo "⏹️ Stopping containers..."
	@docker-compose down

# Restart containers
restart:
	@echo "🔄 Restarting containers..."
	@docker-compose restart

# View logs
logs:
	@echo "📋 Viewing logs..."
	@docker-compose logs -f

# Rebuild containers
build:
	@echo "🔨 Rebuilding containers..."
	@docker-compose down
	@docker-compose up -d --build

# Access web container shell
shell:
	@echo "🐚 Accessing web container shell..."
	@docker exec -it supportboard_web bash

# Access database shell
db-shell:
	@echo "🗄️ Accessing database shell..."
	@docker exec -it supportboard_db mysql -u root -p

# Clean up everything
clean:
	@echo "🧹 Cleaning up containers and volumes..."
	@docker-compose down -v
	@docker system prune -f

# Show container status
status:
	@echo "📊 Container status:"
	@docker-compose ps
