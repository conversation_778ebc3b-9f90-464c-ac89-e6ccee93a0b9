version: '3.8'

services:
  # PHP-Apache Web Server
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: supportboard_web
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
    depends_on:
      - db
    environment:
      - DB_HOST=db
      - DB_NAME=supportboard_cloud
      - DB_USER=supportboard
      - DB_PASSWORD=supportboard123
    networks:
      - supportboard_network

  # MySQL Database
  db:
    image: mysql:8.0
    container_name: supportboard_db
    restart: always
    environment:
      MYSQL_DATABASE: supportboard_cloud
      MYSQL_USER: supportboard
      MYSQL_PASSWORD: supportboard123
      MYSQL_ROOT_PASSWORD: root123
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./sb_cloud.sql:/docker-entrypoint-initdb.d/sb_cloud.sql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - supportboard_network

  # phpMyAdmin for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: supportboard_phpmyadmin
    restart: always
    ports:
      - "8081:80"
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root123
      MYSQL_ROOT_PASSWORD: root123
    depends_on:
      - db
    networks:
      - supportboard_network

volumes:
  db_data:

networks:
  supportboard_network:
    driver: bridge
